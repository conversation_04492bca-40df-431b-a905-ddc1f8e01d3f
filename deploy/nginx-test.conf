#
# The default server
#

#daemon off;
user root;
worker_processes 4;
#include worker_cpu_affinity.conf;

error_log /workspace/logs/error.log;

#pid        /workspace/nginx.pid;

events {
     use epoll;
     worker_connections 60000;
}

http {
     include mime.types;
     default_type application/octet-stream;
     charset utf-8;

     client_header_buffer_size 4k;
     large_client_header_buffers 4 32k;
     client_max_body_size 20G;
     # client_body_buffer_size 1024k;

     open_file_cache max=5000 inactive=60;
     #enable response code 40x;
     fastcgi_intercept_errors on;

     log_format main
          '[INFO] $remote_addr - $remote_user [$time_local]  '
          '"$request" $status $request_length $request_time $body_bytes_sent '
          '"$http_referer" "$http_user_agent" $server_addr $upstream_addr $host $upstream_cache_status ';

     access_log /workspace/logs/access.log main;

     log_format error
          '[ERROR] $remote_addr - $remote_user [$time_local]  '
          '"$request" $status $request_length $request_time $body_bytes_sent '
          '"$http_referer" "$http_user_agent" $server_addr $upstream_addr $host $upstream_cache_status ';

     error_log /workspace/logs/error.log error;

     sendfile on;
     tcp_nopush on;

     #keepalive_timeout  0;
     keepalive_timeout 1200;
     reset_timedout_connection on;

     #gzip  on;
     #our client does not support gzip
     gzip on;
     gzip_min_length 500k;
     gzip_buffers 4 16k;
     gzip_http_version 1.1;
     gzip_comp_level 8;
     gzip_types application/javascript;
     gzip_vary off;
     etag on;

     #shorten the timeout period, original one is 300
     fastcgi_connect_timeout 30;
     fastcgi_send_timeout 30;
     fastcgi_read_timeout 30;
     fastcgi_buffer_size 128k;
     fastcgi_buffers 8 128k;
     fastcgi_busy_buffers_size 256k;
     fastcgi_temp_file_write_size 256k;
     fastcgi_hide_header Pragma;
     # fastcgi cache
     fastcgi_cache_path fastcgi_cache
          levels=1:2
          keys_zone=cache_voice:128m
          inactive=30m
          max_size=4G;

     # set_real_ip_from   ************/24;
     set_real_ip_from 10.0.0.0/8;
     # set_real_ip_from   ***************/25;
     real_ip_header X-Real-IP;


     #web server
     server {
          listen 8080;
          server_name _;
          # root         /workspace/web/;

          # Load configuration files for the default server block.
          include /etc/nginx/default.d/*.conf;

          if ($host ~ '^cloud\.intra\.xiaojukeji\.com') {
               rewrite '' https://cooper.didichuxing.com redirect;
               break;
          }
          # web端打开
          set $client '0';

          set $header_set 'no-cache';

          if ($http_user_agent ~* '(D\-Chat)') {
               set $header_set 'no-store';
          }
          # 非DC移动端打开
          if ($http_user_agent ~ '(MIDP)|(WAP)|(UP.Browser)|(Smartphone)|(Obigo)|(AU.Browser)|(wxd.Mms)|(WxdB.Browser)|(CLDC)|(UP.Link)|(KM.Browser)|(UCWEB)|(SEMC\-Browser)|(Mini)|(Symbian)|(Palm)|(Nokia)|(Panasonic)|(MOT\-)|(SonyEricsson)|(NEC\-)|(Alcatel)|(Ericsson)|(BENQ)|(BenQ)|(Amoisonic)|(Amoi\-)|(Capitel)|(PHILIPS)|(SAMSUNG)|(Lenovo)|(Mitsu)|(Motorola)|(SHARP)|(WAPPER)|(LG\-)|(LG/)|(EG900)|(CECT)|(Compal)|(kejian)|(Bird)|(BIRD)|(G900/V1.0)|(Arima)|(CTL)|(TDG)|(Daxian)|(DAXIAN)|(DBTEL)|(Eastcom)|(EASTCOM)|(PANTECH)|(Dopod)|(Haier)|(HAIER)|(KONKA)|(KEJIAN)|(LENOVO)|(Soutec)|(SOUTEC)|(SAGEM)|(SEC\-)|(SED\-)|(EMOL\-)|(INNO55)|(ZTE)|(iPhone)|(Android)|(Windows CE)|(Wget)|(Java)|(curl)|(Opera)') {
               set $client '1';
          }

          # DC移动端打开
          if ($http_user_agent ~ '(zhushou)') {
               set $client '2';
          }

          # ipad dc 打开
          # if ($http_user_agent ~ '(iPad)') {
          #      set $client '3';
          # }

          # 知识库不跳转
          if ($uri ~ '(^/knowledge)|(^/select-file-mobile)') {
               set $client '0';
          }

          if ($uri ~ '(^/select-file-mobile)') {
               set $client '0';
          }

          # 小米pad单独处理，识别成pc端
          if ($http_user_agent ~* '(XiaoMi.*Device/)|(Device/.*XiaoMi)|(MI PAD)|(Xiaomi Pad)') {
               set $client '0';
          }

          # 云空间有无对应功能标记
          set $cloud '0';
          if ($uri ~ '(^/shares)|(^/teams/invite)|(^/team-file)|(^/upload)') {
               set $client '${client}1';
               # 标记云空间有对应功能标记
               set $cloud '1';
          }
          # if ($uri ~ '(^/team-file)') {
          #      set $client '${client}2';
          #      # 标记云空间有对应功能标记
          #      set $cloud '1';
          # }
          # 如果未设置过云空间有无对应功能标记
          if ($cloud = '0') {
               # 标记云空间无对应功能
               set $client '${client}0';
          }

          # 未设置cooper移动端有无对应功能标记
          set $cooper '0';
          if ($uri ~ '(^/$)|(^/recent)|(^/disk)|(^/files)|(^/team-folder)|(^/fromme)|(^/tome)|(^/favorite)') {
               # 标记cooper移动端有对应功能
               set $client '${client}1';
               # 标记设置过cooper移动端有对应功能标记
               set $cooper '1';
          }
          # 如果未设置过cooper移动端有对应功能标记
          if ($cooper = '0') {
               # 标记cooper移动端无对应功能
               set $client '${client}0';
          }

          if ($uri ~ '(^/note)|(^/index.html)') {
               # 氢笔记有对应功能
               set $client '4';
          }

          if ($uri ~ '(^/(js|css))|(^(?!/api/).*\.(js|css|jpg|png|jpeg|less|wasm|sass|ttf|eot|woff|woff2|json|map)$)') {
               # 氢笔记有对应功能
               set $client '5';
          }

          # DC移动端打开 && 云空间无对应功能 && Cooper移动端有 -- 打开cooper移动端（0121前）
          if ($client = '201') {
               rewrite '^/(.*)$' https://cooper-test.didichuxing.com/m/$1 redirect;
               break;
          }

          # DC移动端打开 && 云空间无对应功能 && Cooper移动端无 -- 打开云空间首页
          if ($client = '200') {
               rewrite '^/(.*)$' https://cooper-test.didichuxing.com/m/c/ redirect;
               break;
          }

          # DC移动端打开 && 云空间无对应功能 && Cooper移动端有 -- 打开cooper移动端（0121前）
          # if ($client = '301') {
          #      rewrite '^/(.*)$' https://cooper-test.didichuxing.com/m/$1 redirect;
          #      break;
          # }

          # DC移动端打开 && 云空间无对应功能 && Cooper移动端无 -- 打开云空间首页
          # if ($client = '300') {
          #      rewrite '^/(.*)$' https://cooper-test.didichuxing.com/m/c/ redirect;
          #      break;
          # }

          # DC移动端打开 && 云空间有对应功能 -- 打开云空间
          if ($client ~ '^21') {
               rewrite '^/(.*)$' https://cooper-test.didichuxing.com/m/c/$1 redirect;
               break;
          }
          # DC移动端打开 && 云空间有对应功能(特指团队空间) -- 打开云空间
          # if ($client ~ '^22') {
          #      rewrite '^/(.*)$' https://cooper-test.didichuxing.com/m/c/$1 redirect;
          #      break;
          # }
          # DC移动端打开 && 云空间有对应功能 -- 打开云空间
          # if ($client ~ '^31') {
          #      rewrite '^/(.*)$' https://cooper-test.didichuxing.com/m/c/$1 redirect;
          #      break;
          # }
          # ipad端打开 && 打开PC端
          # if ($client ~ '^32') {
          #      break;
          # }

          # 非DC移动端打开 && 云空间无对应功能 && Cooper移动端有 -- 打开cooper移动端（0121前）
          if ($client = '101') {
               rewrite '^/(.*)$' https://cooper-test.didichuxing.com/m/$1 redirect;
               break;
          }

          # 非DC移动端打开 && 云空间无对应功能 && Cooper移动端无 -- 打开cooper移动端首页（0121前）
          if ($client = '100') {
               rewrite '^/(.*)$' https://cooper-test.didichuxing.com/m/recent redirect;
               break;
          }

          # 非DC移动端打开 && 云空间有对应功能 -- 打开云空间
          if ($client = '110') {
               rewrite '^/(.*)$' https://cooper-test.didichuxing.com/m/c/$1 redirect;
               break;
          }

          location ~ "(^/(js|css))|(^(?!/api/).*\.(wasm|js|css|jpg|png|jpeg|less|sass|ttf|eot|woff|woff2|json|map)$)" {
               root /home/<USER>/workspace/target/test/;
               add_header Cache-Control max-age=2592000;
               add_header Access-Control-Allow-Origin '*';
               add_header Access-Control-Allow-Headers '*';
               add_header Access-Control-Allow-Private-Network 'true';
          }

          location ^~ /knowledge {
               root /home/<USER>/workspace/target/test/; # 显示的根索引目录
               try_files $uri $uri/ /knowledge.html;

               etag on;
               if_modified_since exact;
               add_header Cache-Control $header_set;
          }

          location / {
               root /home/<USER>/workspace/target/test/; # 显示的根索引目录
               try_files $uri $uri/ /index.html;

               etag on;
               if_modified_since exact;
               add_header Cache-Control $header_set;
          }


          location ~ ^/(cooper_gateway|cooper_server|platform) {
               proxy_set_header Host cooper-test.didichuxing.com;

               # 测试麒麟 ip
               proxy_pass http://************;
          }

          location /jotting {
               proxy_pass https://************/cooper_gateway/jotting;
               # proxy_http_version 1.1;
               # proxy_set_header Upgrade $http_upgrade;
               # proxy_set_header Connection "upgrade";
               proxy_set_header Host "cooper-test.didichuxing.com";
               # proxy_read_timeout 600;
               # add_header Cache-Control no-store;
          }
     }
}