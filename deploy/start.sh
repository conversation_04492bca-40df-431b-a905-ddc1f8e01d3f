#!/bin/bash
source /etc/profile

cd $WORKSPACE/source

echo "================= 当前环境 =========================="
echo $env

if [[ $env = "test" ]]||[[ $env = "prod" ]]||[[ $env = "qa" ]] ; then     #如果是linux的话打印linux字符串
cp -fr deploy/nginx-${env}.conf /etc/nginx/nginx.conf

touch $WORKSPACE/target/${env}/knowledge.html
touch $WORKSPACE/target/${env}/index.html

else 
echo "nothing to do" 
fi     #ifend
cp -fr deploy/mime.types /etc/nginx/mime.types
nginx -g 'daemon off;'
