const path = require('path');
const fs = require('fs');

const appDirectory = fs.realpathSync(process.cwd());
const resolveApp = (relativePath) => path.resolve(appDirectory, relativePath);
const moduleFileExtensions = ['js', 'json', 'jsx', 'tsx'];

// Resolve file paths in the same order as webpack
const resolveModule = (resolveFn, filePath) => {
  const extension = moduleFileExtensions.find((extension) => fs.existsSync(resolveFn(`${filePath}.${extension}`)));

  if (extension) {
    return resolveFn(`${filePath}.${extension}`);
  }

  return resolveFn(`${filePath}.js`);
};

// config after eject: we're in ./config/
module.exports = {
  appPath: resolveApp('.'),
  appBuild: resolveApp('dist'),
  appPublic: resolveApp('public'),
  cooperAppHtml: resolveApp('public/index.html'),
  knowledgeHtml: resolveApp('public/knowledge.html'),
  blankJs: resolveModule(resolveApp, 'src/blank'),
  cooperAppIndexJs: resolveModule(resolveApp, 'src/cooper'),
  knowledgeAppIndexJs: resolveModule(resolveApp, 'src/knowledge'),
  appPackageJson: resolveApp('package.json'),
  appSrc: resolveApp('src'),
  appCommon: resolveApp('src/common'),
  appCommonCss: resolveApp('src/assets/style/global/*.less'),
  appJsConfig: resolveApp('jsconfig.json'),
  yarnLockFile: resolveApp('yarn.lock'),
  appNodeModules: resolveApp('node_modules'),
  codeWorker: resolveApp('src/utils/code'),
  publicJs: resolveApp('dist/static/js'),
};
