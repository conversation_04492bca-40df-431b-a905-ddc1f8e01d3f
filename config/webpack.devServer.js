const paths = require('./paths');
const ignoredFiles = require('react-dev-utils/ignoredFiles');


module.exports = {
  disableHostCheck: true,
  compress: true,
  clientLogLevel: 'none',
  contentBase: paths.appBuild,
  watchContentBase: true,
  hot: true,
  // 使用 ws 服务器
  transportMode: 'ws',
  injectClient: false,
  publicPath: '/',
  quiet: true,
  // 忽略文件变动目录
  // watchOptions: {
  //   ignored: ignoredFiles(paths.appSrc),
  // },
  host: '0.0.0.0',
  overlay: false,
  historyApiFallback: true,
  // 接口代理配置
  // `proxy` is run between `before` and `after` `webpack-dev-server` hooks
  proxy: [{
    context: ['/cooper_gateway', '/platform/stream', '/v1/resource', '/didocapi', '/jotting', 'cooper_server'],
    target: process.env.APP_ENV === 'qa' ? 'https://cooper-qa.didichuxing.com' : 'https://cooper-test.didichuxing.com',
    changeOrigin: true,
  }, {
    context: ['/collab'],
    target: 'https://didoc-qa.didichuxing.com',
    changeOrigin: true,
    secure: false,
    cookieDomainRewrite: 'localhost',
  }, {
    context: ['/api'],
    target: 'https://didoc-qa.didichuxing.com',
    changeOrigin: true,
    secure: false,
    cookieDomainRewrite: 'localhost',
    pathRewrite: { '^/api/api/': '/api/' },
  }, {
    context: ['/collab/ws'],
    target: 'https://didoc-qa.didichuxing.com',
    changeOrigin: true,
    secure: false,
    cookieDomainRewrite: 'localhost',
    ws: true,
  }],
  headers: {
    'Access-Control-Allow-Origin': '*',
  },
  before(app, server) { },
  after(app) { },
};
