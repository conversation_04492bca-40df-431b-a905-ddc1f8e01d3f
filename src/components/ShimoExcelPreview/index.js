import { PageNotPublishIcon } from '@/assets/icon';
import ErrorTips from '@/components/ErrorTips';
import LayoutContext from '@/components/serviceComponents/Layout/layoutContext';
import { inPhone } from '@/utils';
import { fetchSignatureAndToken, initShimoExcel } from '@/utils/shimo-excel';
import { message } from 'antd';
import classNames from 'classnames/bind';
import { intl } from 'di18n-react';
import { useContext, useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import styles from './style.module.less';

const cx = classNames.bind(styles);

const ShimoExcel = (props) => {
  const { changeInitLoading, docInfo, isResizingAside } = props;
  const { knowledgeId, pageId } = useContext(LayoutContext);
  const containerRef = useRef(null);
  const [shimoSDK, setShimoSDK] = useState(null);
  const [versionMode, setVersionMode] = useState(false);
  const navigate = useNavigate();
  const isInPhone = inPhone();

  useEffect(() => {
    if (isInPhone) {
      changeInitLoading(false);
    }
  }, []);

  if (isInPhone) {
    return (
      <ErrorTips
        img={PageNotPublishIcon}
        title={
          <div className={cx('no-permission-tip')}>
            {intl.t('暂不支持移动端访问表格')}
          </div>
        }
      />
    );
  }

  const handleParamsChange = async (instance) => {
    const Editor = instance.getEditor();
    const sheetId = await Editor.getActiveSheetId();
    navigate(`${window.location.pathname}?sheetId=${sheetId}`)
  }

  const initEditor = async () => {
    changeInitLoading(true);
    const container = containerRef.current;
    const data = {
      fileId: docInfo.guid,
      signature: docInfo.accessToken,
      token: docInfo.uploadToken,
      id: pageId,
      container,
    };
    const shimoSDK = await initShimoExcel(data);
    const Editor = shimoSDK.getEditor();

    Editor.on('paramsChanged', handleParamsChange.bind(null, shimoSDK));
    container.addEventListener('keydown', Editor.print);
    if (isInPhone) {
      window.postMessage({
        type: 'initPreviewShimoExcelEnd',
      }, '*')
    } else {
      container.querySelector('iframe')?.contentWindow.postMessage({
        type: 'showVersion',
        version: Number(docInfo.latestVersion || 1),
      }, '*');
    }

    window.addEventListener('message', (e) => {
      const { data } = e;
      if (data?.type === 'initPreviewShimoExcelEnd') {
        setTimeout(() => {
          changeInitLoading(false);
        }, 1000)
      }
    });
    setShimoSDK(shimoSDK);
  };

  // 定时刷新token
  const refreshTimer = (wait = 60000) => {
    return setTimeout(async () => {
      console.log('*** refreshTimer');
      const { signature, token, ttl } = await fetchSignatureAndToken();
      shimoSDK.setCredentials({
        signature,
        // token
      });
      refreshTimer(Math.max(0, ttl - 1000 * 60 * 60));
    }, wait);
  };

  const showVersionList = () => {
    containerRef.current.querySelector('iframe')?.contentWindow.postMessage({
      type: 'showVersionList',
    }, '*');
    setVersionMode(true);
  };

  const closeVersionList = () => {
    containerRef.current.querySelector('iframe')?.contentWindow.postMessage({
      type: 'hideVersionList',
    }, '*');
    setVersionMode(false);
    shimoSDK.getEditor().hideHistory();
  }

  useEffect(() => {
    let timer = shimoSDK ? refreshTimer() : null;

    return () => {
      console.log('*** refreshTimer -- clear');
      clearTimeout(timer);
    };
  }, [shimoSDK]);

  useEffect(() => {
    initEditor()
      .catch((e) => {
        message.error('文档初始化失败');
        console.error('*** initShimo', e);
      })
      .finally(() => {
        // changeInitLoading(false);
        containerRef.current.querySelector('iframe')?.contentWindow.postMessage({
          type: 'inKnowledge',
        }, '*');
      });
  }, []);

  return (
    <div className={cx('shimo-excel')}>
      {/* <div className={cx('shimo-excel-version')}>
        <button onClick={showVersionList}>版本记录</button>
      </div> */}
      {
        isInPhone && <h2 className={cx('shimo-excel-title')}>{docInfo.pageName}</h2>
      }
      <div className={cx('shimo-excel-content', { 'version-list-mode': versionMode })}>
        {
          versionMode && <button
            className={cx('hide-excel-version')}
            onClick={closeVersionList}>关闭版本记录</button>
        }
        <div
          ref={containerRef}
          id='shimo-excel-container'
          className={cx('shimo-excel-container', { 'stop-event': isResizingAside })}/>
      </div>
    </div>
  );
};

function mapStateToProps({ pageDetail, asideDK }) {
  const { docInfo } = pageDetail;
  const { isResizingAside } = asideDK;
  return {
    docInfo,
    isResizingAside,
  };
}

function mapDispatchToProps({ pageDetail }) {
  const { changeInitLoading } = pageDetail;
  return {
    changeInitLoading,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(ShimoExcel);
