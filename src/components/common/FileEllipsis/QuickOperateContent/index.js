import { Tooltip } from 'antd';
import { intl } from 'di18n-react';
import { useState, useEffect } from 'react';
import starFile from '@/components/CooperOperation/StarFile';
import {
  MY_COOPER,
  TEAM_COOPER,
  SHARE_FROM_ME,
  SHARE_WITH_ME,
  STAR,
} from '@/constants/cooper';
import classBind from 'classnames/bind';
import styles from './style.module.less';
import { searchAddTeamMemberByTeamId } from '@/service/cooper/cooperFile';
import AddQuick from '@/components/CooperOperation/AddQuick';
import RemoveQuick from '@/components/CooperOperation/RemoveQuick';
import isDkPage from '@/components/OperateMenu';

const cx = classBind.bind(styles);

export default function QuickOperateContent({ resourceId, resourceType, record, doneCallback }) {
  const [isStared, setIsStared] = useState(false);
  const [isQuicked, setIsQuicked] = useState(false);

  // useEffect(() => {
  //   setIsStared(!(record.marked_star === false || record.marked_star === 0))
  // }, [record.marked_star])

  // 获取按钮状态
  useEffect(() => {
    const fetchStatus = async () => {
      try {
        const result = await searchAddTeamMemberByTeamId({
          resourceType,
          resourceId,
        });
        setIsStared(result.existStar || false);
        setIsQuicked(result.existQuick || false);
      } catch (error) {
        console.error('获取快速操作状态失败:', error);
      }
    };

    if (resourceId && resourceType) {
      fetchStatus();
    }
  }, [resourceId, resourceType]);


  // 收藏逻辑
  const handleStar = (e) => {
    e.stopPropagation();
    starFile([record], doneCallback);
  }

  function _addQuick(e) {
    const params = {
      objectType: record.resourceTypeStr || record.space_resource_type || record.objectType || record.resource_type,
      objectId: record.resourceId || record.id,
      sourceAppId: isDkPage() ? 4 : 2,
      sourceId:
        record.teamId
        || record.sourceId
        || record.parent_id
        || record.spaceId
        || record.space_id,
      shareId: record.shareId,
      shareType: record.shareType,
      shareLink: record.shareLink,
      doneCallback: (quickId) => {
        if (uniqueCallback.handelQuickAccess) {
          uniqueCallback.handelQuickAccess(quickId)
        } else {
          runCallbacks()
        }
        setFile((data) => ({
          ...data,
          quickId,
        }));
      },
    };
    if (record.base) {
      params.shareInfo = `${record.shareLink}?base=${record.base}`
    }
    AddQuick(params)
  }

  // 移除快速访问
  function _removeQuick(e) {
    e.stopPropagation();
    RemoveQuick({
      id: record.quickId || record.quick_id,
      doneCallback: () => {
        if (uniqueCallback.handelQuickAccess) {
          uniqueCallback.handelQuickAccess(null)
        } else {
          runCallbacks()
        }
        setFile((data) => ({
          ...data,
          quickId: null,
          marked_quick_visit: false,
        }));
      },
    })
  }

  // 快速访问逻辑
  const handleQuick = (e) => {
    e.stopPropagation();

    if (isQuicked) {
      RemoveQuick({
        id: quickObj.quickId,
        doneCallback: () => {},
      });
    } else {
      AddQuick({
        objectType: type,
        objectId: pageId,
        sourceAppId: 4,
        sourceId: knowledgeId,
        doneCallback: () => {},
      });
    }
  }

  const handleShare = (e) => {
    e.stopPropagation();
    // 分享逻辑
  }

  return (
    <div className={cx('quick-operate-wrap')}>
      <Tooltip title={isStared ? intl.t('取消收藏') : intl.t('收藏')} >
        <div
          className={cx('filename-side-star', 'quick-operate-icon')}
          onClick={(e) => handleStar(e)}
          data-e2e='collect'
      >
          {isStared ? <i
            className={
              cx('dk-iconfont', 'dk-icon-yishoucang1', 'icon-selected', 'icon-selected-star')
            }
        /> : <i
          className={
            cx('dk-iconfont', 'dk-icon-shoucang4px')
          }
        />}
        </div>
      </Tooltip>
      <Tooltip title={isStared ? intl.t('从“快速访问”移出') : intl.t('添加至快速访问')} >
        <div
          className={cx('filename-side-quicked', 'quick-operate-icon')}
          onClick={(e) => handleQuick(e)}
          data-e2e='collect'
      >
          {isQuicked ? <i
            className={
            cx('dk-iconfont', 'dk-icon-kuaisufangwenmian', 'icon-selected')
          }
        /> : <i
          className={
            cx('dk-iconfont', 'dk-icon-kuaisu4')
          }
        />}
        </div>
      </Tooltip>
      <Tooltip title={intl.t('分享')} >
        <div
          className={cx('filename-side-share', 'quick-operate-icon')}
          onClick={(e) => handleShare(e)}
          data-e2e='collect'
      >
          <i
            className={
            cx('dk-iconfont', 'dk-icon-fenxiang4px')
          }
          />
        </div>
      </Tooltip>
    </div>
  );
}

