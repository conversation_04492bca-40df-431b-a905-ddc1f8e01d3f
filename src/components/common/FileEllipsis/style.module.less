.file-ellipsis {
  padding-right: 4px;
  display: flex;
  justify-content: space-between;
  color: #2F343C;
  align-items: center;
  overflow: hidden;
  height: 22px;
  padding-left: 4px;
  width: 100%;
  cursor: pointer;

  .emphasis {
    color: red;
  }

  .department-src {
    width: 42px;
    margin-left: 10px;
  }

  .file-ellipsis-inner-span {
    white-space: nowrap;
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    padding: 0 2px;
    height: 22px;
    line-height: 22px;
    color: @blueGray-1;

    &:hover {
      // background-color: @blueGray-10;
      border-radius: 4px;
    }
  }

  &:hover {
    :global {
      .quick-operate-wrap {
        display: block !important;
      }
    }
  }



  .link-tag {
    font-family: PingFangSC-Medium;
    display: inline-block;
    background: rgba(4, 127, 254, 0.1);
    border-radius: 4px;
    text-align: center;
    padding: 0 8px;
    color: #047FFE;
    font-size: 22px;
    transform: scale(0.5);
    box-sizing: content-box;
    height: 36px;
    line-height: 36px;
    margin-left: -22px;
    margin-right: -26px;
  }
}

.not-click {
  cursor: default;
}