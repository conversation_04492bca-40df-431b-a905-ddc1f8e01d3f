.v3-quick-access {
  .widthAdaptation();
  margin-bottom: 8px;
  padding: 0 32px;
  &-title {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    font-weight: normal;
    color: #222A35;
    top: 0px;
    position: sticky;
    z-index: 1;
    background-color: #fff;
    height: 24px;
    background-color: #fff;
    display: block;
    margin-bottom: 0;
    padding: 0 0 12px 0;
    box-sizing: content-box;
  }
}
.is-dc {
  padding: 0 24px;
}
.container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
