import { intl } from 'di18n-react';
import { Component } from 'react';
import UserSuggest from '../../common/user-suggest';

class Search extends Component {
  render() {
    const { teamName, onSelect } = this.props;
    return (
      <div className='search'>
        <UserSuggest
          placeholder={intl.t('请输入用户或者组')}
          teamName={teamName}
          onSelect={onSelect}
        />
      </div>
    );
  }
}

export default Search;
