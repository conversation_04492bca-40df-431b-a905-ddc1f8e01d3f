
.direct-share {
  font-size: 14px;
  color: #17233e;

  .share-container {
    position: relative;
    min-height: 159px;

    .share-list {
      height: 100%;
      overflow: auto;
      padding-top: 10px;

      .share-list-item{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 0;
      }
    }

    .place-holder {
      height: 21px;
    }

    .avatar {
      width: 40px;
      border-radius: 20px;
      margin-right: 16px;
      vertical-align: middle;
    }

    .info {
      display: inline-block;
      vertical-align: middle;

      > div:first-child {
        max-width: 340px;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .mail {
        font-size: 12px;
        color: #8a93a8;
      }
    }

    .operation {
      float: right;
      margin-top: 10px;

      .ant-checkbox-wrapper {
        margin-right: 8px;
      }

      > span {
        padding-right: 10px;
        margin-right: 10px;
        border-right: 1px solid #bec5d2;
      }

      a {
        color: #17233e;

        &:hover,
        &:focus {
          color: #0066FF;
        }
      }
    }
  }

  .foot {
    .direct-add {
      padding: 0 32px;
    }

    .button-container {
      text-align: right;

      button {
        font-size: 14px;
        height: 32px;
        padding: 0 16px;

        &.two-word {
          padding: 0 20px;
        }

        &:first-child {
          border-color: #e4e9f3;
          color: #17233e;
        }

        &:first-child:hover {
          color: @black;
          border-color: @black;
        }

        &:last-child {
          background-color: @black;
          margin-left: 10px;
          border: 0;
          color: #fff;

          &:hover {
            background-color: @black;
          }
        }
      }
    }

    .warn {
      text-align: left;
      color: #999999;
      line-height: 18px;
      word-break: break-word;
    }
  }

}
