.ant-select-dropdown-menu-item {
  font-size: 14px;
  color: #17233e;
}

.cooper-share-modal {
  z-index: 2001 !important;
  .hidden {
    display: none;
  }
  .batch-add-title {
    line-height: 28px;
    display: flex;
    align-items: center;
    .back {
      cursor: pointer;
      margin-right: 16px;
      width: 24px;
      height: 24px;
      background: url('@/assets/icon/createKnowledgeBack.png') no-repeat;
      background-size: contain;
    }
  }


  .ant-modal-title > div {
    max-width: 450px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .ant-modal-content {
    min-height: 364px;
    border-radius: 12px;
    background-color: #fefefe;
    box-shadow: 0 2px 8px 0 rgba(23,35,62,0.20);
  }

  .ant-modal-close {
    top: 6px;
    right: 8px;
  }

  .ant-modal-close-x {
    font-size: 18px;
    font-weight: 100;
    color: #bec5d2;

    &:hover {
      color: #0066FF;
    }
  }

  .ant-modal-header {
    border-bottom: 0;
    padding: 32px 32px 16px;
    border-radius: 8px;

    .ant-modal-title {
      font-size: 20px;
      color: #17233e;

      span {
        margin-right: 12px;
      }
    }
  }

  .ant-modal-body {
    padding: 0;
    .category {
      padding: 1px 32px 8px;
      a {
        font-size: 15px;
        color: #999999;
        position: relative;

        &:first-child {
          margin-right: 40px;
        }

        &.active {
          color: #333333;
          &::after {
            content: ' ';
            display: block;
            position: absolute;
            width: 24px;
            height: 2px;
            background:#0066FF;
            left: 17px;
            bottom: -5px;
          }
        }
      }
    }

    .direct-share {
      padding: 15px 0 32px 0;
    }

    .addMember-tabs {
      .ant-tabs-nav-wrap {
        padding: 0 32px;
      }
    }

  }
  &.show-batch-add .ant-modal-body .direct-share{
    padding: 0;
  }

  .ant-radio-wrapper,
  .ant-checkbox-wrapper {
    position: relative;
    margin-right: 8px;
  }

  .ant-radio-inner {
    border-color: #bec5d2;
  }


  .ant-select-selection-selected-value {
    font-size: 14px;
    color: #17233e;
  }

  .ant-select-selection {
    margin-bottom: 0;
    border-color: #e4e9f3;
  }

  .ant-checkbox-inner {
    border-radius: 12px;
    border-color: #bdc5d2 !important;
  }

  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #0066FF !important;
    border-color: #0066FF !important;
  }

  .ant-checkbox-checked .ant-checkbox-inner::after {
    left: 2px;
    top: 2px;
    border: 0;
    height: 10px;
    width: 10px;
    background-image: url(./icon/icon_gouxuan.svg);
    background-size: 10px 10px;
    transform: rotate(0deg) scale(1);
  }

  .ant-select-selection:hover,
  .ant-select-selection:focus {
    border-color: #0066FF;
  }
}
.show-batch-add, .cooper-share-modal {
  .ant-modal {
    width: 640px !important;
  }
}