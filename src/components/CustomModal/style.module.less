
.custom-modal-content{
  display: flex;
  position: relative;
  justify-content: center;
  align-items: center;
  .custom-confirm-icon{
    width: 132px;
    height: 132px;
    margin-right: 24px;
    margin-left: 8px;
  }
  .custom-text{
    flex: 1;
    position: relative;
    top: 8px;
    .text-h1{
      font-size: 24px;
      line-height: 33px;
      margin-bottom: 10px;
      font-weight: 500;
      color: @text-color;
    }
    .text-p{
      font-size: 16px;
      line-height: 24px;
      color: #444B4F;
    }
  }
}

:global {
  .custom-confirm-container-viewDetails{
    .ant-btn:first-child {
      color: #444B4F;
      background: #F6F6F6;
      // margin-right: 129px !important;
    }
  }
.custom-confirm-container{
    .ant-modal-content{
      .ant-modal-confirm-btns{
        margin-top: 34px;
      }
    }
    .ant-btn {
      font-size: 16px;
      line-height: 22px;
      border-radius: 4px;
      border: none;
      height: auto;
      padding: 8px 28px;
    }
    .ant-btn:first-child {
    
      color: #444B4F;
      background: #F6F6F6;
      margin-right: 8px;
  }
    .ant-modal-confirm-btns{
      .ant-btn:last-child{
        color: #FFFFFF;
        font-weight: 500;
      }
    }
  }
}