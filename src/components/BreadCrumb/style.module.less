.bread-crumb {
  .bread-crumb-title {
    .breadcrumb-item-text {
      display: inline-block;
      .ellipsis();
      max-width: 200px;
      color: #656A72;
      font-size: 14px;
      margin: 0 4px;
      vertical-align: middle;

      &.is-last {
        font-weight: 500;
        color: #656A72;
      }

      &:hover {
        font-weight: 500;
        color: #656A72;
      }

      .bread-omit {
        cursor: pointer;
        font-size: 14px;
        vertical-align: text-top;
      }
    }
  }
}

.bread-crumb-container {
  :global {
    .ant-popover-inner-content {
      width: 360px;
      max-height: 452px;
      padding: 7px !important;
      margin: 0;
      overflow-y: auto;
    }
  }

  .pop-item-link {
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    padding: 0 12px;
    cursor: pointer;
    display: block;
    .ellipsis();

    .pop-name {
      color: #333333;
      font-size: 14px;
    }

    &:hover {
      background: #F3F3F3;

      a {
        color: #222A35;
      }
    }
  }

  // .pop-item {
  //   height: 32px;
  //   line-height: 32px;
  //   border-radius: 4px;
  //   padding: 0 12px;
  //   cursor: pointer;



  //   &:hover {
  //     background: #F3F3F3;

  //     a {
  //       color: #222A35;
  //     }
  //   }
  // }

}