import React from 'react';
import { connect } from 'react-redux';
import CooperPreview from '@/components/FilePreview';
import withRouter from '@/hooks/withRouter';
import { downloadFile, dealDownloadRes } from '@/utils/file';
import { get } from '@/utils/request/cooper';
import api from '@/utils/request/api/CooperApi';
import { Helmet } from 'react-helmet';

class CooperVideoPreview extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      file: {},
      showPreview: false,
    };
  }

  componentDidMount() {
    const { id, shareId } = this.props.params;

    let url = api.RESOURCE_META.replace(':resourceId', id);
    if (shareId !== '0') url += `?shareId=${shareId}`;

    get(url).then((res) => {
      this.setState({
        file: {
          id: Number(id),
          share_id: Number(shareId),
          display_name: res.filename,
          name: res.filename,
          permission: res.permission,
        },
      });
    });
  }

  downloadFile = (f) => {
    const { shareId, type } = this.props.params;
    const { connectSiteId } = this.props;
    const { teamId } = this.getIds();
    // file
    if (!type) {
      downloadFile([f], connectSiteId, teamId);
      return;
    }
    // share
    let url;
    if (type === 'link') {
      url = api.API_LINK_DOWNLOAD.replace(':linkId', shareId);
    } else if (type === 'direct') {
      url = api.API_DIRECT_DOWNLOAD.replace(':directId', shareId);
    }
    url += `?region=${connectSiteId}`;
    post(url, ids).then((res) => {
      dealDownloadRes(res);
    });
  }

  getIds = () => {
    const { pathname } = window.location;

    if (/^\/disk\/|^\/files\//.test(pathname)) {
      const m = pathname.match(/^\/files\/(\d+)$/);
      const parentId = m ? Number(m[1]) : 0;
      return {
        parentId,
        teamId: 0,
      };
    } // 团队空间

    if (/^\/team-file\//.test(pathname)) {
      // TODO: 需要兼容/team-file/xxx/home的逻辑吗？
      const m1 = pathname.match(/^\/team-file\/\d+\/(\d+)$/);
      const parentId = m1 ? Number(m1[1]) : 0;
      const m2 = pathname.match(/^\/team-file\/(\d+)/);
      const teamId = m2 ? Number(m2[1]) : 0;
      return {
        parentId,
        teamId,
      };
    } // 其他页面

    return {
      parentId: 0,
      teamId: 0,
    };
  };

  render() {
    const { file } = this.state;
    const { type } = this.props.params;

    return (
      <div className='cooper-recent'>
        <Helmet>
          <link
            rel="shortcut icon"
            type="image/png"
            href="//img-ys011.didistatic.com/static/cooper_cn/do1_IdgHKEBYk8gvDIAQMp8C" />
        </Helmet>
        {
          file && file.id
            ? <CooperPreview
                files={[file]}
                fidx={0}
                isHideCloseIcon={true}
                closePreview={() => {}}
                type={type}
            />
            : null
        }
      </div>
    );
  }
}

// export default CooperVideoPreview;
function mapStateToProps(state) {
  return {
    connectSiteId: state?.user?.connectSiteId || window.__connectSiteId || 0,
  };
}
// routes:  /videopreview/:type/:id/:shareId
export default withRouter(connect(mapStateToProps)(CooperVideoPreview));
