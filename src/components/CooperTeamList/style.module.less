.cooper-team-list {
  background: #fff;
  overflow: hidden;
  height: 100%;

  .ctl-content {
    background: #fff;
    // height: calc(100% - 75px);
    height: 100%;
  }

  .ctl-title {
    font-size: 28px;
    font-family: PingFangSC-Light, PingFang SC;
    font-weight: 300;
    color: #333333;
    padding: 0px 24px 16px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .cooper-subhead {
      flex: 1;
    }

    .ant-btn {
      color: #0066FF;
      border: 1px #0066FF solid;
      border-radius: 4px;
      height: 32px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      img {
        width: 16px;
        height: 16px;
      }

      span {
        font-size: 14px;
        color: #0066FF;
        margin-left: 2px;
        font-weight: 400;
      }

      ;

      img {
        width: 16px;
        height: 16px;
      }

      span {
        font-size: 14px;
        color: #0066FF;
      }
    }
  }

  .ctl-empty-tip {
    overflow: hidden;
    // margin-top: 128px;
    width: 100%;
    position: relative;
    top:30%;

    .ctl-et-img {
      margin: 0 auto;
      display: block;
      width: 180px;
    }

    .ctl-et-text {
      margin: 12px 0 0;
      padding: 0;
      height: 20px;
      font-size: 14px;
      line-height: 20px;
      text-align: center;
      color: @blueGray-4;
    }

    .ctl-et-link {
      color: #047FFE;
      cursor: pointer;
    }
  }
  .ctl-empty-tip-owner{
    margin: 48px 0 80px 0;
    .ctl-et-img{
      width: 140px;
    }
  }

  .ctl-box {
    width: 100%;
    padding: 0 32px;

    .ctl-box-trigger {
      position: relative;
      float: right;
      margin-right: 20px;
      padding-right: 16px;
      height: 20px;
      font-size: 14px;
      line-height: 20px;
      color: #0066FF;
      cursor: pointer;

      .ctl-box-trigger-icon {
        position: absolute;
        right: 0;
        top: 7px;
        width: 8px;
        transform: rotate(180deg);
      }

      &.open {
        .ctl-box-trigger-icon {
          top: 6px;
          transform: rotate(0);
        }
      }
    }

    .ctl-box-title {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      background-color: #fff;
      z-index: 1;
      width: 100%;
      height: 24px;
      font-size: 16px;
      line-height: 24px;
      color: #222A35;
      font-family: PingFangSC-Medium;
      margin-bottom: 16px;
    }

   

    .ctl-box-list {
      width: 100%;
      display: grid;
      gap: 16px;
      grid-template-columns: repeat(auto-fill, minmax(332px, 1fr));
      padding-bottom: 32px;
    }
  }

  .ctl-box-item {
    position: relative;
    display: inline-block;
    border-radius: 8px;
    padding: 16px;
    height: 92px;
    cursor: pointer;
    background: #F6F7F7;
    border: 1px solid #F6F7F7;

    &.ctl-box-item-add {
      float: left;
      padding-top: 48px;
      line-height: 12px;
      font-size: 12px;
      text-align: center;
      color: #bec5d2;
      background-color: rgba(230, 231, 237, .2);
      border-style: dashed;
      background-size: 20px 20px;
      background-repeat: no-repeat;
      background-position: center 17px;
      background-image: url('./icon/icon_jiahao.svg');

      &:hover {
        border-color: #0066FF;
        box-shadow: none;
      }
    }

    &:hover {
      border: 1px solid #1A6EFF;
      background: #FFFFFF;

      .ctl-bi-ding {
        display: flex;
        align-items: center;
        justify-content: center;

        :global(.dk-iconfont) {
          color: #222A35!important;
        }
      }
    }

    .f1 {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
    }

    .ctl-bi-ding {
      display: none;
      position: absolute;
      top: 16px;
      right: 16px;
      width: 24px;
      height: 24px;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background: #F2F3F3;
      }

      &.ctl-bi-dinged {
        :global(.dk-iconfont) {
          color: #BDC0C3;
        }
      }

      &.ctl-bi-dinged,
      &.ctl-bi-dinged:hover {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .ctl-bi-logo {
      margin-right: 8px;
      width: 24px;
      height: 24px;
    }

    .ctl-bi-logo-default {
      border-radius: 2px;
      margin-right: 8px;
      width: 24px;
      height: 24px;
      // background: url('./icon/icon_tuanduimorentouxiang.svg') no-repeat center;
      // background: url('https://img-ys011.didistatic.com/static/cooper_cn/spaceTeam.png') no-repeat center;
    }

    .ctl-bi-name {
      font-size: 14px;
      line-height: 20px;
      color: #333;
      display: flex;
      padding-right: 24px;
      font-family: PingFangSC-Medium;
      overflow: hidden;
      :global {
        .tag-global {
          margin-left: 0px;
          margin-right: 4px;
        }
      }
      .f1-text {
        display: inline-block;
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .department {
        background: #F7DA9C;
        color: #6D4800;
        font-size: 12px;
        border-radius: 2px;
        display: inline-block;
        height: 18px;
        line-height: 18px;
        font-weight: 700;
        padding: 0 4px;
        box-sizing: content-box;
        margin-right: 8px;
        transform: scale(.916);
        // width: 24px;
        &:last-child {
          margin-left: 8px;
          margin-right: 0;
        }
      }
    }

    .ctl-bi-info {
      margin-top: 16px;
      height: 20px;
      font-size: 12px;
      line-height: 20px;
      color: #6A707C;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      overflow: hidden;

      .quota-word {
        max-width: 60px;
      }

      .ctl-bi-info-status {
        cursor: pointer;
        color: #1A6EFF;
      }
    }
    
    .ctl-bi-info-out {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
      &-status-box {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .ctl-bi-info-out-status {
          display: flex;
          align-items: center;
          padding: 3px 8px;
          border-radius: 12px;
          align-content: center;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 12px;
          height: 24px;
          > span {
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .status-cion {
            font-size: 14px;
            margin-right: 4px;
          }
        }
        .ctl-bi-info-out-status-reject {
          color: #FA652F;
          background: rgba(250, 101, 47, 0.1);
        }
        .ctl-bi-info-out-status-applied {
          color: #1A6EFF;
          background: rgba(26, 110, 255, 0.102);
        }
      }
      &-action-box {
        display: flex;
        > span {
          font-size: 13px;
          cursor: pointer;
          padding: 4px 12px;
          background: #FFFFFF;
          margin: 0 3px;
          border-radius: 4px;
          color: @blueGray-2;
          &:hover {
            color: @blueGray-1;
          }
        }
      }
    }
    .ctl-bi-info-private,
    .ctl-bi-info-public {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      height: 14px;
      font-size: 12px;
      text-indent: 18px;
      padding-right: 8px;
      background-size: 14px 14px;
      background-repeat: no-repeat;
      background-position: 0 0;
      word-break: keep-all;
    }

    .ctl-bi-info-private {
      background-image: url('./icon/icon_simi.svg');
      background-size: 14px 14px;
    }

    .ctl-bi-info-public {
      background-image: url('./icon/icon_gongkai.svg');
      background-size: 14px 14px;
    }

    .ctl-bi-info-member {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      height: 14px;
      padding-right: 8px;
      padding-left: 8px;
      font-size: 12px;
      text-indent: 18px;
      background-size: 14px 14px;
      background: url('./icon/icon_chengyuan.svg') no-repeat left;
      background-origin: content-box;
      border-left: 1px solid #E8E9EA;
      word-break: keep-all;
    }

    .clt-bi-info-owner{
      padding-right: 8px;
      padding-left: 8px;
      border-left: 1px solid #E8E9EA;
      font-size: 12px;
      text-indent: 18px;
      background-size: 14px 14px;
      background: url('./icon/owner-icon.svg') no-repeat left;
      background-origin: content-box;
      .ellipsis()
    }
  }
  .ctl-box-item-out {
    &:hover {
      background: #F6F7F7;
      border: 1px solid #F6F7F7;
    }
    cursor: auto;
  }
}


.loading-div {
  position: absolute;
  background-color: #fff;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  left: 0;
  top: 0;
  z-index: 10;
}

:global{
  .windows .cooper-team-list-os-flag{
    overflow: hidden;
    &:hover{
      overflow-y: overlay;
    }

    .ctl-content-os-flag {
      width: 100%;
      &:hover{
        width: -moz-calc( 100% + 17px ) !important;
        width: -o-calc( 100% + 17px ) !important;
        width: -webkit-calc( 100% + 17px ) !important;
        width: calc(100% + 17px) !important;
      }
    }
  }
  .mac, .linux, .generic {
    .ctl-content-os-flag{
      overflow-y: overlay;
    }
  }
}

