.createKnowledge {
  .titleArea {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        .font-size(18px);
        font-weight: 600;
        color: @blueGray-1;
      }
      .backIconWrap {
        cursor: pointer;
        :global{
         .ant-input-clear-icon {
           svg {
             display: none;
           }
           &::after {
             font-family: "dk-iconfont" !important;
             content: '\e6fd';
             font-size: 24px;
           }
           &:hover {
             &::after {
               background: rgba(47, 52, 60, 0.08);
             }
           }
         }
        }
       }
    
  }

  .buttonArea {
    display: flex;
    flex-direction: row-reverse;
    text-align: center;
    margin: 24px 0 0 0;
    .cancel {
      // padding: 0;
      margin-right: 8px;
      // width: 60px;
      // border-radius: 4px;
      // border: 1px solid #DDDDDD;
      // font-size: 14px;
      // color: #333333;
      // &:hover {
        // background-color: #F7F8F9;
      // }
    }
    .ok {
      padding: 0;
      border: none;
      width: 60px;
      background: #088FFC;
      color: #FFFFFF;
      border-radius: 4px;
      font-size: 14px;
      &:hover {
        background-color: #077BDB;
      }
    }
    .okDisabled {
      opacity: 0.5;
      &:hover {
        background: #088FFC;
      }
    }
  }
  :global {
    .ant-modal-content {
      border-radius: 12px;
    }
    .ant-modal-body {
      padding: 32px;
    }
  }
  .error-icon {
    vertical-align: text-bottom;
    margin-right: 4px;
  }
}
.departMentModal {
  :global {
    .ant-modal-content .ant-modal-footer button.ant-btn:first-child {
      display: none;
    }
  }
}


.basicSetup {
  width: 556px;

  .mainTitle {
    height: 22px;
    font-size: 14px;
    color: @blueGray-color;
    line-height: 22px;
    font-weight: 500;
    .knowledgeId {
      margin-left: 15px;
      font-weight: 400;
      user-select: text;
    }
  }

  .avatarTitle {
    margin-top: 10px;
  }
  .required {
    color: @error-color;
    font-size: 20px;
    font-weight: @font-weight-medium;
    padding-left: 4px;
    vertical-align: middle;
  }
  .coverIcon {
    margin: 10px 12px 0 1px;
    width: 56px;
    height: 56px;
    border-radius: 4px;
    &.canEdit {
      cursor: pointer;
    }
  }
  .nameItem {
    display: flex;
    position: relative;
    .spaceNameCheckMsg {
      color: @error-color;
      margin-left: 0;
      bottom: -8px;
    }
  }
  .nameInputWrap {
    margin-top: 8px;
    flex: 1;
    height: 58px;
  }

  .nameInput {
    height: 42px;
    line-height: 24px;
    border-radius: 4px;
    font-size: 14px;
    border: 1px solid #DADCE3;
    &.disabled {
      color: rgba(0, 0, 0, 0.25);
    }
    &:focus {
      box-shadow: none;
      border: 0.5px solid #088FFC;
      box-shadow: 0 0 0 1.5px rgba(8, 143, 252, 0.1);
    }

    :global {
      .ant-input-suffix {
        visibility: hidden;
      }
    }
    &:focus, &:focus-within {
      :global {
        .ant-input-suffix {
          visibility: visible;
        }
      }
    }
  }
  .introTitle {
    margin: 24px 0 8px;
    height: 22px;
    font-size: 14px;
    color: @blueGray-color;
    line-height: 22px;
    font-weight: 500;
    position: relative;
  }
  .introTips {
    position: absolute;
    right: 0;
    color: @error-color;
    font-size: 12px;
    top: 2px;
  }
  .form {
    .introText {
      padding: 12px;
      height: 120px !important;
      border-radius: 4px;
      border: 1px solid #DADCE3;
      font-size: 14px;
      color: #505050;
      line-height: 22px;
      resize: none;
      &::-webkit-scrollbar {
        display: none;
      }

      &.disabled {
        color: rgba(0, 0, 0, 0.25);
      }
      &:focus {
        box-shadow: none;
        border: 0.5px solid #088FFC;
        box-shadow: 0 0 0 1.5px rgba(8, 143, 252, 0.1);
      }
  }
  }
  .saveBtn {
    margin-top: 32px;
    height: 32px;
    background: @primary-color !important;
    border-radius: 4px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: #FFFFFF !important;
    border: none;
    &:hover {
      background: @primary-color;
      color: #FFFFFF;
    }
    &.disabled {
      opacity: 0.5;
    }
  }
  .beDepartment {
    margin-top: 64px;
    .title {
      margin-bottom: 10px;
      height: 22px;
      font-size: 16px;
      font-weight: 500;
      color: #111111;
      line-height: 22px;
    }
    .desc {
      margin-bottom: 9px;
      height: 22px;
      font-size: 14px;
      font-weight: 400;
      color: @text-color;
      line-height: 22px;
    }
    .tip {
      font-size: 14px;
      color: rgb(156, 156, 156);
      margin-bottom: 24px;
    }
    .dkId {
      color: @primary-color;
      cursor: pointer;
      font-weight: 500;
    }
    .btn {
      padding: 0 20px;
      height: 44px;
      line-height: 44px;
      background: @primary-color;
      border-radius: 4px;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 44px;
      border: none;
      &.disabled {
        background: #F7F7F7;
        color: #999;
      }
    }
  }

  .avatar-list {
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
  }

  .iconBlock {
    border: 2px solid transparent;
    width: 40px;
    height: 40px;
    box-sizing: content-box;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      border: 1px solid @primary-color;
      text-align: center;
      line-height: 40px;
      width: 42px;
      height: 42px;
    }
    .icon {
      width: 38px;
      height: 38px;
      border-radius: 4px;
    }
  }

  .iconBlockSeleted {
    border: 1px solid @primary-color;
    text-align: center;
    line-height: 40px;
    width: 42px;
    height: 42px;
    cursor: unset;

    .icon {
      width: 38px;
      height: 38px;
      border-radius: 4px;
    }
  }
}


:global {
  .ant-modal-content {
    border-radius: 8px;
  }
}

.redIcon {
  color: #FF563B;
  margin-right: 2px;
}