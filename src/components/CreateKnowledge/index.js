import { intl } from 'di18n-react';
import CreateModal from './CreateModal';
import { useState } from 'react';
import { Button } from 'antd';

const CreateKnowledge = () => {
  const [visible, setVisible] = useState(false);

  const onCreate = () => {
    window.__OmegaEvent('ep_dkpc_dkhome_createdk_ck');
    setVisible(true);
  };

  return (
    <div className="create-wrap">
      <Button
        onClick={onCreate}
        id="dk-create-btn" // 用于引导气泡位置
        type="primary"
      >
        {intl.t('新建知识库')}
      </Button>
      <CreateModal
        visible={visible}
        isReplaceNewTab={true}
        onCloseModal={() => {
          setVisible(false);
        }}
      />
    </div>
  );
};

export default CreateKnowledge;
