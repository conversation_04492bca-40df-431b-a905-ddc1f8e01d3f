.content-tip {
  cursor: pointer;
  color: rgba(0, 102, 255, 1);
}

.content-title-weight {
  font-weight: bold;
}

.folder-tree {
  height: 100%;
  width: 100%;
  overflow: auto;
  position: relative;

  .checkbox-wrap {
    margin-right: 12px;
  }

  .ft-triangle-wrapper {
    height: 30px;
    width: 19px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .tb-body .tb-body-row-fileList:hover {
    background-color: #F3F3F3;
    border-radius: 4px;
  }

  .tb-header>.tb-header-div,
  .tb-body .tb-body-row-fileList {
    height: 46px;
    line-height: 46px;
    white-space: nowrap;
    display: flex;
    align-items: center;
    color: @blueGray-4;
    padding-left: 4px;

    >span {
      display: inline-block;
    }

    .file-name {
      min-width: 300px;
      max-width: initial;
      width: calc(100% - 448px);
      display: flex;
      align-items: center;
      overflow: hidden;

      .file-name-display {
        display: inline-block;
        flex: 1;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      >.ant-checkbox-wrapper {
        position: relative;
        top: -2px;
        margin-right: 18px;
      }
    }

    .file-resizer {
      width: 20px;
      margin-right: 8px;
      margin-left: 12px;
      position: relative;
      text-align: center;

      .resizer-line {
        width: 1px;
        height: 50px;
        background: #076BFF;
        position: absolute;
        top: 0;
        left: 50%;
      }
    }

    >.file-time {
      width: 130px;
      margin-right: 30px;
      display: flex;
      align-items: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .file-owner {
      width: 130px;
      margin-right: 30px;
      align-items: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    >.file-owner+.file-time {
      margin-right: 4px;
    }

    >.file-operate {
      width: 114px;
      padding-right: 4px;
      display: flex;
      justify-content: right;
      align-items: center;
    }

    .triangle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 19px;
      margin-right: 10px;
      font-size: 16px;
      // background-size: 8px 8px;
      // background-image: url(https://img-ys011.didistatic.com/static/cooper_cn/do1_Bw1673hz9vMmk8p48HJH);
      // background-position: center center;
      // background-repeat: no-repeat;
      cursor: pointer;
      color: @blueGray-4;

      &-li {
        margin-left: 4px;
      }
      
    }
  }
  .is-open {
    font-size: 16px;
    transform: rotate(90deg);
  }
  .is-loading {
    font-size: 14px !important;
    animation-name: rotate;
    animation-duration: 1s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
  }

  @keyframes rotate {
   0%{
    transform: rotate(0);
   }
   50% {
    transform: rotate(180deg);
   }
   100% {
    transform: rotate(360deg);
   }
  }
  .tb-header {
    position: relative;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    width: max-content;
    min-width: 100%;
    padding: 0 24px;
    display: flex;

    .tb-header-div {
      width: max-content;
      min-width: 100%;
      display: flex;
      height: 36px !important;
      line-height: 36px !important;
      border-bottom: 1px solid @blueGray-11;

      .file-operate,
      .file-owner,
      .file-time,
      .file-name {
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #666666;
        line-height: 22px;

        .ant-table-column-sorter {

          .anticon-caret-up,
          .anticon-caret-down {
            font-weight: bold;
          }
        }
      }
    }
  }

  .tb-header,
  .tb-body {

    .tb-body-row-fileList.bottom-tip {
      pointer-events: none;
      border: none;
      color: @blueGray-4;
      display: flex;
      font-size: 14px;
      justify-content: center;
      margin-bottom: 32px;
    }
  }

  .tb-body {
    height: calc(100% - 40px);
    min-width: 100%;
    width: max-content;
    overflow: hidden;
    padding: 8px 24px 0;
    &:hover{
      overflow: auto;
    }

    .file-name {
      color: @blueGray-1;
    }
  }

  .tb-body-row-fileList {
    padding: 0 4px;

    &:hover {
      border-radius: 4px;
      background: @blueGray-12;
    }
  }

  :global {
    .ant-btn {
      min-width: 60px;
      width: auto;
      height: 32px;
      border: 1px solid #DDDDDD !important;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      line-height: 20px;

      &:hover,
      &:focus {
        border-color: #518dff;
        color: #518dff;
        background-color: #fff;
      }
    }
  }

  .loading-tip {
    margin-top: 14px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    font-size: 14px;

    .text {
      margin-left: 4px;
    }
  }

}

.person-list {

  .tb-header>.tb-header-div,
  .tb-body .tb-body-row-fileList {
    .file-name {
      width: calc(100% - 280px);
    }

    >.file-operate {
      width: 80px;
      padding-right: 4px;
      display: flex;
      justify-content: right;
      align-items: center;
    }
  }
}

.loadMore {
  margin-top: 16px;
  text-align: center;
  .font-size(14px);
  color: @blueGray-4;
  &:hover {
    opacity: 0.8;
    cursor: pointer;
  }
}
