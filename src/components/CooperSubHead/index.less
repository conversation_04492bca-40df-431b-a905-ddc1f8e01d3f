.cooper-subhead {
  padding-right: 14px;
  display: flex;
  align-items: center;

  &:nth-child(2) {
    .cooper-subhead-title {
      padding: 8px 10px 8px;
    }
  }

  .cooper-subhead-title {
    padding: 22px 0;
    color: #666;
    display: flex;

    .lockTag {
      margin: 3px 0 0 3px;
      padding: 0 4px;
      height: 16px;
      line-height: 16px;
      background: #EFF0F2;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;

      .word {
        display: block;
        color: #666666;
        transform-origin: center center;
        transform: scale(0.833333333333334);
      }
    }
  }

  .cooper-subhead-title-text {
    float: left;
    margin-top: 6px;
    margin-left: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }


  .ant-breadcrumb {
    white-space: nowrap;

    .bread-omit {
      border-radius: 4px;

      &:hover {
        background: #F7F7F7;
      }
    }

    >ol > li {
      display: inline-block;
      cursor: pointer;
      height: 21px;
    }

    >ol > li:last-child .ant-breadcrumb-link {
      color: #2F343C;
      font-weight: 500;
    }

    >ol > li:first-child .ant-breadcrumb-link {
      padding-left: 0;
    }

    .ant-breadcrumb-separator {
      float: left;
      position: relative;
      margin: 0;
      top: -1px;
    }

    .ant-breadcrumb-link,
    .ant-breadcrumb-link a,
    .ant-breadcrumb-link span {
      color: #666666;
      ;
    }

    .ant-breadcrumb-link:hover,
    .ant-breadcrumb-link:hover a {
      color: @primary-color;
    }

    .ant-breadcrumb-link {
      overflow: hidden;
      float: left;
      max-width: 155px;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: center;

      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 400;
      color: #2F343C;
      line-height: 21px;
      padding: 0 5px;
      font-size: 16px;
    }
  }





  .icon-separator {
    display: inline-block;
    width: 8px;
    height: 8px;
    background-image: url(./icon/icon_mianbaoxie.svg);
    background-size: 8px 8px;
  }
}

.is-dc-subhead {
  .ant-breadcrumb {
    >span:nth-child(1) .ant-breadcrumb-separator {
      margin-left: 2px;
    }

    >span:nth-child(2) .ant-breadcrumb-separator {
      margin-left: 2px;
    }
  }

  .ant-breadcrumb-link {
    max-width: 84px;
  }

  .ant-breadcrumb-separator {
    margin: 0 2px 0 -4px;
  }
}


.bread-crumb-container {
  .ant-popover-arrow {
    display: none;
  }
}

.pop-ellipsis {
  width: 400px;
  max-height: 452px;
  background: #FFFFFF;
  border-radius: 6px;
  overflow-y: scroll;

  &::-webkit-scrollbar {
    display: none !important;
  }

  .folder-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;

    &:hover {
      background: #F7F7F7;
    }

    .folder-item-link {
      width: 100%;
      display: flex;
      align-items: center;

      &:hover {
        cursor: pointer;

        .folder-name {
          color: #0066FF;

        }

      }

      .folder-icon {
        width: 28px;
        height: 28px;
        object-fit: cover;
        vertical-align: middle;
        margin-right: 8px;
      }

      .folder-name {
        color: #2F343C;
        font-size: 14px;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.cooper-subhead-teamManage-tooltip {
  z-index: 900;

  .teamManage-tip {
    color: #3D8CDF;
    cursor: pointer;
    font-weight: 700;
  }

  .ant-tooltip-inner {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

.sub-header-share .cooper-subhead-title{
  padding: 0 !important;
}
.sub-header-share .ant-breadcrumb .ant-breadcrumb-link{
  .font-size(12px) !important;
  color: @blueGray-7;
}
.sub-header-share.ant-breadcrumb > ol > li:last-child .ant-breadcrumb-link{
  color: @blueGray-4;
}

