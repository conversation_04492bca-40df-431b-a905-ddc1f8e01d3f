import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import { Select, Spin } from 'antd';
import { useState } from 'react';
import { checkError<PERSON>haracter } from '@/utils';
import styles from './style.module.less';

const cx = classBind.bind(styles);
/**
 * 封装
 * loading
 * onPressEnter
 *
 */
export default function SearchSelect({
  loading = false,
  onSearch = () => {},
  onPressEnter = () => {},
  onFocus = () => {},
  list = [],
  keyMap = {},
  selectValue,
  children,
  filterOption = false,
  notFoundContent = intl.t('暂无内容'),
  ...rest
}) {
  const { key = 'id', val = 'value', label = 'label' } = keyMap;

  const onInputKeyDown = (e) => {
    const { value } = e.target;
    if (e.key === 'Enter' && onPressEnter) {
      if (checkError<PERSON>haracter(value) === '') {
        onPressEnter(value);
      }
    }
  };

  const onSearchFn = (value) => {
    if (checkError<PERSON>haracter(value) === '') {
      onSearch();
    }
  };

  return (
    <Select
      {...rest}
      showSearch
      className={cx('dk-ant-select_reset')}
      allowClear
      loading={loading}
      value={selectValue}
      onSearch={onSearchFn}
      onFocus={(e) => onFocus(e.target.value)}
      onInputKeyDown={onInputKeyDown}
      filterOption={filterOption}
      defaultActiveFirstOption={false}
      notFoundContent={loading ? <Spin size="small" /> : notFoundContent}
    >
      {list.map((item) => (
        <Select.Option
          key={item[key]}
          value={item[val]}>
          {typeof children === 'function' ? children(item) : item[label]}
        </Select.Option>
      ))}
    </Select>
  );
}
