.office-player {
  position: fixed;
  top: 56px;
  right: 0;
  bottom: 0;
  left: 0;

  .container {
    height: calc(100% - 1px);
    width: 100%;
    overflow: auto;

    // &.is-dc {
    //   height: calc(100% - 76px);
    // }

    iframe {
      display: block;
      min-width: 100%;
      min-height: 100%;
      border: none;
      transform-origin: 0 0;
    }
  }

  .tools {
    position: absolute;
    bottom: 70px;
    left: 0;
    width: 100%;
    height: 40px;
    overflow: hidden;
    display: flex;
    justify-content: center;

    >div {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      width: 140px;
      opacity: 0;
      height: 40px;
      background-color: rgba(#000, .8);
      border-radius: 4px;
      transition: .5s;
    }

    &:hover>div {
      opacity: 1;
    }

    button {
      width: 20px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: transparent;
      border: none;
      cursor: pointer;

      img {
        height: 20px;
        width: 20px;
      }
    }
  }
}