.header-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 50%;
  max-width: 50%;

  .back-icon {
    font-size: 18px;
    width: 26px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }

  .icon-word {
    width: 20px;
    height: 20px;
    margin: 0 8px 0 2px;
  }

  .file-name {
    font-size: 14px;
    max-width: calc(100% - 40px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    
  }
}

.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-start;

  .open-in-cooper {
    display: flex;
    align-items: center;
    width: auto;
    height: 32px;
    border: 1px solid #FFFFFF80;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #FFFFFF;
    text-align: center;
    position: relative;
    margin-right: 30px;
    padding: 0 16px 0 12px;
    background-color: transparent;
    cursor: pointer;

    img {
      width: 18px;
      height: 18px;
    }

    &::after {
      content: '';
      height: 70%;
      width: 1px;
      background-color: #FFFFFF;
      position: absolute;
      right: -21px;
    }
  }

  .button {
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    margin: 0 10px 0;
    opacity: .8;
    cursor: pointer;
    border: none;

    img {
      width: 18px;
    }

    &:focus {
      outline: none;
    }

    &:hover {
      opacity: 1;
    }
  }
  .button.disabled {
    opacity: 0.4;
    &:hover {
      background-color: #333333;
    }
  }
  .more {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 12px;

    img {
      width: 16px;
      height: 3px;
    }

    &:hover {
      background: #333333;
      border-radius: 3px;
    }
  }
}

.hover-no-auth-tip {
  width: 254px;
  .ant-tooltip-inner {
    color: #222A35 !important;
    background-color: #FFFFFF !important;
    padding: 16px !important;
    .top-text {
      font-size: 14px;
      line-height: 22px;
      .auth-type {
        margin: 0px 3px;
        font-weight: bold;
      }
    }
    .bot-text-link {
      display: block;
      color:  #047FFE;
      font-size: 12px;
      line-height: 17px;
      &:hover {
        text-decoration: underline;
      }
      &:last-child {
        margin-top: 6px;
      }
    }
  }
}