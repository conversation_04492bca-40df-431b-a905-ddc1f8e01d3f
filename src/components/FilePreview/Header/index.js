
import ImportCollab from '@/components/CooperOperation/ImportCollab';
import ShareFile from '@/components/CooperOperation/ShareFile';
import OperateMenu from '@/components/OperateMenu';
import { updateQuicks } from '@/components/QuickAccess/index';
import { getOperationAuth, getOperationShareAuth } from '@/service/cooper/home';
import miniBus from '@/utils/miniBus';
import { Tooltip, message, Divider } from 'antd';
import { intl } from 'di18n-react';
import { Fragment, useEffect, useState } from 'react';
import { getSafeLevel } from '@/service/cooper';
import IcDownload from '../icon/v2/icon_xiazai.svg';
import open from '../icon/v3/<EMAIL>';
import share from '../icon/v3/<EMAIL>';
import more from '../icon/v3/<EMAIL>';
import Tag from '@/components/Tag';
import CustomerService from '@/baseComponents/CustomerService';
import { PREVIEW_OPT, PREVIEW_SHARE_LINK } from '@/components/OperateMenu/constant';
import './index.less';

// 操作里面的配置
const config = {
  moveOperate: false,
  copyOperate: false,
  renameOperate: false,
  duplicateOperate: false,
  downloadOperate: false,
  shareOperate: false,
  multiSelectOperate: false,
  starOperate: false,
  useConfig: true
};

const replaceWithBold = (inputString) => {
  console.log(inputString, 'inputString');
  // 正则表达式用于匹配${}内的内容
  const regex = /\$(.*?)\$/g;
  if (typeof inputString === 'string') {
    // 使用String.prototype.replace方法和一个替换函数来进行替换
    return inputString.replace(regex, (match, group1) => {
      // group1包含了${}内的文本
      return `<span class="auth-type">${group1}</span>`;
    });
  }
  return inputString
}
// eslint-disable-next-line consistent-return
const getAuthTip = (type, auth) => {
  console.log(type, auth);
  if (type === 'share') {
    if (auth) {
      return (
        <div>
          <div className='top-text'>
            {
              intl.t('你没有权限，可咨询管理员授予')
            }
            <span class="auth-type">{intl.t('分享')}</span>
            {
              intl.t('权限')
            }
          </div>
          <Divider style={{ marginTop: '12px', marginBottom: '12px' }}/>
          {
            PREVIEW_SHARE_LINK.map((item, index) => {
              return (
                <a
                  className='bot-text-link'
                  href={item.link}
                  target='_blank'
                  key={index}>{intl.t(item.text)}</a>
              )
            })
          }
        </div>
      )
    }
    return intl.t('分享')
  }
  if (type === 'download') {
    if (auth) {
      return (
        <div>
          <div className='top-text'>
            <div className='top-text'>
              {
              intl.t('你没有权限，可咨询管理员授予')
            }
              <span class="auth-type">{intl.t('下载')}</span>
              {
              intl.t('权限')
            }
            </div>
          </div>
          <Divider style={{ marginTop: '12px', marginBottom: '12px' }}/>
          {
            PREVIEW_SHARE_LINK.map((item, index) => {
              return (
                <a
                  className='bot-text-link'
                  href={item.link}
                  target='_blank'
                  key={index}>{intl.t(item.text)}</a>
              )
            })
          }
        </div>
      )
    }
    return intl.t('下载')
  }
}

function Header(props) {
  const { fname, file, onClose, state, onDownload, updateImgFiles, refreshFolder, updateShareFiles } = props
  const { type, hasOperation } = state
  const [headerState, setHeaderState] = useState({
    rotate: 0,
    scale: 10,
    convertTip: '',
    apiShareOperate: false,
    apiDownloadOperate: false,
  })
  const [safeLevel, setSafeLevel] = useState({});
  useEffect(() => {
    type && getAuth()

    window.addEventListener('message', closeFromIframe);
    return () => {
      window.removeEventListener('message', closeFromIframe);
    }
  }, [type]);

  useEffect(() => {
    if (!file.id) return;
    reqSafeLevel();
  }, [file.id]);

  function reqSafeLevel() {
    getSafeLevel({ resourceId: file.id }).then((data) => {
      setSafeLevel(data);
    });
  }

  // 定义删除 快速访问的回调函数
  const uniqueCallback = {
    deleteOperate: () => {
      onClose();
      miniBus.dispatchEvent('folderCreated');
      if (typeof updateQuicks === 'function') {
        updateQuicks();
      }
      if (typeof refreshFolder === 'function') {
        refreshFolder()
      }
    },
    handelQuickAccess: (quickId) => {
      if (typeof updateQuicks === 'function') {
        updateQuicks();
      }
      if (typeof updateImgFiles === 'function') {
        updateImgFiles(file, quickId);
      }
      if (typeof refreshFolder === 'function') {
        refreshFolder()
      }
      if (typeof updateShareFiles === 'function') {
        updateShareFiles(file);
      }
      miniBus.dispatchEvent('folderCreated');
    },
  };

  // 判断文件所在地是不是团队空间
  const isTeam = (data) => {
    return data.space_type_str === 'TEAM_SPACE' || data.spaceType === 'TEAM_SPACE' || window.location.href.includes('/team-file/')
  }

  // 获取权限信息
  const getAuth = () => {
    const shareId = file.shareId || file.share_id || '';
    const { shareType } = file;

    if (shareId && shareType) {
      getOperationShareAuth(shareId, shareType, file.resourceId || file.id || file.objectId)
        .then((res) => {
          setHeaderState({
            // eslint-disable-next-line no-bitwise
            apiShareOperate: !!(res.perm & 16),
            // eslint-disable-next-line no-bitwise
            apiDownloadOperate: !!(res.perm & 32),
          })
        })
        .catch(() => {
          message.error(intl.t('获取权限失败'));
        });
    } else {
      getOperationAuth(file.resourceId || file.id || file.objectId)
        .then((res) => {
          setHeaderState({
            // eslint-disable-next-line no-bitwise
            apiShareOperate: !!(res.perm & 16),
            // eslint-disable-next-line no-bitwise
            apiDownloadOperate: !!(res.perm & 32),
          })
        })
        .catch(() => {
          message.error(intl.t('获取权限失败'));
        });
    }
  }

  const editByCollab = () => {
    const isSheet = !/\.docx$|\.doc$|\.md$|\.txt$/i.test(fname)
    window.__OmegaEvent(isSheet ? 'ep_filespreview_openwithsheet_ck' : 'ep_filespreview_openwithdoc_ck');
    let parentId = file.parent_id || file.parentId || 0
    let spaceId = file.space_id || file.spaceId || file.team || file.sourceId
    ImportCollab(file, 1, parentId, spaceId, () => {
      // console.log('转换为在线文档');
    });
  }

  const closeFromIframe = (e) => {
    // iframe 中的关闭事件
    if (['formcancel', 'formclose'].includes(e.data.event)) {
      // this.props.onClose();
    }
  };

  const backGround = (
    <div className='more'>
      <img src={more} />
    </div>
  );

  const shareByPreview = (fileItem) => {
    window.__OmegaEvent('ep_filespreview_share_ck');
    ShareFile(fileItem.id, fileItem.name, undefined, fileItem.relationTypeTags);
  }

  const collabTitle = /\.xlsx$|\.xls$|\.csv$|\.xlsm/i.test(fname)
    ? intl.t('用协作表格打开')
    : intl.t('用协作文档打开');

  const _getFamilyByType = (securityLevel) => {
    if (!securityLevel) return;
    if (securityLevel === 'UNKNOWN') return 'safety-level-2';
    if (securityLevel.indexOf('1') !== '-1' || securityLevel.indexOf('2') !== '-1') return 'safety-level-2';
    if (securityLevel.indexOf('3') !== '-1') return 'safety-level-3';
    if (securityLevel.indexOf('4') !== '-1') return 'safety-level-4';
    if (securityLevel.indexOf('5') !== '-1') return 'safety-level-5';
  }

  return <Fragment>
    <div className='header-left'>
      <i
        className='dk-iconfont dk-icon-fanhuiyemian back-icon'
        onClick={onClose} />
      <span className='file-name'>{fname}</span>
      <div className='tag-wrap'>
        {safeLevel.securityLevel && safeLevel.securityLevel !== 'UNKNOWN' && <Tag
          type={_getFamilyByType(safeLevel.securityLevel)}
          text={`${intl.t('数据等级C')}${safeLevel.securityLevel}`}
        />}
        {
          ((file.relationTypeTags || []).includes('IN_OUT')) && <Tag
            type='out-yellow'
            text={intl.t('外部')}
          />
        }
      </div>
    </div>
    <div className='header-right'>

      {/* 用协作文档打开 */}
      {
        /\.docx$|\.doc$|\.md$|\.txt$|\.xlsx$|\.xls$|\.csv$|\.xlsm$/i.test(fname) && (
          <Tooltip
            title={collabTitle}
            placement='bottom'>
            <button
              onClick={editByCollab}
              className='open-in-cooper'>
              <img
                src={open}
                alt='icon-edit' />
              <div className='right-text'>
                {collabTitle}
              </div>
            </button>
          </Tooltip>
        )
      }
      {/* 分享 */}
      {/* {
        headerState.apiShareOperate && (
          <Tooltip
            title={intl.t('分享')}
            placement='bottom'
          >
            <span
              className='button'
              onClick={() => shareByPreview(file)}
            >
              <img
                src={share}
                alt='icon-feedback' />
            </span>
          </Tooltip>
        )
      } */}
      <Tooltip
        title={getAuthTip('share', !headerState.apiShareOperate)}
        placement='bottom'
        overlayClassName={!headerState.apiShareOperate ? 'hover-no-auth-tip' : ''}
      >
        <span
          className={`button ${!headerState.apiShareOperate ? 'disabled' : ''}`}
          onClick={() => headerState.apiShareOperate && shareByPreview(file)}
        >
          <img
            src={share}
            alt='icon-feedback' />
        </span>
      </Tooltip>
      {/* 下载 只有 excel、word、ppt、txt、pdf 的时候需要下载按钮在上面出现 */}
      {/* { headerState.apiDownloadOperate && type === 'newoffice' && <Tooltip
        title={intl.t('下载')}
        placement='bottom'
      >
        <span
          className='button'
          onClick={() => {
            window.__OmegaEvent('ep_filespreview_download_ck');
            onDownload();
          }}>
          <img
            src={IcDownload}
            alt='icon-download' />
        </span>
      </Tooltip> } */}
      <Tooltip
        title={getAuthTip('download', !headerState.apiDownloadOperate)}
        placement='bottom'
        overlayClassName={!headerState.apiDownloadOperate ? 'hover-no-auth-tip' : ''}
      >
        <span
          className={`button ${!headerState.apiDownloadOperate ? 'disabled' : ''}`}
          onClick={() => {
            window.__OmegaEvent('ep_filespreview_download_ck');
            headerState.apiDownloadOperate && onDownload();
          }}>
          <img
            src={IcDownload}
            alt='icon-download' />
        </span>
      </Tooltip>
      {
        hasOperation && <OperateMenu
          trigger='hover'
          backGround={backGround}
          key={file.id}
          file={file}
          config={config}
          isTeam={isTeam(file)}
          uniqueCallback={uniqueCallback}
          originFileType={9}
          location={PREVIEW_OPT}
        />
      }

      <CustomerService
        hideSpliteLine={true}
        hideShowNewFeature={true}
      />

    </div>
  </Fragment>
}

export default Header
