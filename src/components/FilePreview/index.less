.cooper-preview {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  font-size: 14px;
  background-color: rgba(0, 0, 0, 0.8);

  &-header {
    position: fixed;
    z-index: 3015;
    height: 56px;
    width: 100%;
    background: #141414;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
  }

  &-body {
    position: absolute;
    top: 56px;
    bottom: 80px;
    left: 0;
    right: 0;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;

    >img {
      display: none;
    }

    .cp-video-content {
      width: 80%;
      height: 80%;
      overflow: auto;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .cp-unknown {
      color: #fff;
    }

    .cp-pre {
      width: 80%;
      height: 80%;
      overflow: auto;
      padding: 10px;
      background: #fff;
      color: #333;
    }
  }

  &-footer {
    position: fixed;
    bottom: 30px;
    left: 0;
    right: 0;
    z-index: 3015;
    height: 52px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .cp-content {
    position: relative;
    overflow: auto;
    display: flex;
    width: 80%;
    height: 80%;
    justify-content: center;
    align-items: center;
    &.has-image {
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,.9);
    }
    &.has-pdf{
      height: 100%;
    }
  }
  .cp-pre {
    // width: 100%;
    overflow: auto;
    padding: 10px;
    background: #fff;
    color: #333;
  }
  .video-react {
    height: 100% !important;
    width: 100% !important;
  }
  .cp-unknown {
    display:flex;
    font-size: 14px;
    color: #fff;
    align-items:center;
    justify-content:center;
  }
  .cp-foot {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 10;
    height: 68px;
    width: 100%;
    background: #000;
    color: #fff;
  }
  .cp-foot-tool {
    width: 100%;
    height: 68px;
    line-height: 68px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  .cp-ft-item{
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    .cp-ft-icon {
      display: inline-block;
      height: 20px;
      margin: 0 4px 0 18px;
     
    }
    .cp-ft-text{
      .font-size(12px);
    }
    .cp-image {
      flex-shrink: 0;
    }
  }
  
  .video-iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
}

.viewer-container-fullscreen {
  .cooper-preview-header {
    display: none;
  }

  .viewer-container {
    .viewer-footer {
      height: 0px !important;
      overflow: hidden !important;
    }
    .viewer-navbar {
      height: 0px;
    }
  }
}

.viewer-container-fullscreen-cancel {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  right: 40px;
  top: 40px;
  z-index: 10000;
  background-image: url('./icon/close.png');
  background-repeat: no-repeat;
  background-size: 16px 16px;
  background-position: center center;
  cursor: pointer;

  &::before {
    content: attr(title);
    display: block;
    opacity: 0;
    position: absolute;
    white-space: nowrap;
    left: 50%;
    top: -20px;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    font-size: 12px;
    color: #fff;
    text-align: center;
    height: 25px;
    line-height: 25px;
    padding: 0 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #222A35;
    border-radius: 4px;
  }

  &:hover {
    background-color: rgba(255, 255, 255, 0.15);

    &::before {
      opacity: 1;
    }
  }
}