import { intl } from 'di18n-react';
import { useMemo } from 'react';
import { Spin } from 'antd';
import classBind from 'classnames/bind';
import { TAB_TYPE } from '@/constants/dkList';
import ErrorTips from '@/components/ErrorTipsDk';
import { NoMyDkIcon, NoMyJoinDkIcon } from '@/assets/icon';
import { isDesktopDC } from '@/utils';
import DKCard from '@/pages/cooper/KnowledgeList/DKCard';
import MHCard from '@/pages/cooper/PortalList/MHCard';
import styles from './style.module.less';

const cx = classBind.bind(styles);

const DKList = ({ type, dkData, errorData, initLoading, isMyKnowledge }) => {
  const noData = useMemo(() => {
    return Array.isArray(dkData) ? dkData.length === 0 : [];
  }, [dkData]);

  const isJoin = useMemo(() => {
    return Number(type) === TAB_TYPE.JOIN;
  }, [type]);

  const tipsTitle = useMemo(() => {
    if (errorData) return intl.t('数据获取失败，请稍后再试');
    if (isMyKnowledge) {
      return intl.t('暂无知识库');
    }
    if (!isJoin) {
      return intl.t('赶紧创建一个知识门户，帮助团队快速聚合和透传知识');
    }
    return intl.t('你还没有被邀请进他人的知识门户');
  });

  const tipsDesc = useMemo(() => {
    if (errorData) return null;
    if (isMyKnowledge) {
      if (Number(type) === TAB_TYPE.ALL) { return intl.t('可前往有管理权限的团队空间创建，在此可进行快速访问'); }
    }
    return undefined;
  });

  const onClickCard = () => {
    // 注意1 和 '1'的判断
    switch (type) {
      case type == TAB_TYPE.ALL:
        isMyKnowledge
          ? window.__OmegaEvent('ep_dkpc_dkhome_all_viewdk_ck')
          : window.__OmegaEvent('ep_kg_home_all_viewkg_ck');
        break;
      case type == TAB_TYPE.OWN:
        isMyKnowledge
          ? window.__OmegaEvent('ep_dkpc_dkhome_owned_viewdk_ck')
          : window.__OmegaEvent('ep_kg_home_manage_viewkg_ck');
        break;
      case type == TAB_TYPE.JOIN:
        isMyKnowledge
          ? window.__OmegaEvent('ep_dkpc_dkhome_joined_viewdk_ck')
          : window.__OmegaEvent('ep_kg_home_joined_viewkg_ck');
        break;
      default:
        break;
    }
  };

  return (
    <div
      className={cx('dk-list-wrap', {
        'dk-list-wrap-dc': isDesktopDC,
      })}
    >
      {initLoading && (
        <div className={'page-detail-loading'}>
          <Spin />
        </div>
      )}

      {noData ? (
        <div className={cx('no-list')}>
          <ErrorTips
            title={tipsTitle}
            desc={tipsDesc}
            overlayClassName={cx('dk-and-mh-list')}
            img={isJoin && !isMyKnowledge ? NoMyJoinDkIcon : NoMyDkIcon}
          />
        </div>
      ) : (
        <ul
          className={cx({
            'team-dk-list': isMyKnowledge,
            'team-dk-list-mh': !isMyKnowledge,
            'team-dk-list-dc': isMyKnowledge && isDesktopDC,
            'team-dk-list--mh-dc': !isMyKnowledge && isDesktopDC,
          })}
        >
          {dkData.map((item) => {
            return isMyKnowledge ? (
              <DKCard
                key={item.id}
                dkInfo={item}
                onClick={onClickCard} />
            ) : (
              <MHCard
                key={item.id}
                mhInfo={item}
                onClick={onClickCard} />
            );
          })}
        </ul>
      )}
    </div>
  );
};

export default DKList;
