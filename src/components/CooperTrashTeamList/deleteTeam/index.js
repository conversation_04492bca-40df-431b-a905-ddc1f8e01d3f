import { intl } from 'di18n-react';
import React from 'react';
import { Input, message } from 'antd';
import { postAbort } from '@/utils/request/abort';
import { del } from '@/utils/request/cooper';
import PrimaryModal from '@/components/common/PrimaryModal';
import api from '@/utils/request/api/CooperApi';
import mountAnywhere from '@/utils/mountAnywhere';
import './index.less';

async function doDelete(teamId) {
  const isDeleteCode = await del(api.ADAPTER_TEAMS_TEAMID.replace(':teamId', teamId));
  return isDeleteCode;
}

async function spaceRemoveAction(spaceId) {
  const isDeleteCode = await postAbort(api.SPACE_REMOVE, { spaceId });
  return isDeleteCode;
}


class DeleteTeam extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      confirmText: '',
    };
  }

  doDelete = async () => {
    const { teamId, doneCallback = () => {}, onClose, title } = this.props;
    const { confirmText } = this.state;
    if (!confirmText) return;
    if (confirmText && confirmText === intl.t('以上风险我已知悉')) {
      const isDeleteCode = title ? await spaceRemoveAction(teamId) : await doDelete(teamId);
      if (isDeleteCode === 1) {
        message.success(intl.t('删除成功'));
        doneCallback();
        onClose();
      } else if (isDeleteCode === 1151) {
        message.error(intl.t('空间已被恢复，请刷新后重试'));
      } else {
        message.error(intl.t('删除失败'));
      }
    } else {
      message.warn(intl.t('内容输入存在问题，请重新输入'));
    }
  };

  inputChange = (e) => {
    let value = e && e.target && e.target.value;
    this.setState({
      confirmText: value,
    });
  }

  render() {
    const { confirmText } = this.state;
    const { onClose, teamName, title = '' } = this.props;
    return (
      <PrimaryModal
        title={intl.t('删除团队')}
        onCancel={() => {
          onClose();
        }}
        onOk={this.doDelete}
        okText={intl.t('确认删除')}
        cancelText={intl.t('取消')}
        selfClassName={confirmText.length > 0 ? 'delete-team-modal' : 'delete-team-modal deliver-team-disabled-modal'}
      >
        <div className='delete-content'>
          <div className='delete-tips'>
            <p className='delete-tips-text-large'>
              <i className='tips-icon' />
              <span className='tips-text-span'>
                {intl.t('团队空间{name}一旦删除成功', {
                  // eslint-disable-next-line react/no-unescaped-entities
                  name: <span>"<span className='tips-text-span-red'>{teamName}</span>"</span>,
                })}
                {/* className='delete-tips-text' */}
                <span>  { title ? intl.t('空间内所有文档无法进行恢复，请谨慎操作') : intl.t('空间内所有内容将进您的回收站，10 天后自动彻底删除。如果你删除的内容中有属于他人的，其所有者将收到通知，请谨慎操作，空间关联行为较多删除后可还原内容包含：空间所在位置、空间内文件、空间内成员、空间成员权限、空间类型')}</span>
              </span>
            </p>
          </div>
          <p className='input-label'>{intl.t('请在下方输入{risk}进行再次确认', {
            risk: <span>&quot;<span>{intl.t('以上风险我已知悉')}</span>&quot;</span>,
          })}</p>
          <Input
            className='confirm-input'
            onChange={this.inputChange}
            placeholder={intl.t('以上风险我已知悉')}
          />
        </div>
      </PrimaryModal>
    );
  }
}

function deleteTeam(teamId, teamName, doneCallback, title) {
  const deleteModal = <DeleteTeam
    teamId={teamId}
    teamName={teamName}
    doneCallback={doneCallback}
    title={title}/>;
  mountAnywhere(deleteModal);
  return deleteModal;
}

export default deleteTeam;
