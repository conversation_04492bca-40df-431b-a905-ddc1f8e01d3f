.folder-tree {
  height: 100%;
  // width: 100%;
  // overflow-x: auto;
  // overflow-y: auto;
  position: relative;
  min-height: 400px;
  overflow-x: hidden;
  &:hover{
    overflow-x:auto
  }

  .ft-triangle-wrapper {
    height: 30px;
    width: 19px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  // .tb-header-container {
  //   // padding: 0 32px;
  // }

  .tb-header,
  .tb-body>li {
    height: 46px;
    line-height: 46px;
    white-space: nowrap;
    display: flex;
    align-items: center;
    color: @blueGray-4;

    >span {
      display: inline-block;
    }

    .file-name {
      width: 300px;
      min-width: 250px;
      overflow: hidden;
      // max-width: initial;
      // width: calc(100% - 410px);
      display: flex;
      align-items: center;
      flex: 1;

      .file-name-display {
        display: inline-flex;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        align-items: center;
      }

      .file-name-display-text {
        line-height: 1;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        align-items: center;
      }

      .file-name-display-count {
        line-height: 1;
        color: @blueGray-6;
        padding-left: 6px;
        font-size: 12px;
        margin-top: 2px;
      }

      >.ant-checkbox-wrapper {
        position: relative;
        top: -2px;
        margin-right: 18px;
      }
    }

    .file-resizer {
      width: 20px;
      margin-right: 8px;
      margin-left: 52px;
      position: relative;
      text-align: center;

      .resizer-line {
        width: 1px;
        height: 50px;
        background: #076BFF;
        position: absolute;
        top: 0;
        left: 50%;
      }
    }

    >.file-owner,
    >.file-time {
      width: 180px;
      min-width: 130px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      // margin-right: 70px;
      display: flex;
      align-items: center;
      color: @blueGray-4;

      :global {
        .path-ellipsis {
          cursor: default !important;
        }
      }
    }

    >.file-time {
      min-width: 100px; //time的可以更小一点
    }

    >.file-operate {
      width: 40px;
      min-width: 40px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      display: flex;
      justify-content: right;
      align-items: center;
      margin-right: 20px;
      display: inline-block;
    }

    .triangle {
      display: inline-block;
      vertical-align: middle;
      width: 4px;
      height: 7px;
      margin-right: 10px;
      background-size: 8px 8px;
      background-image: url(https://img-ys011.didistatic.com/static/cooper_cn/do1_Bw1673hz9vMmk8p48HJH);
      background-position: center center;
      background-repeat: no-repeat;
      cursor: pointer;

      &-li {
        margin-left: 4px;
      }

      &.is-open,
      &.sort {
        transform: translateY(-15%) rotate(90deg);

        &.sort-up {
          transform: rotate(270deg);
        }
      }

    }
  }

  .tb-header {
    font-size: 14px;
    font-weight: 500;
    color: #666;
    // width: max-content;//根据父级的宽度，自适应宽度，所以应该注释掉
    min-width: 100%;
    height: 36px !important;
    line-height: 36px !important;
    border-bottom: 1px solid @blueGray-11;

    .file-operate,
    .file-owner,
    .file-time,
    .file-name {
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #666666;
      line-height: 18px;

      .ant-table-column-sorter {

        .anticon-caret-up,
        .anticon-caret-down {
          font-weight: bold;
        }
      }
    }
  }

  .tb-header,
  .tb-body {

    >li.bottom-tip {
      pointer-events: none;
      border: none;
      color: @blueGray-4;
      display: flex;
      font-size: 14px;
      justify-content: center;
      margin-bottom: 32px;
    }
  }

  .tb-body {
    height: calc(100% - 68px);
    min-width: 100%;
    padding-top: 8px;
    // width: max-content;
  }

  .tb-body-row {
    // padding: 0 4px;

    &:hover {
      border-radius: 4px;
      background: @blueGray-12;
    }
  }

  :global {
    .ant-btn {
      min-width: 60px;
      width: auto;
      height: 32px;
      border: 1px solid #DDDDDD !important;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      line-height: 20px;

      &:hover,
      &:focus {
        border-color: #518dff;
        color: #518dff;
        background-color: #fff;
      }
    }
  }

  .loading-tip {
    margin-top: 14px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    font-size: 14px;

    .text {
      margin-left: 4px;
    }
  }

}

.confirm-modal {
  :global {
    .ant-modal-content {
      border-radius: 8px;
    }

    .ant-modal-confirm-title {
      color: @blueGray-color;
    }

    .ant-modal-confirm-content {
      color: @blueGray-5;
      font-size: 14px;
      margin-top: 16px;
    }
  }
}

.confirm-modal-close {
  color: @blueGray-7;
  font-size: 24px;
  position: absolute;
  top: 32px;
  right: 32px;
}

.delete-modal {
  :global {
    .ant-btn-primary {
      background-color: @error-color  !important;
      border: 1px solid @error-color  !important;
    }
  }
}

.message-link {
  color: @primary-color;
  cursor: pointer;
}