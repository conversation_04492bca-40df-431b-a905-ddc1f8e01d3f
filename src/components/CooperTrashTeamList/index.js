import classBind from 'classnames/bind';
import FoldTree from './FoldTree/index';
import { noop } from 'lodash-es';
import styles from './style.module.less';

const cx = classBind.bind(styles);

function CooperTrashTeamList({
  role = 0,
  isTeam = true,
  teamId = 0,
  folderId = 0,
  viewType,
  sortBy,
  orderAsc,
  onDragResizeEnd,
  onToggleSort,
  fileOpreationType,
  uploadCase = { triggerUpload: noop },
  setIsShowSwitchView,
  originFileType,
  switchView,
  isShowSwitchView,
  showRightBox,
}) {
  return (
    <div className={cx('cooper-trash-team-list')}>
      <FoldTree
        role={role}
        folderId={folderId}
        fileOpreationType={fileOpreationType}
        teamId={teamId}
        isTeam={isTeam}
        viewType={viewType}
        switchView={switchView}
        sortBy={sortBy}
        orderAsc={orderAsc}
        onDragResizeEnd={onDragResizeEnd}
        onToggleSort={onToggleSort}
        setIsShowSwitchView={setIsShowSwitchView}
        isShowSwitchView={isShowSwitchView}
        showRightBox={showRightBox}
        originFileType={originFileType}
      />
    </div>
  )
}

export default CooperTrashTeamList;
