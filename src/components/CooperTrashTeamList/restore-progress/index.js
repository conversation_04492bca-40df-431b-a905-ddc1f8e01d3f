import { intl } from 'di18n-react';
import React, { useEffect, useState } from 'react';
import { getReductionProgress } from '@/service/cooper/teamTrash';
import './index.less';


const MProgress = (props) => {
  const { taskId = '', type = '' } = props;
  const [restoreStatus, setRestoreStatus] = useState('');
  const _messageMap = {
    SUCCESS: intl.t('的团队空间正在还原中,请等待...'),
    COMPLETED: intl.t('团队空间还原成功'),
    FAILED: intl.t('恢复失败，请联系Cooper反馈群'),
  }

  useEffect(() => {
    restoreProgress(taskId);
  }, []);

  const sleep = (ms) => {
    return new Promise((resolve) => setTimeout(resolve, ms));
  };

  const restoreProgress = async (taskId) => {
    while (true) {
      // eslint-disable-next-line no-await-in-loop
      await sleep(500);
      // eslint-disable-next-line no-await-in-loop
      const { status } = await getReductionProgress(taskId);
      setRestoreStatus(status);
      if (status === 'COMPLETED' || status === 'FAILED') {
        break;
      }
    }
    await sleep(3000);
    props.doneCallback();
    props.onClose();
  };


  if (restoreStatus) {
    return (
      <div className='trash-reduction-progress'>
        <span className='reduction-info'>
          <img
            alt=''
            src={require('../icon/warning.png')}
            className='reductionImg' />
          <p className="reduction-info-content">
            <span className="reduction-info-title">{props.spaceName}</span>
            <span>{_messageMap[restoreStatus || type]}</span>
          </p>
        </span>
      </div>
    );
  }
  return null;
};

export default MProgress;
