.search-page-filter {
  :global {
    .ant-radio-wrapper {
      display: flex;
      align-items: center;
      line-height: 34px;
      height: 34px;
      border-radius: 6px;
      color: #222A35;
      margin: 0;
      padding: 0 12px;

      &:not(.hide-icon):hover {
        background: #F7F7F7;
      }
    }

    .ant-radio-group {
      width: 100%;
    }

    .ant-select-single .ant-select-selection-item {
      color: @primary-color;
    }

    .ant-radio {
      top: 0;
    }
  }

  .hide-icon {
    :global {
      .ant-radio {
        display: none;
      }
    }
  }

  .filter-item {
    padding: 12px 10px 20px 8px;

    &>p {
      font-weight: 400;
      font-size: 13px;
      color: rgba(34, 42, 53, 0.5);
      line-height: 18px;
      padding: 0px 12px 8px;
    }

    &:last-child {
      border-bottom: none
    }

  }

  .knowledge-name {
    width: 160px;
    height: 22px;
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;
    display: flex;
    align-items: center;
    overflow: hidden;

    span {
      .ellipsis();
      max-width: 400px;
    }

    .shaixuanIcon {
      font-size: 12px;
      margin: 0 3px;
      color: #212223;
    }
  }

  .moreDk {
    transition: all 0.2s;
    display: block;

    &.isOpen {
      transform: rotate(180deg);
    }
  }

}

.dk-switch-search-popver {
  :global {
    .ant-tooltip-content {
      transform: translate(50px, 0);
    }
  }
}