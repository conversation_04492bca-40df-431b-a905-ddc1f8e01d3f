import { intl } from 'di18n-react';
import React from 'react';
import classBind from 'classnames/bind';
import Autosuggest from 'react-autosuggest';
import debounce from 'lodash/debounce';

import { searchUser, searchTeamUser } from './action';
import { defaultAvatar } from './icon/default-avatar.jpg';
import PersonCard from '@/components/common/PersonCard';
import defaultTeamAvatar from './icon/icon_morentouxiang.svg';
const TEAM_SPACE = 'TEAM_SPACE';
import styles from './index.module.less';
const cx = classBind.bind(styles);

function getSuggestionValue(suggestion) {
  return suggestion.pinyinname;
}

function renderSuggestion(suggestion) {
  const s = Object.assign({}, suggestion, {
    dep: suggestion.dep || '',
  });

  const avatar = s.avatar || (s.type === 0 ? defaultAvatar : defaultTeamAvatar);
  return (
    <div>
      <PersonCard
        avatar={avatar}
        name={s.title || '--'}
        department={s.dep}
        mail={s.mail}
        depWidth={300}
        type={s.type}
      />
      {suggestion.existed && (
        <span className={cx('exist-tag')}>{intl.t('已添加')}</span>
      )}
    </div>
  );
}

function renderSectionTitle(section) {
  return <div className={cx('title-name')}>{section.title}</div>;
}

function getSectionSuggestions(section) {
  return section.list;
}

export default class UserSuggest extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      value: '',
      suggestions: [],
    };
    this.loadSuggestions = debounce(this.loadSuggestions, 300);
  }

  loadSuggestions = value => {
    (this.props.spaceType === TEAM_SPACE ? searchTeamUser(value, this.props.resourceId) : searchUser(value)).then(data => {
      if (data.key === this.state.value) {
        const user = data.users.filter(u => u.type === 0);
        const { existNames = [] } = this.props;
        user.forEach(u => {
          u.existed = existNames.findIndex(name => name === u.pinyinname) > -1;
        });
        const suggestions = [];
        if (user.length > 0) {
          suggestions.push({
            title: intl.t('用户'),
            list: user,
          });
        }
        this.setState({
          suggestions,
        });
      }
    });
  };
  onChange = (event, { newValue }) =>
    this.setState({
      value: newValue,
    });
  onSuggestionsFetchRequested = ({ value }) => {
    this.loadSuggestions(value);
  };
  onSuggestionsClearRequested = () => {
    this.setState({
      suggestions: [],
    });
  };
  onSuggestionSelected = (e, { suggestion }) => {
    // 先清空搜索框
    this.setState({
      value: '',
    }); // 回调父组件方法

    this.props.onSelect(suggestion);
  };

  render() {
    const { value, suggestions } = this.state;
    const { placeholder = '', spaceType } = this.props;
    const inputProps = {
      placeholder,
      value,
      onChange: this.onChange,
      onFocus: () => {
        if (spaceType === TEAM_SPACE) {
          this.loadSuggestions('');
        }
      }
    };
    return (
      <div className={cx('user-suggest')}>
        <Autosuggest
          multiSection={true}
          focusInputOnSuggestionClick={false}
          shouldRenderSuggestions={() => {
            return true;
          }}
          suggestions={suggestions}
          onSuggestionsFetchRequested={this.onSuggestionsFetchRequested}
          onSuggestionsClearRequested={this.onSuggestionsClearRequested}
          getSuggestionValue={getSuggestionValue}
          getSectionSuggestions={getSectionSuggestions}
          renderSuggestion={renderSuggestion}
          renderSectionTitle={renderSectionTitle}
          inputProps={inputProps}
          onSuggestionSelected={this.onSuggestionSelected}
        />
      </div>
    );
  }
}
