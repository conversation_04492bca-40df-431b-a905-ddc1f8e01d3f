
.transfer-collaboration {
  margin-top: 16px;
  height: 245px;
  .transfer-user-lsit {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    margin-top: 16px;
    .transfer-user-item {
      display: flex;
      width: 100%;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      .avatar-container {
        padding-right: 16px;
        .avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
        }
      }
      .transfer-user-info {
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        .transfer-user-title {
          font-size: 14px;
          color: #2F343C;
        }
        .transfer-user-mail {
          font-size: 12px;
          color: #94A0B0;
          margin-top: 3px;
        }
      }
      .transfer-user-operation {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        cursor: pointer;
        .transfer-user-delete {
          font-size: 14px;
          color: #2F343C;
          cursor: pointer;
          &:hover {
            color: #0066ff;
          }
        }
      }
    }
  }
}

.transfer-modal-title {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .transfer-modal-title-text {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #2F343C;
  }
  .transfer-modal-title-icon {
    width: 16px;
    height: 16px;
    margin-left: 5px;
    cursor: pointer;
  }
}
.ant-modal-body {
  overflow: initial !important;
}

.user-suggest {

  :global {
    .react-autosuggest {
      &__container {
        input {
          line-height: 32px;
          border-radius: 4px;
          width: 100%;
          height: 32px;
          padding: 0 8px;
          border: 1px solid #e4e9f3;
          outline: none;
          font-size: 14px;
          &::placeholder {
            font-size: 14px;
          }
        }
      }
      &__suggestions-container {
        max-height: 320px;
        overflow: auto;
        margin-top: 4px;
        background: #fefefe;
        box-shadow: 0 2px 8px 0 rgba(23,35,62,0.20);
        border-radius: 4px;
      }
      &__suggestion {
        &--highlighted {
          background: #f3f7fe;
        }
        .user-suggest {
          &__item {
            padding: 10px 10px;
            transition: background .3s ease;
            cursor: pointer;
            &:hover {
              background: #f3f7fe;
            }
          }
          &__imgwrapper {
            float: left;
            width: 40px;
            height: 40px;
            overflow: hidden;
            border-radius: 40px;
            img {
              max-width: 100%;
            }
          }
          &__content {
            margin-left: 56px;
          }
          &__name {
            line-height: 20px;
            font-size:14px;
            color: #17233e;
          }
          &__mail {
            line-height: 17px;
            height: 17px;
            margin-top: 3px;
            font-size:12px;
            color: #8a93a8;
          }
        }
      }
    }

    .react-autosuggest__container--open {
      position: relative;
    }
  
    .react-autosuggest__suggestions-container {
      margin-top: 0;
    }
  
    .react-autosuggest__suggestions-container--open {
      position: absolute;
      top: 32px;
      z-index: 1;
      width: 100%;
      max-height: 334px;
      overflow: auto;
    }

    .user-suggest__name {
      display: inline-block;
      white-space: nowrap;
      max-width: 450px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  
    .react-autosuggest__section-title {
      line-height: unset;;
    }
  
    .react-autosuggest__input {
      position: relative;
      z-index: 1;
    }
  
    .react-autosuggest__input:hover {
      border-color: #3f81ff;
    }
  
    .react-autosuggest__input:focus {
      border-color: #3f81ff;
      box-shadow: 0 0 0 2px rgba(16, 142, 233, 0.2);
    }
  
    .react-autosuggest__input::placeholder {
      color: #bec5d2;
      font-family: PingFangSC-Regular;
      font-weight: 100;
    }
  
  }

  .title-name {
    padding: 5px 10px;
    color: #8a93a8;
  }

  .exist-tag {
    background-color:rgba(#8a93a8, .1);
    color: #8a93a8;
    font-size: 12px;
    line-height: 12px;
    border-radius: 4px;
    padding: 1.5px 8px;
    position: relative;
    top: -8px;
    left: -15px;
  }
}
