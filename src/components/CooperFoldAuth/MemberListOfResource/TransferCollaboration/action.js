import { get, post } from '@/utils/request/cooper';
import Cooper<PERSON><PERSON> from '@/utils/request/api/CooperApi';

export const searchUser = (name) => {
  if (!name) {
    // XXX: 应显示最近选择过的
    return Promise.resolve([]);
  }

  return get(`${CooperApi.GET_USER_EXIT_ME}?keyword=${encodeURIComponent(name)}`).then(res => {
    const result = res || [];
    return {
      key: name,
      users: result // .slice(0, 15)
    };
  });
};

export const searchTeamUser = (name, resourceId) => {
  return get(`${CooperApi.GET_TEAM_USER_EXIT_ME.replace(':resourceId', resourceId)}?keyword=${encodeURIComponent(name)}`).then(res => {
    const result = res || [];
    return {
      key: name,
      users: result // .slice(0, 15)
    };
  });
};

export const transferTo = (resourceId, user) => {
  return post(CooperApi.TRANSFER_USER_TO.replace(':resourceId', resourceId), {
    transferTo: user.id,
    transferToMemberId: user.memberId
  });
};
