.user-perm-select-wrap{
  text-align: right;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 14px;
  color: #666666;
  &:hover {
    background-color: #E0E1E3;
  }
  .member-perm-select{
    display: flex;
    align-items: center;
    .powerChoose-content {
      text-align: right;
      border-radius: 4px;
      font-size: 12px;
      color: #4E555D;
      display: flex;
      align-items: center;
      justify-content: right;
      
      &.choose-disabled {
        color: #909499;
      }
    }
    .arrow-choose{
      display: inline-block;
      width: 12px;
      height: 12px;
      margin-left: 4px;
    }
    .arrow-choose-disabled{
      opacity: 0;
    }
  }
  .powerChoose-visible {
    .arrow-choose{
      transform: rotate(180deg);
      transition: transform .3s ease;
    }
  }

  .right-bottom{
    font-size: 10px;
    font-weight: 400;
    color: #999999;
    line-height: 14px;
    pointer-events:none;
    white-space: nowrap;
  }

}

.user-perm-select-wrap-disable{
  cursor: default;
  &:hover {
    background-color: transparent;
  }
  .right-bottom{
    padding-right: 0;
  }
}

.docs-cooperator-member-popover{
  width: 306px;
  .dropdown-content{
    .recovery{
      background:rgba(4, 127, 254, 0.08);
      padding:7px 8px !important;
      line-height: 17px;
      position: relative;
      z-index: 1;
      border-radius: 4px;
      height: auto !important;
      margin-bottom: 1px;
      &:hover{
        background: rgba(4, 127, 254, 0.14) !important;
      }
      .recovery_label{
          font-size: 12px;
          font-weight: 400;
          color: rgba(51, 51, 51, 1);
          line-height: 17px;
          text-align: justify;
        }
        .recovery_tip{
          cursor: pointer;
          position: absolute;
          right: 8px;
          font-size: 12px;
          margin-left: 11px;
          font-weight: 400;
          color: #047FFE;
          vertical-align: text-top;
        }
    }
    .split-line{
      width: 100%;
      height: 1px;
      background-color: #EEEEEF;
      margin: 10px 0;
    }
    .option-remove{
      display: flex;
      width: 100%;
      font-size: 14px;
      padding: 6px 8px;
      border-radius: 4px;
      cursor: pointer;
      color: #FF563B;

      &:hover {
        background-color: #F2F3F3;
      }
    }
   
  }

  .hide-member-perm-change{
    .ant-popover-inner{
      display: none;
    }
  }
}
