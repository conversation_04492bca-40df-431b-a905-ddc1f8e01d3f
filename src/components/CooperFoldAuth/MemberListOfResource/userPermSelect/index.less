.user-item-right{
  text-align: right;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 14px;
  color: #666666;
  &:hover {
    background-color: #E0E1E3;
  }
  .dropdown-checkbox{
    width: auto;
    height: auto;
    display: flex;
    min-width: 0;
    .dropdown-checkbox__value{
      border: none;
      background: none;
      display: flex;
      align-items: center;
      color: #505050;
      .dropdown-checkbox__caret{
        height: 17px;
        right: 4px;
      }
      span{
        height: auto;
        line-height: 20px;
        padding: 0 20px 0 0;
        font-size: 12px;
      }
    }
  }
  .right-bottom{
    font-size: 10px;
    font-weight: 400;
    color: #999999;
    line-height: 14px;
    padding-right: 20px;
    pointer-events:none;
    white-space: nowrap;
  }
}
.user-item-right-disabled{
  &:hover {
    background-color: #EFF0F2;
  }
  .dropdown-checkbox-disabled{
    .dropdown-checkbox__value{
      span{
        color: #505050 !important;
      }
    }
  }
}
.user-item-right-bottom{
  position: relative;
  &:after{
    content: '';
    display: table;
    clear: both;
  } 
  .dropdown-checkbox{
    height: 36px;
    .dropdown-checkbox__value{
      height: 18px;
    }
  }
  .right-bottom{
    position: absolute;
    right: 0;
    top: 24px;
  }
  .empty-sub-bottom{
    height: 0;
    position: relative;
    right: 0;
    top: 0;
    opacity: 0;
  }
}
.none-operate{
  .dropdown-checkbox {
    .dropdown-checkbox__value{
      span{
        padding-right: 0;
      }
    }
  }
  .right-bottom{
    padding-right: 0;
  }
}