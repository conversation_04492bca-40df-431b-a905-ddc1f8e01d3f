.change-page-owner {
  text-align: center;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 400;
  padding: 4px;
  color: #047FFE;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: #E8E9EA;
  }
  .owner-change-icon{
    font-size: 14px;
    margin-right: 2px;
  }
  .owner-change-text{
    font-size: 12px;
    display: inline-block;
  }
}

.cooper-action {
  text-align: center;
  display: flex;

  button {
    background: none;
    border: none;
    cursor: pointer;
    outline: none;
    height: 28px;
    line-height: 28px;

    &:hover {
      button {
        color: #1a75ff;
      }
    }

    &.apply-owner-btn {
      margin-left: 18px;
      color: #0066FF;

      .disabled {
        color: #94a0b0;
      }

    }
  }

  .question-icon {
    vertical-align: middle;
    width: 20px;
    height: 20px;
    border-radius: 4px;
    display: inline-block;
    margin-left: 2px;
    background: url('./icon_wenhao_circle.png') center center;
    background-size: 14px 14px;
    background-repeat: no-repeat;
    &:hover {
      background-color: #F4F4F4;
      background-image: url('./icon_wenhao_circle_hover.png');
    }
  }

  .transfer-owner {
    height: 28px;
    line-height: 28px;
    font-size: 13px;
    color: #047FFE;
  }

  &::after {
    content: '';
    display: block;
    clear: right;
  }
  :global{
    .batch-member-btn{
      margin-right: 12px;
      .batch-text,
      i{
        color: #047FFE;
      }
      &:hover{
        background-color: #E8E9EA;
      }
    }
  }
}