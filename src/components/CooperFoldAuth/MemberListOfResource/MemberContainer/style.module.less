
.resource-member-wrap {
  .list-header-input{
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;

    .search-input {
      width: 100%;
      height: 36px;
      line-height: 36px;
      border: 1px solid #D3D4D7;
      background: url('../../icon/search.png') 12px center no-repeat;
      background-size: 18px 18px;
      padding-left: 32px;
      font-size: 14px;
      display: flex;
      align-items: center;
    }
    .add-member-content{
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 12px;
      .split-line{
        margin-right: 12px;
        height: 30px;
        width: 1px;
        background-color: #F2F3F3;
      }
    }

  }

  .add-collaborator {
    margin-left: 10px;
    height: 36px;
    border-color: @blueGray-9;
    padding: 0 12px;

    i {
      color: @blueGray-15;
      margin-right: 4px !important;
    }
  }

  .add-collaborator:hover {
    border-color: @primary-color;
    color: @primary-color;
    i {
      color: @primary-color;
    }
  }

  .add-collaborator-disabled {
    margin-left: 10px;
    padding: 0 12px;

    i {
      margin-right: 4px !important;
    }

    button {
      height: 36px;
    }
  }
  

  .list-wrap{
    .list-wrap-header{
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 14px;
      .list-header-title{
        font-size: 14px;
        font-weight: 500;
        color: #222A35; 
        line-height: 22px;

        i {
          font-size: 20px;
          margin-right: 4px;
          cursor: pointer;
        }
      }
    }
    .list-wrap-content{
      position: relative;
      max-height: 442px;
      min-height: 200px;
      overflow: auto;
      margin-left: -8px;
      margin-right: -8px;
      .no-search-result {
        font-size: 14px;
        color: #999999;
        font-weight: 400;
        line-height: 200px;
        text-align: center;
      }
    }
  }
}
  