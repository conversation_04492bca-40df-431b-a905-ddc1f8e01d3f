import React, { useState, useMemo, useEffect, useRef, Fragment } from 'react';
import { message,Button } from 'antd';
import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import TeamContext from '@/components/CooperFoldAuth/TeamContext';
import { getResourceAuth, getOperationAuth } from '@/service/cooper/home';
import BatchManagement from '../BatchManagement';
import InheritContent from './InheritContent';
import MemberContainer from './MemberContainer';
import { getUserRole } from './service';
import PermissionsPop from '@/components/PermissionsPop';
import { parsePerm } from '@/components/CooperFoldAuth/util';
import { ROLE_TYPE, ROLE_TYPE_DOC, ROLE_TYPE_SPACE } from './constants';
import OperatePermContent from './OperatePermContent';
import styles from './style.module.less';
import { sendFileTypeEvent } from '@/utils/type'

const cx = classBind.bind(styles);

const permConfigDoc = () => {
  return {
    header: null,
    body: [
      {
        key: {
          text: intl.t('查看'),
          row: 1,
        },
        value: {
          text: intl.t('可查看/评论，不能编辑'),
          row: 1,
        },
      },
      {
        key: {
          text: intl.t('编辑'),
          row: 1,
        },
        value: {
          text: intl.t('可查看/评论/编辑，不能分享给他人'),
          row: 1,
        },
      },
      {
        key: {
          text: intl.t('下载'),
          row: 1,
        },
        value: {
          text: intl.t('可查看/下载，不能编辑'),
          row: 1,
        },
      },
      {
        key: {
          text: intl.t('管理员'),
          row: 1,
        },
        value: {
          text: intl.t('可查看/编辑/评论/下载/分享给他人'),
          row: 1,
        },

      },
      {
        key: {
          text: [intl.t('无权限')],
          row: 1,
        },
        value: {
          text: intl.t('不可访问'),
          row: 1,
        },
      },
    ],
  }
}

const permConfigDir = () => {
  return {
    header: null,
    body: [
      {
        key: {
          text: intl.t('查看'),
          row: 1,
        },
        value: {
          text: intl.t('可查看/评论，不能编辑'),
          row: 1,
        },
      },
      {
        key: {
          text: intl.t('编辑'),
          row: 1,
        },
        value: {
          text: intl.t('可查看/评论/编辑，不能分享给他人'),
          row: 1,
        },
      },
      {
        key: {
          text: intl.t('分享'),
          row: 1,
        },
        value: {
          text: intl.t('可分享文件夹和本地上传的文件'),
          row: 1,
        },
      },
      {
        key: {
          text: intl.t('上传'),
          row: 1,
        },
        value: {
          text: intl.t('可在文件夹内上传、新建文件'),
          row: 1,
        },
      },
      {
        key: {
          text: intl.t('下载'),
          row: 1,
        },
        value: {
          text: intl.t('可查看/下载，不能编辑'),
          row: 1,
        },
      },
      {
        key: {
          text: intl.t('管理员'),
          row: 1,
        },
        value: {
          text: intl.t('可查看/编辑/评论/下载/分享给他人'),
          row: 1,
        },

      },
      {
        key: {
          text: [intl.t('无权限')],
          row: 1,
        },
        value: {
          text: intl.t('不可访问'),
          row: 1,
        },
      },
    ],
  }
}

const MemberListOfResource = ({
  refreshFileOrCrumb, // 更新文件列表
  closeFoldAuthModal,
  resourceId,
  teamId,
  folderName,
  goRootFolder,
  resourceType,
  isPersonalTeam,
  isInOperate,
  showBack,
  handleBack,
  gotoNext,
  info,
  toggleChildVisible,
  permissionsPopHover = false,
  showTransferable = true
}) => {
  const [userRole, setUserRole] = useState(null); // 当前登录用户对于当前资源的角色
  const [spaceRole, setSpaceRole] = useState(null);
  const [permInfo, setPermInfo] = useState({});
  const [isInherit, setIsInherit] = useState(null);
  const [totalCount, setTotalCount] = useState(null);
  const [currUserPermText, setCurrentUserPermText] = useState(null);

  const childMemberContainerRef = useRef(null);

  const childInheritContainerRef = useRef(null);

  const ROLE_MAP = useMemo(() => {
    if (resourceType === 'dir') return ROLE_TYPE;
    if (resourceType === 'doc') return ROLE_TYPE_DOC;
    if (resourceType === 'space') return ROLE_TYPE_SPACE;
  }, [resourceType]);

  const RoleTextGlobal = {
    SPACE_OWNER: () => intl.t('管理员'),
    owner: () => intl.t('管理员'),
    RESOURCE_OWNER: () => intl.t('管理员'),
    admin: () => intl.t('管理员'),
    RESOURCE_ADMIN: () => intl.t('管理员'),
    member: parsePerm,
    RESOURCE_MEMBER: parsePerm,
    noperm_member: () => intl.t('无权限成员'),
    RESOURCE_NOPERM_MEMBER: () => intl.t('无权限成员'),
  }

  const getUserRoleForFold = () => { // 获取当前登录用户在空间的角色
    getUserRole({
      spaceId: teamId,
      resourceId,
    }).then((res) => {
      if (res.spaceOwner) {
        setUserRole(ROLE_TYPE.SuperAdmin);
      } else {
        setUserRole(res.resourceRole);
      }
      setSpaceRole(res.spaceRole);
    }).catch(() => {
      message.error(intl.t('获取权限失败'));
      setUserRole('');
    });
  };

  const getUserPerm = () => { // 获取当前登录用户的资源权限
    getOperationAuth(resourceId)
      .then((res) => {
        const { perm, roleKey } = res;
        if (roleKey === ROLE_TYPE.Member
          || roleKey === ROLE_TYPE_DOC.Member
          || roleKey === ROLE_TYPE_SPACE.Member
        ) {
          const permObject = RoleTextGlobal[roleKey](perm);
          const permText = permObject.map((i) => {
            return i.label
          }).join('/')
          setCurrentUserPermText(permText);
        } else {
          setCurrentUserPermText(intl.t('管理员')); // 所有者 和管理员显示“管理员”
        }
      })
      .catch(() => {
        message.error(intl.t('获取权限失败'));
      });
  }

  const getUserPermNew = () => { // 获取当前登录用户的资源权限
    getResourceAuth(resourceId)
      .then((res) => {
        const { perm, roleKey } = res;
        if (roleKey === ROLE_TYPE.Member
          || roleKey === ROLE_TYPE_DOC.Member
          || roleKey === ROLE_TYPE_SPACE.Member
        ) {
          const permObject = RoleTextGlobal[roleKey](perm);
          const permText = permObject.map((i) => {
            return i.label
          }).join('/')
          setCurrentUserPermText(permText);
        } else {
          setCurrentUserPermText(intl.t('管理员')); // 所有者 和管理员显示“管理员”
        }
        setSpaceRole((res.spaceRole || []).toString());

        if ((res.spaceRole || []).toString() === 'SPACE_OWNER') {
          setUserRole(ROLE_TYPE_DOC.SuperAdmin);
        } else {
          setUserRole(res.roleKey);
        }
        setPermInfo(res);
      })
      .catch(() => {
        message.error(intl.t('获取权限失败'));
      });
  }

  useEffect(() => {
    if (!isPersonalTeam) {
      getUserRoleForFold();
    }

    // 更多操作里的协作文档，才需调用此接口
    if (isInOperate) {
      getUserPermNew();
    }  else {
      getUserPerm();
    }
  }, []);

  // 入口权限
  // 文件夹：
  // 空间所有者、文件夹管理员（必须是空间成员）

  // 文档：
  // 空间所有者、文档所有者（可以不是空间成员）、文档管理员（可以不是空间成员）
  const hasManagePerm = useMemo(() => {
    if (resourceType === 'dir') {
      return (
        userRole === ROLE_MAP.SuperAdmin
        // || userRole === ROLE_MAP.Owner
        || userRole === ROLE_MAP.Admin
      );
    }
    if (resourceType === 'doc') {
      return (
        userRole === ROLE_MAP.SuperAdmin
        || userRole === ROLE_MAP.Owner
        || userRole === ROLE_MAP.Admin
      );
    }
  }, [resourceType, userRole, spaceRole, ROLE_MAP])

  // 文件夹的超管和普管有权限 SuperAdmin:空间所有者
  const showInherit = useMemo(() => {
    if (isPersonalTeam) return false;
    return hasManagePerm;
  }, [hasManagePerm]);

  // 不继承;  只有文件夹显示添加成员入口，文档不显示
  const showAddMemberBtn = useMemo(() => {
    if (resourceType !== 'dir') return false;
    return isInherit === false && hasManagePerm;
  }, [isInherit, hasManagePerm, resourceType]);

  // 同继承
  const showBatchMemberBtn = useMemo(() => {
    return hasManagePerm;
  }, [hasManagePerm]);

  // 更新用户列表
  const updateUserList = () => {
    if (childMemberContainerRef.current) {
      childMemberContainerRef.current.loadMoreCurrList({ resourceId }, true);
    }
  }

  return (
    <TeamContext.Provider value={{ resourceId, teamId, closeFoldAuthModal, goRootFolder, userRole, resourceType }}>
      <div
        id='docs-cooperator-list'
        className={cx('member-list-of-resource', {
          'member-list-of-resource-modal': isInOperate
        })}
      >
        <div className={cx('list-header')}>
          {
            isInOperate ? (
              <div className={cx('list-header-title')}>
                { showBack && <i className={cx('dk-iconfont dk-icon-fanhuiyemian')} onClick={handleBack} />}
                { intl.t('协作者') }
              </div>
            ) : (
              <div className={cx('list-header-title')}>
                { intl.t('协作者') }
                { totalCount > 0 && `· ${totalCount}` }
              </div>
            )
          }
          <div className={cx('title-action')}>
            {/* {
              showBatchMemberBtn && (
                <Fragment>
                  <BatchManagement
                    resourceId={resourceId}
                    resourceType={resourceType}
                    teamId={teamId}
                    callback={() => {
                      updateUserList();
                    }}
                    isInherit={isInherit}
                  />
                  {showInherit && <div className={cx('split-line')} />}
                </Fragment>
              )
            } */}
            {showInherit && (
              <InheritContent
                resourceId={resourceId}
                resourceType={resourceType}
                teamId={teamId}
                ref={childInheritContainerRef}
                spaceRole={spaceRole}
                updateInheritStatus={setIsInherit}
                callback={() => {
                  updateUserList();
                }}
              />
            )}
            {
              isInOperate && hasManagePerm && (
                <Button className={cx('add-collaborator')} onClick={() => {
                  gotoNext();
                  sendFileTypeEvent('ep_collaborator_add_ck', info?.fileType);
                }}>
                  <i className='dk-iconfont dk-icon-tianjiachengyuan2' />
                  {intl.t('添加协作者')}
                </Button>
              )
            }
          </div>
        </div>

        <div className={cx('list-body-area')}>
          <MemberContainer
            showBack={showBack}
            info={info}
            isInOperate={isInOperate}
            updateTotalCount={setTotalCount}
            showAddMemberBtn={showAddMemberBtn}
            hasManagePerm={hasManagePerm}
            ref={childMemberContainerRef}
            userRole={userRole}
            resourceType={resourceType}
            spaceRole={spaceRole}
            parentText={childInheritContainerRef.current && childInheritContainerRef.current.parentText}
            gotoNext={gotoNext}
            handleBack={handleBack}
          />

        </div>
        <div className={cx('list-footer')} >
          <div className={cx('user-perm-show')}>
            <span>{intl.t('我的权限：')}{currUserPermText}</span>
            <PermissionsPop
              permissionConfig={resourceType === 'dir' ? permConfigDir() : permConfigDoc()}
              title={intl.t('成员权限说明')}
              width={351}
              hiddenText={true}
              isHasHover={permissionsPopHover}
            />
          </div>
          {
            (isInOperate || showBatchMemberBtn) && (
              <OperatePermContent
                spaceRole={spaceRole}
                toggleChildVisible={toggleChildVisible}
                userRole={userRole}
                info={{
                  ...info,
                  spaceType: permInfo.spaceType,
                  creatorOrgMemberId: permInfo.ownerMemberId,
                  createBy: permInfo.ownerInfo?.ldap,
                }}
                isDk={false}
                resourceId={resourceId}
                resourceType={resourceType}
                showBatchMemberBtn={showBatchMemberBtn}
                showInherit={showInherit}
                showTransferable={showTransferable}
                callback={() => {
                  updateUserList();
                  // onCloseUserModal();
                }}
              />
            )
          }
          
        </div>
      </div>
    </TeamContext.Provider>
  );
};

export default MemberListOfResource;
