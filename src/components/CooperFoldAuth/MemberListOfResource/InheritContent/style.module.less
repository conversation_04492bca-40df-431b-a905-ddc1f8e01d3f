.inherit-set-btn {
    padding: 6px 8px;
    border-radius: 4px;
    max-width: 200px;
    border: none;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    // border: 1px solid transparent;
    // border: 1px solid rgba(0,102,255,0.7);
    // background: url('../../icon/inherit.png') center center no-repeat;
    background-size: 100%;
    &:hover{
        background-color: #E8E9EA;
    }
    .set-icon {
      font-size: 14px;
      margin-right: 4px;
      color: #222A35;
    }
  
    .set-text {
      font-size: 12px;
      color: #4e555d;
      line-height: 16px;
      margin-right: 8px;
      font-weight: normal;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    i{
        color: #4e555d;
    }
  }

  .disabled{
    cursor: not-allowed;
    &:hover {
        opacity: 0.4;
    }
  }

.inherit-set-wrap{
    padding: 10px;
    .inherit-content{
        width: 100%;
        .container{
            cursor: pointer;
            width: 260px;
            border-radius: 6px;
            border: 1px solid #E8E9EA;
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            padding: 13px 18px;
            margin-bottom: 10px;
            .container-left{
                width: 16px;
                height: 16px;
                margin-right: 10px;
            }
            .container-right{
                display: flex;
                flex:1;
                flex-direction: column;
                .right-title{
                    font-size: 15px;
                    font-weight: 500;
                    color: #222A35;
                    line-height: 16px;
                    margin-bottom: 5px;
                }
                .right-tip{
                    font-size: 12px;
                    color:#909499;
                    line-height: 16px;
                }
            }
        }

        .change-container-inherit{
            border: 1px solid rgba(0,102,255,0.7);
            background: url('../../icon/inherit.png') center center no-repeat;
            background-size: 100%;
        }
        .change-container-noninherit{
            border: 1px solid rgba(0,102,255,0.7);
            background: url('../../icon/non_inheritance.png') center center no-repeat;
            background-size: 100%;
        }
       
    }
    .container-footer{
        cursor: pointer;
        color: #656A72;
        line-height: 16px;
        font-size: 12px;
        padding: 6px 8px;
        display: inline-block;
        border-radius: 4px;

        &:hover{
            background: #F2F3F3;
        }
        .arrow-perm{
            font-size: 12px;
            margin-left: 2px;
            color: #656A72;
        }
    }
    
}


