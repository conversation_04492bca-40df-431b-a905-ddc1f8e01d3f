.add-auth-members {
  :global{
    .ant-modal-header{
      border-bottom: none;
      border-radius: 8px;
      padding: 20px 24px 14px 24px;
      .ant-modal-title{
        font-size: 20px;
        line-height: 28px;
      }
    }
    .ant-modal-content {
      padding: 0;
      border-radius: 6px;
    }
      .ant-modal-body {
      padding: 0 24px 24px;
    }
  }

  .addMember {
    .search-input {
      width: 100%;
      height: 36px;
      line-height: 36px;
      border-radius: 4px;
      border: 1px solid @blueGray-9;
      background: url('./search.png') 12px center no-repeat;
      background-size: 18px 18px;
      padding-left: 32px;
      font-size: 14px;
      margin-bottom: 14px;
      &:focus {
        border: 0.5px solid #7EB2FF;
        box-shadow: 0 0 0 1.5px rgba(8, 143, 252, 0.1);
      }
    }
    .memberList-title {
      margin: 5px 0 16px;
      height: 22px;
      font-size: 16px;
      font-weight: 500;
      color: #2F343C;
      line-height: 22px;
    }
    .footer {
      margin-top: 22px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .num {
        font-size: 14px;
        color: #2F343C;
      }
      .btnArea {
        display: flex;
        .btn {
          width: 90px;
          height: 34px;
          border-radius: 6px;
          text-align: center;
          font-size: 14px;
          &.cancel {
            cursor: pointer;
            margin-right: 12px;
            color: #333333;
            border: 1px solid #DDDDDD;
          }
          &.ant-btn-clicked:after {
            display: none;
          }
          &.confirm {
            background: #333333;
            color: #FFFFFF;
            border: none;
            &[disabled] {
              background: #AAAAAA;
            }
          }
        }
      }
    }
    .searchResult {
      height: 278px;
      position: relative;
    }
  }
}