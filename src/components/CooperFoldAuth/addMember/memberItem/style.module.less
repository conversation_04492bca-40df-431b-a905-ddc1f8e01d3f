.addMember-member-item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .pic {
    margin-right: 14px;
    width: 36px;
    height: 36px;
    border-radius: 50%;
  }
  .info {
    flex:1;
    .nameArea {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      height: 14px;
      font-size: 14px;
      color: #111111;
      .name {
        line-height: 14px;
        .ellipsis();
        max-width: 90px;
      }
      .tag {
        margin-left: 6px;
        padding: 0 2px;
        height: 16px;
        line-height: 16px;
        background: rgba(0, 102, 255, 0.08);
        border-radius: 4px;
        font-size: 12px;
        .word {
          display: block;
          color: #0066FF;
          transform-origin: center center;
          transform: scale(0.833333333333334);
        }
      }

      .tag-quit-self{
        background:rgba(244, 244, 245, 1);
        .word{
          color: #666666;
        }
      }

      .tag-self{
        padding: 0 5px;
      }
      .out-yellow {
        background-color: rgba(241, 139, 0, 0.1);
        .word{
          color: #F18B00;
        }
      }
    }
    .mail {
      height: 10px;
      font-size: 12px;
      color: #999999;
      line-height: 10px;
      word-break: keep-all;
    }
  }
  .powerChoose{
    text-align: right;
    cursor: pointer;
    height: 28px;
    line-height: 28px;
    padding: 0 26px 0 10px;
    border-radius: 4px;
    font-size: 14px;
    color: #666666;
  }
  .disabled {
    padding-right: 4px;
    color: #999999;
    background: none;
    cursor: not-allowed;
  }
  
  
}

.quit-member {
  opacity: 0.5;
}