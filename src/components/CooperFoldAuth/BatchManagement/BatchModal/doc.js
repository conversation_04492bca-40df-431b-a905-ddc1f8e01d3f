import React, { useState,  useEffect, Fragment } from 'react';
import { Modal, Button, Input, Spin, Checkbox, Popover, message } from 'antd';
import { intl, getLocale } from 'di18n-react';
import InfiniteScroll from 'react-infinite-scroller';
import MemberList from '../memberList';
import useLoadMore from '../useLoadMore';
import { debounce } from 'lodash-es';
import { BatchChangeRoleForPerm, BatchChangeRoleForAdmin, BatchDeleteMember } from '../batchManageDoc';
import { getCooperators } from '../../MemberListOfResource/service';
import EmptyInfo from '../emptyInfo';
import { TAG_TYPE, ROLE_TYPE, ROLE_TYPE_DOC } from '@/components/CooperFoldAuth/MemberListOfResource/constants';
import DropdownCheckbox from '@/components/common/DropdownCheckbox';
import permUtil from '@/utils/perm';
import { getApolloConfig } from '@/utils/ab';
import './index.less';

const BatchModal = (props) => {
  const { visible, onClose, callback, resourceId, changeCooperTotalCount, teamId, userRole} = props;

  const [keyword, setKeyword] = useState('');
  const [selectedList, setSelectedList] = useState([]);
  const [batchPlaceholder, saveBatchPlaceholder] = useState(intl.t('请选择'));
  const [batchValue, saveBatchValue] = useState([1]);
  const [allCheckedValue, setAllCheckedValue] = useState(false);
  const [formatCurrList, setFormatCurrList] = useState([])
  const [confirmLoading, setConfirmLoading] = useState(false)
  const [linkConf, setLinkConf] = useState({});
  const [isRemoveMemberVisible, setIsRemoveMemberVisible] = useState(false);
  const {
    loadMore,
    loading,
    list,
    hasMore,
  } = useLoadMore((args) => getCooperators({ keyword, resourceId, ...args }), { keyword: '',  pageSize: 50 });

  const PermissionOptions = [
    {
      label: intl.t('查看'),
      id: 1,
      value: '1',
      perm: 1,
      desc: intl.t('用户可以查看在线协作文档'),
      disabled: true,
    },
    {
      label: intl.t('编辑'),
      id: 2,
      value: '2',
      perm: 2,
      desc: intl.t('用户可以对协作文档进行修改和评论'),
    },
    {
      label: intl.t('下载'),
      id: 32,
      value: '32',
      perm: 32,
      desc: intl.t('用户可以将协作文档导出'),
    },
  ];
  
  const isSpace = teamId !== 0;
  const PopoverContent = (text, type) => {
    return (
      <div className="popoverContent">
        {intl.t(text)}，
        <span className="link" onClick={() => {
          // eslint-disable-next-line no-unused-vars, camelcase
          const { batch_up_link_zh, batch_up_link_en } = linkConf;
          // eslint-disable-next-line camelcase
          const url = getLocale() === 'zh-CN' ? batch_up_link_zh : batch_up_link_en;
          url && window.open(url);
        }}>{intl.t('查看其他解决方案')}</span>
        {
          type === 1 && <i
            className='dk-iconfont dk-icon-guanbi1'
            onClick={() => {
              message.destroy();
            }}/>
        }
      </div>
    );
  };
  // 将权限转为字符串
  const getPermsString = (role, permList) => {
    if (role === ROLE_TYPE_DOC.Admin || role === ROLE_TYPE_DOC.Owner) {
      return intl.t('管理员');
    }
    if (role === ROLE_TYPE_DOC.NoPerm) {
      return intl.t('无权限成员');
    }

    const permStringList = PermissionOptions.filter((item) => {
      return permList.includes(item.perm);
    });

    return permStringList.map((item) => item.label).join('/');
  }
  // 判断当前是否可以操作
  // const isShowEditBox = (userRole, role) => {
  //   // 登陆人是空间普通管理者 且 当前人是管理者 此时是不能操作的
  //   switch (userRole) {
  //     case ROLE_TYPE.SuperAdmin: return role === ROLE_TYPE.SuperAdmin;
  //     case ROLE_TYPE.Admin: return role === ROLE_TYPE.SuperAdmin || role === ROLE_TYPE.Admin;
  //     default: return true;
  //   }
  // };
  // 数据再加工
  const formatMembers = (members) => {
    const memberList = members.map((u) => {
      let perms = [];
      for (const p of permUtil.permValues) {
        if (p & u.permission) perms.push(p);
      }
      const permList = perms
      // 对权限进行处理
      const permsString = getPermsString(u.role, permList);
      // 标识是不是单独授权
      const canRemove = u.tag.find(ele => ele === TAG_TYPE.MEMBER) || false
      const m = {
        ...u,
        permsString,
        canRemove,
      };
      // 超出权限外的不能操作
      m.isDisable = !m.canOperate;
      return m;
    });
    return memberList;
  };

 

  useEffect(() => {
    setFormatCurrList(formatMembers(list, userRole))
  }, [userRole, list])
 

  // 每当弹窗打开的时候需要去清空数据
  const init = () => {
    setKeyword('')
    setAllCheckedValue(false)
    setSelectedList([])
    saveBatchValue([1])
    saveBatchPlaceholder(intl.t('查看'))
    loadMore({ keyword: '' }, true)
  }
  useEffect(() => {
    message.destroy()
    init();
    if (props.visible) {
      getApolloConfig('cooper_link-conf').then(res => {
        setLinkConf(res);
      });
    }
  }, [props.visible])

  const onInputChange = debounce((value) => {
    setKeyword(value)
    loadMore({ keyword: value }, true)
  }, 500);
  // 全选
  const onCheckAllChange = (e) => {
    if (e.target.checked) {
      if (formatCurrList.filter((item) => !item.isDisable).length > 1000) {
        message.destroy()
        message.warning(PopoverContent('当前最多支持同时选中1000个人', 1), 0);
        setAllCheckedValue(false)
      } else {
        setSelectedList(formatCurrList.filter((item) => !item.isDisable))
        setAllCheckedValue(true)
      }
    } else {
      setSelectedList([])
      setAllCheckedValue(false)
    }
  }

  useEffect(() => {
    if (selectedList.length === formatCurrList.filter((item) => !item.isDisable).length) {
      setAllCheckedValue(true)
    }
    if(selectedList.length === 0) {
      setAllCheckedValue(false)
    }
  }, [selectedList.length])

  // 勾选单个
  const singleCheckChange = (e, memberInfo) => {
    if (e) {
      if (selectedList.length >= 1000) {
        message.destroy()
        message.warning(PopoverContent('当前最多支持同时选中1000个人', 1), 0);
      } else {
        setSelectedList([...selectedList, memberInfo])
      }
    } else {
      setSelectedList(selectedList.filter((item) => item.orgMemberId !== memberInfo.orgMemberId))
    }
  }
  // 移除单个勾选内容
  const removeSingleItem = (memberInfo) => {
    const memberList = selectedList.filter((item) => item.orgMemberId !== memberInfo.orgMemberId)
    setSelectedList(memberList)
  }

  // 改变权限的时候
  const changeBatchPerms = (value) => {
    const v = value[0] === 1 ? value : [1].concat(value);
    saveBatchValue(v);
    saveBatchPlaceholder('');
  };

  const apiCallback = (isError, isDelete = false) => {
    setConfirmLoading(false)
    setIsRemoveMemberVisible(false)
    if(!isError) {
      message.success(intl.t('更新权限成功'));
      callback()
      onClose()
      if(isDelete) {
        getCooperators({ keyword: '', resourceId, pageSize: 50, pageNum: 0 }).then(res => {
          const { totalCount } = res
          changeCooperTotalCount(totalCount)
        })
      }
    }
  }
  // 修改权限
  const onHandleChangePerms = (value) => {
    if (value.length > 0) {
      setConfirmLoading(true)
      let permNum = null;
      value.forEach((v) => {
        permNum += v;
      });
      BatchChangeRoleForPerm(resourceId, selectedList, permNum, apiCallback)
    } else {
      if(batchPlaceholder === intl.t('移除成员')) {
        if(isSpace) {
          setIsRemoveMemberVisible(true)
        } else {
          setConfirmLoading(true)
          BatchDeleteMember(resourceId, selectedList, apiCallback)
        }
      } else {
        setConfirmLoading(true)
        let type = batchPlaceholder === intl.t('设为管理员') ? 'add' : 'remove';
        BatchChangeRoleForAdmin(resourceId, selectedList, type, apiCallback)
      }
    }
  };

  // 设置操作项有哪些
  const getBatchActions = (allCheck) => {
    const res = [];
    const opt = allCheck
      ? {
        label: intl.t('取消管理员'),
        handle: () => {
          saveBatchPlaceholder(intl.t('取消管理员'));
          saveBatchValue([]);
        },
      }
      : {
        label: intl.t('设为管理员'),
        handle: () => {
          saveBatchPlaceholder(intl.t('设为管理员'));
          saveBatchValue([]);
        },
      };
    // 超管可以设置管理员
    if (userRole === ROLE_TYPE.SuperAdmin && isSpace) res.push(opt);
    // 移除成员
    res.push({
      label: intl.t('移除成员'),
      handle: () => {
        saveBatchPlaceholder(intl.t('移除成员'));
        saveBatchValue([]);
      },
    });
    return res;
  };
  const getRemovePopoverContent = () => {
    return (
      <div className='remove-popover-content'>
        <div className='remove-popover-content-title'>
          <i className='dk-iconfont dk-icon-a-tishi2'/>
          <span className='remove-popover-content-title-text'>
            {intl.t('从空间或父级文件夹继承成员暂不支持移除')}
          </span>
        </div>
        <div className='remove-popover-content-footer'>
          <Button
            size='small'
            onClick={() => {
              setIsRemoveMemberVisible(false)
            }}>
            {intl.t('取消')}
          </Button>
          <Button
            type='primary'
            onClick={() => {
              setConfirmLoading(true)
              BatchDeleteMember(resourceId, selectedList, apiCallback)
            }}
            size='small' >{intl.t('确认')}</Button>
        </div>
      </div>
    )
  }
  // const allCheckAdmin = !selectedList.find((m) => m.role === ROLE_TYPE.Member);
  const allCheckAdmin = () => {
    if (selectedList.length === 0) return false
    return !selectedList.find((m) => m.role === ROLE_TYPE.Member)
  }
  return (
    <Fragment>
      <Modal
        visible={visible}
        title={intl.t('批量管理')}
        footer={null}
        zIndex={2000}
        width={800}
        key={visible}
        onCancel={() => {
          onClose();
          setIsRemoveMemberVisible(false);
          setAllCheckedValue(false);
          setConfirmLoading(false);
          setKeyword('');
        }}
        maskClosable={false}
        className="batch-modal"
      >
        <div className="batch-modal-content">
          <div className="batch-modal-content-left">
            <div className="batch-modal-content-left-input">
              <Input
                onChange={(e) => {
                  onInputChange(e.target.value);
                }}
                placeholder={intl.t('搜索协作成员')}
              />
            </div>
            {
              !keyword && (
                <div className="list-wrap-title">
                  <div className="list-wrap-title-left">
                    <Checkbox
                      checked={allCheckedValue}
                      className="list-wrap-title-left-checkbox"
                      onChange={(e) => onCheckAllChange(e)}> 
                        <span className="list-wrap-title-left-text">
                          {intl.t('全选')}
                        </span>
                        <span className="list-wrap-title-left-tips">
                          ({intl.t('最多选择1000个')})
                        </span>
                    </Checkbox>
                  </div>
                  <div className="list-wrap-title-right">
                    <Popover
                      placement="top"
                      content={PopoverContent('当前仅支持一次性选中1000人', 2)}
                      zIndex={9999}
                      overlayClassName="list-wrap-title-popover"
                    >
                      <div className="list-wrap-title-right-content">
                        <span className="list-wrap-title-right-tips">
                          {intl.t('超出选择数量怎么办')}?
                        </span>
                      </div>
                    </Popover>
                  </div>
                </div>
              )
            }
            <div className={`list-wrap ${keyword ? 'search-list' : ''}`}>
              {
                list && list.length > 0 ? (
                  <InfiniteScroll
                  initialLoad={false}
                  pageStart={0}
                  loadMore={loadMore}
                  hasMore={hasMore}
                  useWindow={false}
                  getScrollParent={() => document.querySelector('.batch-modal-content-left .list-wrap')}
                >
                  <MemberList
                    list={formatCurrList}
                    selectedList={selectedList}
                    singleCheckChange={singleCheckChange}
                  />
                </InfiniteScroll>
                ) : loading ? <Spin loading={loading} /> : <EmptyInfo desc={intl.t('暂无搜索结果')} />
              }
            </div>
          </div>
          <div className="batch-modal-content-right">
            <div className="list-wrap-title">
              {`${intl.t('已选')} ${selectedList.length}/1000`}
            </div>
            <div className="list-wrap">
              <MemberList
                list={selectedList}
                removeSingleItem={removeSingleItem}
                type="selected" />
            </div>
          </div>
        </div>
        <div className="batch-modal-footer">
          <div className="batch-modal-footer-left">
            {intl.t('批量修改权限为')}：
            <DropdownCheckbox
              options={PermissionOptions}
              placeholder={batchPlaceholder}
              actions={getBatchActions(allCheckAdmin())}
              value={batchValue}
              newStyle={true}
              onChange={(value, newOptions) => changeBatchPerms(value, newOptions)}
            />
          </div>
          <div className="batch-modal-footer-right">
            <Button
              className="batch-modal-footer-right-btn"
              onClick={onClose}>
              {intl.t('取消')}
            </Button>
            <Popover
              placement="topRight"
              content={getRemovePopoverContent()}
              visible={isRemoveMemberVisible && isSpace}
              zIndex={9999}
            >
              <Button
                disabled={ batchPlaceholder === intl.t('请选择') || selectedList.length === 0 }
                className="batch-modal-footer-right-btn"
                type="primary"
                loading={confirmLoading}
                onClick={() => {
                  onHandleChangePerms(batchValue);
                }}
            >
                {intl.t('确认修改')}
              </Button>
            </Popover>
          </div>
        </div>
      </Modal>
    </Fragment>
  );
};

export default BatchModal;