.batch-modal {
  height: 630px !important;
  min-height: 630px !important;
  .ant-modal-content {
    width: 800px;
    min-width: 800px;
    border-radius: 8px;
    overflow: hidden;
    .ant-modal-header {
      padding: 20px 24px 12px !important;
      border-bottom: none !important;
    }
  }

  .ant-modal-body {
    padding: 0 !important;

    .batch-modal-content {
      border: 1px solid #EEEEEF;
      margin: 0 24px 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 6px;
      height: 494px;
      &-left {
        width: 432px;
        min-width: 432px;
        border-right: 1px solid #eee;
        padding: 12px 0;
        height: 494px;
        &-input {
          padding: 0 14px;
          margin-bottom: 10px;
          input {
            height: 24px;
            width: 100%;
            background: url('../icon/search.png') 0px 5px no-repeat;
            background-size: 16px 16px;
            padding-left: 20px;
            &::placeholder {
              color: #BDC0C3;
            }
          }
        }
        .list-wrap-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 16px;
          padding: 0 10px 0 16px;
          margin-bottom: 4px;
          &-left {
            display: flex;
            align-items: center;
            &-checkbox {
              .ant-checkbox .ant-checkbox-inner {
                border-radius: 3px !important;
              } 
              span:last-child {
                padding: 0px;
              }
            }
            &-text {
              font-family: PingFang SC;
              color: #222A35;
              font-weight: 400;
              font-size: 14px;
              padding: 0 8px;
            }

            &-tips {
              font-size: 12px;
              color: #909499;
            }
          }

          &-right {
            &-content {
              cursor: pointer;
              height: 20px;
              padding: 0 4px;
              border-radius: 4px;
              display: flex;
              align-items: center;
              font-size: 12px;
              color: #656A72;
            }

            &:hover {
              background-color: #F2F3F3;
              color: #656A72;
              border-radius: 4px;
            }
          }
        }

        .list-wrap {
          height: 406px;
          min-height: 406px;
          overflow: auto;
          position: relative;
        }
        .search-list {
          height: 426px;
          min-height: 426px;
          overflow: auto;
          position: relative;
        }
      }

      &-right {
        width: 318px !important;
        min-width: 318px !important;
        padding: 12px 8px;
        height: 494px;
        .list-wrap-title {
          height: 16px;
          line-height: 14px;
          font-size: 12px;
          color: #999;
          padding: 0 8px;
        }
        .list-wrap {
          height: 448px;
          min-height: 448px;
          overflow: auto;
          position: relative;
        }
      }
    }

    .batch-modal-footer {
      padding: 0 24px 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &-left {
        white-space: nowrap;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        color: #222A35;
        font-size: 14px;
        .dropdown-checkbox {
          width: 260px;
        }
      }

      &-right {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        &-btn {
          margin-left: 12px;
          &:first-child:hover {
            background-color: #F2F3F3;
            border-color: rgba(34, 42, 53, 0.08);
            color: #222A35;
          }
        }
      }
    }
  }
}

.list-wrap-title-popover {
  padding-bottom:  0px !important;
  .ant-popover-content .ant-popover-inner .ant-popover-inner-content {
    padding: 16px !important;
    border: 1px solid #f2f3f3;
    border-radius: 6px !important;
  }
}

.popoverContent {
  font-size: 14px;
  color: #222A35;
  .link {
    font-weight: 500;
    cursor: pointer;
    color: #047FFE;
  }
  .dk-iconfont {
    cursor: pointer;
    margin-left: 10px;
    font-size: 14px;
  }
}
.list-wrap {
  .ant-spin {
    padding-top: 200px;
    width: 386px;
  }
}

.remove-member {
  color: #FF563B;
}

.ant-message-warning {
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-popover-content {
  width: 360px;
  padding: 10px;
  &-title {
    margin-bottom: 16px;
    color: #222a35;
    display: flex;
    .dk-icon-a-tishi2 {
      color: #ffa50c;
      margin-right: 8px;
      margin-top: 2px;
    }
  }
  &-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    button {
      margin-left: 10px;
    }
  }
}