
import Cooper<PERSON><PERSON> from '@/utils/request/api/CooperApi';
import { del, put, post } from '@/utils/request/cooper';

// 修改权限
export const BatchChangeRoleForPerm = async (resourceId, list, perm, callback) => {
  const ps = list.map((item) => {
    let param = {
      permission: perm,
      orgMemberId: item.orgMemberId,
      role: item.role
    };
    // 当修改普通权限的时候这个人是管理员权限 需要先修改为非管理员权限 再对权限进行设置
    if (item.role === 'admin' || item.role === 'noperm_member') {
      let param1 = {
        orgMemberId: item.orgMemberId,
        permission: 35,
        role: 'member',
      };
      return put(CooperApi.LDAP_PERMISSION.replace(':id', resourceId), param1).then(() => {
        return put(CooperApi.LDAP_PERMISSION.replace(':id', resourceId), {...param1, permission: perm })
      })
    }
    return put(CooperApi.LDAP_PERMISSION.replace(':id', resourceId), param)
  });
  await Promise.all(ps).then(() => {callback(false)}).catch(() => {callback(true)})
}


// 调整管理员身份  type 区分是设为管理员还是取消管理员
export const BatchChangeRoleForAdmin = async (resourceId, list, type, callback) => {
  const ps = list.map(async (item) => {
    let param = {
      orgMemberId: item.orgMemberId,
      permission: type === 'add' ? null : 35,
      role: type === 'add' ? 'admin' : 'member'
    };
    return getCanOPerateEntryFn({
      principalMemberId:item.principalMemberId,
      principalType:item.principalType,
      resourceId,
    }).then(res => {
      const cancelAlonePerm = res.cancelAlonePerm;
      if(cancelAlonePerm !== 'REMOVE') {
        return put(CooperApi.LDAP_PERMISSION.replace(':id', resourceId), param)
      } else {
        return false
      }
    }).catch((err) => {
      return false
    })
  });
  await Promise.all(ps).then((res) => {
    callback(false);
  }).catch(() => { callback(true) })
  
}


// 判断是否可以去移除成员
export const getCanOPerateEntryFn = async ({principalMemberId,principalType,resourceId}) => {
  return post(CooperApi.GET_COOPERATION_ENTRY_PERM, {
    principalMemberId:principalMemberId,
    principalType:principalType,
    resourceId,
  });
};

// 删除成员
export const BatchDeleteMember = async (resourceId, list, callback) => {
  
  const ps = list.map(async (item) => {
    return getCanOPerateEntryFn({
      principalMemberId:item.principalMemberId,
      principalType:item.principalType,
      resourceId,
    }).then(res => {
      const cancelAlonePerm = res.cancelAlonePerm;
      if(cancelAlonePerm === 'REMOVE') {
        return del(`${CooperApi.LDAP_PERMISSION.replace(':id', resourceId)}?orgMemberId=${item.orgMemberId}&role=member`)
      } else {
        return false
      }
    }).catch((err) => {
      return false
    })
  });
  await Promise.all(ps).then(() => { callback(false, true) }).catch(() => { callback(true, true)})
}

