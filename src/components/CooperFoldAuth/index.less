.cooperFoldAuth-modal{
  @media screen and (max-height: 800px) {
    .ant-modal {
      top:50px;
    }
  }
  .ant-modal-header{
    border-bottom: none;
    border-radius: 8px;
    padding: 20px 24px 14px 24px;
    .ant-modal-title{
      font-size: 20px;
      line-height: 28px;
    }
  }
  .ant-modal-content {
    padding: 0;
    border-radius: 8px;
  }
    .ant-modal-body {
    padding: 0;
  }
}
// .cooperFoldAuth {
//   @media screen and (max-height: 800px) {
//     .ant-modal {
//       top:50px;
//     }
//   }
//   .ant-modal-content {
//     width: 640px;
//     // height: 649px;
//     border-radius: 6px;
//     padding-left: 30px;
//   }
//   &.addMemberModal {
//     .ant-modal-content {
//       height: 532px;
//     }
//     .ant-modal-header {
//       padding-bottom: 24px;
//     }
//   }
//   .ant-modal-header {
//     border:none;
//     border-radius: 12px;
//     padding: 26px 0 19px;
//     .ant-modal-title {
//       height: 28px;
//       font-size: 20px;
//       color: #2F343C;
//       line-height: 28px;
//       .addMemberTitle {
//         display: flex;
//         align-items: center;
//         .back {
//           cursor: pointer;
//           display: inline-block;
//           margin-right: 8px;
//           width: 24px;
//           height: 24px;
//           background: url('@/components/CooperFoldAuth/icon/back.png') no-repeat;
//           background-size: contain;
//         }
//       }
//     }
//   }
//   .ant-modal-body {
//     padding: 0;
//   }
//   // .ant-modal-close-x{
//   //   width: 20px;
//   //   height: 20px;
//   //   margin: 27px 28px 0 0;
//   //   &::before {
//   //     background-size: 100%;
//   //   }
//   // }
//   .powerSetClosed{
//     margin: 118px auto 0;
//     width: 315px;
//     text-align: center;
//     .pic{
//       width: 192px;
//       height: 171px;
//       margin: 0 auto;
//       background: url('./icon/powerSetClosed.png') no-repeat;
//       background-size: contain;
//     }
//     .desc {
//       margin: 10px 0 20px;
//       font-size: 15px;
//       color: #666666;
//       line-height: 24px;
//       text-align: center;
//     }
//     .openBtn {
//       display: inline-block;
//       margin: 0 auto;
//       padding: 0 18px;
//       height: 36px;
//       background: #333333;
//       border-radius: 6px;
//       font-size: 14px;
//       font-weight: 500;
//       color: #FFFFFF;
//       line-height: 36px;
//       text-align: center;
//       cursor: pointer;
//       border: none;
//       &.ant-btn-clicked:after {
//         display: none;
//       }
//     }
//   }
//   .ant-spin {
//     padding-top: 200px;
//     width: 575px;
//   }
// }