import { intl, getLocale } from 'di18n-react';
import React, { Fragment } from 'react';
import { openNewWindow } from '@/utils/index';
import { Popover } from 'antd';
import { isDiDiTenant } from '@/utils/entryEnhance';
import { useSelector } from 'react-redux';

function HelperLink(props) {
  const { cooperLinkConf } = useSelector((state) => state.GlobalData);
  // eslint-disable-next-line camelcase, max-len
  const { feedBack_dc, help_link_zh, help_link_en, update_log_zh, update_log_en, out_help_link_zh, out_help_link_en, out_update_log_zh, out_update_log_en } = cooperLinkConf;
  const HELP_LIST = isDiDiTenant() ? [
    // {
    //   val: intl.t('Cooper 客服'),
    //   open: (helpChain) => {
    //     openNewWindow(helpChain.support)
    //   }
    // },
    {
      icon: 'dk-icon-kefu',
      val: intl.t('联系客服'),
      open: () => {
        openNewWindow(feedBack_dc)
      },
    },
    {
      icon: 'dk-icon-bangzhuwendang',
      val: intl.t('帮助文档'),
      open: () => {
        if (getLocale() === 'zh-CN') {
          openNewWindow(help_link_zh);
        } else {
          openNewWindow(help_link_en);
        }
      },
    },
    // {
    //   val: intl.t('问题反馈'),
    //   open: (helpChain) => {
    //     openNewWindow(helpChain.feedBack)
    //   }
    // },
    {
      icon: 'dk-icon-gengxinrizhi',
      val: intl.t('更新日志'),
      open: () => {
        if (getLocale() === 'zh-CN') {
          openNewWindow(update_log_zh);
        } else {
          openNewWindow(update_log_en);
        }
      },
    },
  ] : [
    {
      icon: 'dk-icon-bangzhuwendang',
      val: intl.t('帮助文档'),
      open: () => {
        if (getLocale() === 'zh-CN') {
          openNewWindow(out_help_link_zh);
        } else {
          openNewWindow(out_help_link_en);
        }
      },
    },
    {
      icon: 'dk-icon-gengxinrizhi',
      val: intl.t('更新日志'),
      open: () => {
        if (getLocale() === 'zh-CN') {
          openNewWindow(out_update_log_zh);
        } else {
          openNewWindow(out_update_log_en);
        }
      },
    },
  ]

  const _renderContent = () => {
    return (
      <Fragment>
        {
          HELP_LIST.map((item, index) => {
            return <li
              className='help-center-list-item'
              key={index}
              onClick={() => item.open()}>
              <i className={`dk-iconfont ${item.icon}`}/>
              {item.val}
            </li>
          })
        }
      </Fragment>
    )
  }
  return (
    <div className='v3-cooper-helper-wrap'>
      <Popover
        content={_renderContent()}
        placement={ props.type === 'small' ? 'rightTop' : 'bottomLeft'}>
        <div className='v3-cooper-helper'>
          <i className="dk-iconfont dk-icon-a-bangzhuzhongxin4px" />
        </div>
      </Popover>
    </div>
  );
}

export default HelperLink;
