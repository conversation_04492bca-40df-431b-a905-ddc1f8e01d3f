/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-06-12 10:43:29
 * @LastEditTime: 2024-04-11 17:39:54
 * @Description: 简单描述
 * @FilePath: /knowledgeforge/src/components/GlobalUtils/CooperHelper/index.js
 *
 */
import { intl, getLocale } from 'di18n-react';
import React, { Fragment } from 'react';
import { openNewWindow } from '@/utils/index';
import './index.less';
import { Popover } from 'antd';
import { isDiDiTenant } from '@/utils/entryEnhance';
import { useSelector } from 'react-redux';
import helperFeedback from './HelperFeedback';

function CooperHelper(props) {
  const { cooperLinkConf } = useSelector((state) => state.GlobalData);
  // eslint-disable-next-line camelcase, max-len
  const { feedBack_dc, help_link_zh, help_link_en, update_log_zh, update_log_en, out_help_link_zh, out_help_link_en, out_update_log_zh, out_update_log_en } = cooperLinkConf;
  const HELP_LIST = isDiDiTenant() ? [
    {
      icon: 'dk-icon-kefu',
      val: intl.t('联系客服'),
      open: () => {
        openNewWindow(feedBack_dc)
      },
    },
    {
      icon: 'dk-icon-bangzhuwendang',
      val: intl.t('帮助文档'),
      open: () => {
        if (getLocale() === 'zh-CN') {
          openNewWindow(help_link_zh);
        } else {
          openNewWindow(help_link_en);
        }
      },
    },
    {
      icon: 'dk-icon-gengxinrizhi',
      val: intl.t('更新日志'),
      open: () => {
        if (getLocale() === 'zh-CN') {
          openNewWindow(update_log_zh);
        } else {
          openNewWindow(update_log_en);
        }
      },
    },
  ] : [
    {
      icon: 'dk-icon-kefu',
      val: intl.t('联系客服'),
      open: () => {
        helperFeedback();
      },
    },
    {
      icon: 'dk-icon-bangzhuwendang',
      val: intl.t('帮助文档'),
      open: () => {
        if (getLocale() === 'zh-CN') {
          openNewWindow(out_help_link_zh);
        } else {
          openNewWindow(out_help_link_en);
        }
      },
    },
  ]

  const _renderContent = () => {
    return (
      <Fragment>
        {
          HELP_LIST.map((item, index) => {
            return <li
              className='help-center-list-item'
              key={index}
              onClick={() => item.open()}>
              <i className={`dk-iconfont ${item.icon}`}/>
              {item.val}
            </li>
          })
        }
      </Fragment>
    )
  }
  return (
    <div className='v3-cooper-helper-wrap'>
      <Popover
        content={_renderContent()}
        placement={ props.type === 'small' ? 'rightTop' : 'bottomLeft'}>
        <div className='v3-cooper-helper'>
          <i className="dk-iconfont dk-icon-a-bangzhuzhongxin4px" />
        </div>
      </Popover>
    </div>
  );
}

export default CooperHelper;
