import { intl } from 'di18n-react';
import React, { useEffect, useRef, useState } from 'react';
import { Modal } from 'antd';
import classNames from 'classnames/bind';
import mountAnywhere from '@/utils/mountAnywhere';
import MemberListOfDK from '@/components/serviceComponents/MemberListOfDK';
import AddNewCollaborator from '@/components/serviceComponents/AddNewCollaborator';
import Share from '@/components/serviceComponents/Share';
import MemberListOfResource from '@/components/CooperFoldAuth/MemberListOfResource';
import styles from './style.module.less';

const cls = classNames.bind(styles);

const ShareModal = (props) => {
  const { onClose, id, info, cooperLinkConf } = props;
  const { teamId, spaceId, team_id, sourceId, isTeam, space_id } = info;
  const [ step, setStep ] = useState(0);
  const [ searchValue, setSearchValue ] = useState('');
  const shareComponentRef = useRef(null);

  const triggerSearch = (value) => {
    setSearchValue(value);
    setStep(2);
  }

  useEffect(() => {
    if (step === 0 || step === 1) {
      setSearchValue('');
    }
  }, [step]);

  return (
    <Modal
      width={540}
      centered={true}
      destroyOnClose={true}
      className={cls('modal-setting')}
      visible={true}
      title={null}
      footer={null}
      onCancel={onClose}
      closable={false}
    >
      <div>
        {
          step === 0 && (
            <div id='tooltip-wrap' className={cls('share-content')}>
              <div className={cls('share-header-title-line')}>
                <div className={cls('list-header-title')}>{intl.t('分享')}</div>
              </div>
              <Share
                resourceId={id}
                info={info}
                isDkPage={info.isDkPage}
                gotoNext={() => setStep(1)}
                triggerSearch={triggerSearch}
                ref={shareComponentRef}
                closeShare={() => {
                  setSearchValue('');
                }}
                isModalShare={true}
                cooperLinkConf={cooperLinkConf}
              />
            </div>
          )
        }
        {
          step === 1 && (
            <div className={cls('share-collaborator')}>
              {
                info.isDkPage ? (
                  <MemberListOfDK
                    isDkPage={info.isDkPage}
                    needDKDetail={true}
                    resourceId={id}
                    teamId={teamId || spaceId || team_id || sourceId  || space_id}
                    teamIdAllTeam={teamId || spaceId || team_id || sourceId}
                    title={intl.t('协作者')}
                    showBack={true}
                    permission={info.perm}
                    roleKey={info.roleKey}
                    handleBack={() => setStep(0)}
                    gotoNext={() => setStep(2)}
                    permissionsPopHover={true}
                    info={info}
                  />
                ) : (
                  <MemberListOfResource
                    permissionsPopHover={true}
                    resourceId={id}
                    isInOperate={true}
                    teamId={teamId || spaceId || team_id || sourceId}
                    teamIdAllTeam={teamId || spaceId || team_id || sourceId}
                    isPersonalTeam={!isTeam}
                    changeCooperTotalCount={() => {}}
                    info={info}
                    showBack={true}
                    onRemoved={() => {}}
                    onPermChange={() => {}}
                    refreshDocSdk={() => {}}
                    toggleChildVisible={() => {}}
                    handleBack={() => setStep(0)}
                    gotoNext={() => setStep(2)}
                    onCloseUserModal={() => {
                      this.setState({
                        showForm: false,
                      });
                    }}
                    resourceType={'doc'} // 代表文档成员列表
                  />
                )
              }
              
            </div>
          )
        }
        {
          step === 2 && (
            <div className={cls('add-collaborator')}>
              <AddNewCollaborator
                isDkPage={info.isDkPage}
                fileType={info.fileType}
                resourceId={id}
                teamId={teamId || spaceId || team_id || sourceId}
                showBack={true}
                searchValue={searchValue}
                backToFirstStep={() => {
                  setStep(0);
                  setTimeout(() => {
                    if (shareComponentRef.current) {
                      shareComponentRef.current.focusInput();
                    }
                  }, 1000);
                }}
                handleBack={() => setStep(1)}
                closeShare={() => {
                  setStep(0);
                  onClose();
                }}
              />
            </div>
          )
        }
      </div>
    </Modal>
  )
}

export default function shareModal(id, info, cooperLinkConf) {
  const Comp = (
    <ShareModal 
      id={id}
      info={info}
      cooperLinkConf={cooperLinkConf}
    />
  );
  mountAnywhere(Comp);
}

