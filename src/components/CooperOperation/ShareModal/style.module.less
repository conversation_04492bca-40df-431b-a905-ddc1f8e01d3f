.modal-setting {
  :global {
    .ant-modal-content {
      border-radius: 8px;
    }

    .ant-modal-title {
      line-height: 26px;
      font-size: 18px;
    }

    .ant-modal-header {
      border-radius: 8px 8px 0 0;
      border-bottom: none;

      padding: 20px 20px 16px;
    }

    .ant-modal-footer {
      border-top: none;
      padding: 20px;
    }

    .ant-modal-body {
      padding: 20px 20px 0px;
    }
  }

  .modal-setting-close {
    color: @blueGray-7;
    font-size: 24px;
    position: absolute;
    top: 20px;
    right: 20px;
    width: 28px;
    height: 28px;
    border-radius: 4px;
    line-height: 28px !important;
  }

  .share-content {
    padding-bottom: 20px;
  }

  .share-header-title-line{
    border-bottom: 1px solid #F2F3F3;
    margin: 0 -20px 20px;
    padding: 0 20px;
    .list-header-title {
      font-size: 16px;
      font-weight: 600;
      color: #222A35; 
      line-height: 22px;
      word-break: break-all;
      display: inline-flex;
      align-items: center;
      padding-bottom: 6px;
      border-bottom: 2px solid #047ffe;
      i {
        font-size: 20px;
        margin-right: 4px;
        cursor: pointer;
      }
    }
  }
}

.add-collaborator {
  height: 580px;
}