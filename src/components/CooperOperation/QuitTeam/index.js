import { intl } from 'di18n-react';
import { message } from 'antd';
import { d } from '@/utils/request/abort';
import cooperConfirm from '@/components/common/CooperConfirm';
// import { GEMINI_MEMBERS } from '../../../api';
import api from '@/utils/request/api/CooperApi';

async function doQuit(teamId) {
  const data = await d(api.GEMINI_MEMBERS.replace(':teamId', teamId));
  return data;
}

function quitTeam(teamId, doneCallback) {
  cooperConfirm({
    title: intl.t('是否退出该空间？'),
    content: intl.t('退出空间后全部文件会保留在空间里'),
    onOk: async () => {
      const isQuit = await doQuit(teamId);

      if (isQuit) {
        message.success(intl.t('退出成功'));
        doneCallback();
      }
    },
  });
}

export default quitTeam;
