import { intl } from 'di18n-react';
import React from 'react';
import { Select, message } from 'antd';
import { getUserNameFromCookie } from '@/utils';
import { get, post } from '@/utils/request/cooper';
import { MAX_COUNT } from '@/constants/cooperConstants';
import PrimaryModal from '@/components/common/PrimaryModal';
import mountAnywhere from '@/utils/mountAnywhere';
import Tag from '@/components/Tag/index';
// import { TEAM_MEMBERS, TEAM_TRANSFER } from '../../../api';
import api from '@/utils/request/api/DkApi';
import './index.less';

const { Option } = Select;

async function doDeliverTeam(team, to_user, id) { // eslint-disable-line camelcase
  const data = await post(api.TEAM_TRANSFER, {
    team,
    to_user,
    orgMemberId: id,
  });
  return data;
}

async function getMemberList(teamId) {
  const res = await get(`${api.TEAM_MEMBERS.replace(':teamId', teamId)}?pageNum=0&pageSize=${MAX_COUNT}`);
  return res.items || [];
}

class DeliverTeam extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      toUserNameId: undefined,
      confirmLoading: false,
      mbList: [],
    };
  }

  componentDidMount() {
    getMemberList(this.props.teamId).then((mbList) => this.setState({
      mbList,
    }));
  }

  doDeliver = async () => {
    try {
      const { teamId, doneCallback, onClose } = this.props;
      const { toUserNameId, mbList } = this.state;
      if (!toUserNameId) return;
      const toUser = mbList.find((item) => item.id === toUserNameId);
      const userSelf = getUserNameFromCookie();

      if (toUser.self) {
        message.error(intl.t('接收人不能是自己'));
        return;
      }

      if (toUser.orgId !== 1) {
        message.error(intl.t('接收人不能是外部用户'));
        return;
      }

      this.setState({
        confirmLoading: true,
      });

      const data = await doDeliverTeam(teamId, toUser.user_name, toUserNameId);
      this.setState({
        confirmLoading: false,
      });

      if (data) {
        message.success(intl.t('转交成功'));
        doneCallback();
        onClose();
      } else {
        message.success(intl.t('转交成功'));
      }
    } catch (err) {
      this.setState({
        confirmLoading: false,
      });
    }
  };

  notFoundContent = () => {
    return null;
  } ;

  render() {
    // onClose 是 mountAnywhere 自动注入的
    const { onClose } = this.props;
    const { toUserNameId, confirmLoading, mbList } = this.state;
    // const filteredList = mbList.filter(m => {
    //   const userName = m.user_name.toLowerCase();
    //   const keyWord = toUserNameId ? toUserNameId.toLowerCase() : "";
    //   return userName.indexOf(keyWord) !== -1;
    // });
    return (
      <PrimaryModal
        title={intl.t('转交权限')}
        onCancel={onClose}
        onOk={this.doDeliver}
        okText={intl.t('转交')}
        confirmLoading={confirmLoading}
        selfClassName={toUserNameId ? '' : 'deliver-team-disabled-modal'}
        >
        <div className='deliver-team'>
          <div className='warn'>
            {intl.t('输入的转交人必须是团队成员，请核实后再确认提交')}
          </div>
          <div className='receiver'>
            <div className='selectView'>
              <span
                style={{
                  whiteSpace: 'nowrap',
                }}>{intl.t('新所有者')}</span>
              <Select
                showSearch
                  // mode='combobox'
                virtual={false}
                style={{
                  flex: 1,
                  height: 32,
                  width: 120,
                }}
                placeholder={intl.t('请输入新所有者的邮箱前缀')}
                value={toUserNameId}
                onChange={(v, o) => {
                  this.setState({
                    toUserNameId: v,
                  })
                }}
                notFoundContent={this.notFoundContent()}
                onSearch = {(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
                options = {mbList.map((m) => {
                  return {
                    label: <p>
                      <span>{`${m.user_name}(${m.user_display_name})`}</span>
                      {
                        m.showOuter && <Tag
                          text={intl.t('外部')}
                          type='out-yellow' />
                      }
                    </p>,
                    value: m.id,
                  }
                })}
                />
            </div>
            <div className='tips'>{intl.t('转交成功会以邮件和D-Chat通知')}</div>
          </div>
        </div>
      </PrimaryModal>
    );
  }
}

function deliverTeam(teamId, doneCallback) {
  const Comp = <DeliverTeam
    teamId={teamId}
    doneCallback={doneCallback} />;
  mountAnywhere(Comp);
}

export default deliverTeam;

