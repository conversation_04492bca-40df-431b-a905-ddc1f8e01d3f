// @import '@/components/theme.scss';

.deliver-team {
  .warn {
    width: 100%;
    height: 28px;
    line-height: 28px;
    text-align: center;
    background-color: rgba(#ff405b, 0.1);
    color: #ff405b;
    position: relative;

    &::before,
    &::after {
      content: '';
      display: block;
      width: 32px;
      height: 28px;
      background-color: rgba(#ff405b, 0.1);
      position: absolute;
      top: 0;
    }

    &::before {
      left: -32px;
    }

    &::after {
      right: -32px;
    }
  }

  .receiver {
    margin-top: 16px;
    margin-bottom: 24px;
    font-family: PingFangSC-Medium;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0px;
    color: @blueGray-color;


    .selectView {
      display: flex;
      flex-direction: row;
      flex: 1;
      padding-left: 20px;
      align-items: center;

      .ant-select {
        margin-left: 10px;

        input {
          &:focus {
            border: none !important;
          }
        }

        .ant-select-selection--single {
          height: 32px;
          border-color: #e4e9f3;
        }

        .ant-select-selection__rendered {
          line-height: 30px;
          // font-size: $fontSize;
        }

        .ant-select-selection__placeholder {
          color: #bec5d2;
        }
      }

      .ant-select-open .ant-select-selection {
        // border-color: $brandColor;
        box-shadow: 0 0 0 2px rgba(63, 129, 255, 0.2);
      }

      .ant-select-focused .ant-select-selection {
        box-shadow: none;
      }

    }
    .tips {
      font-family: PingFangSC-Regular;
      font-size: 12px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: 0px;
      color: rgba(26, 110, 255, 0.8);
      margin-left: 86px;
      margin-top: 5px;
    }
  }
}

.deliver-team-disabled-modal {
  .ant-modal-footer {
    button:last-child, button:last-child:hover{
      color: rgba(0,0,0,.25);
      background-color: #f7f7f7;
      border-color: #d9d9d9;
      cursor: not-allowed;
    }
  } 
  
}

.ant-modal-body {
  padding: 0 24px;
}
