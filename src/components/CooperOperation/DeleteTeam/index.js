import { intl } from 'di18n-react';
import React from 'react';
import { Input, message } from 'antd';
import { d, postAbort } from '@/utils/request/abort';
import PrimaryModal from '@/components/common/PrimaryModal';
// import { ADAPTER_TEAMS_TEAMID, SPACE_REMOVE } from '../../../api';
import api from '@/utils/request/api/CooperApi';
import mountAnywhere from '@/utils/mountAnywhere';
import './index.less';

async function doDelete(teamId) {
  const isDelete = await d(api.ADAPTER_TEAMS_TEAMID.replace(':teamId', teamId));
  return isDelete;
}

async function spaceRemoveAction(spaceId) {
  const isDelete = await postAbort(api.SPACE_REMOVE, { spaceId });
  const isDeleteRes = isDelete === 1;
  return isDeleteRes;
}


class DeleteTeam extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      confirmText: '',
    };
    this.textRef = React.createRef();
    // this.handleCopy = this.handleCopy.bind(this);
  }


  doDelete = async () => {
    const { teamId, doneCallback = () => {}, onClose, title } = this.props;
    const { confirmText } = this.state;
    // __mirrorSendEvent('DELETE_TEAM_CONFIRM_CLICK');
    if (confirmText && confirmText === intl.t('以上风险我已知悉')) {
      const isDelete = title ? await spaceRemoveAction(teamId) : await doDelete(teamId);
      if (isDelete) {
        message.success(intl.t('删除成功'));
        doneCallback();
        onClose();
      } else {
        message.error(intl.t('删除失败'));
      }
    } else {
      message.warn(intl.t('内容输入存在问题，请重新输入'));
    }
  };

  inputChange = (e) => {
    let value = e && e.target && e.target.value;
    this.setState({
      confirmText: value,
    });
  }

  handleCopy = () => {
    if (this.textRef.current) {
      const textToCopy = this.textRef.current.innerText;
      navigator.clipboard.writeText(textToCopy.replace(/^"(.*)"$/, '$1'))
        .then(() => {
          message.success(intl.t('复制成功'));
        })
        .catch((error) => {
          message.success(intl.t(error));
        });
    }
  };

  render() {
    const { confirmText } = this.state;
    const { onClose, teamName, title = '' } = this.props;

    return (
      <PrimaryModal
        title={intl.t('删除团队')}
        onCancel={() => {
          onClose();
          // __mirrorSendEvent('DELETE_TEAM_CANCEL_CLICK');
        }}
        onOk={confirmText && this.doDelete}
        okText={intl.t('确认删除')}
        cancelText={intl.t('取消')}
        selfClassName={confirmText.length > 0 ? 'delete-team-modal' : 'delete-team-modal delete-team-disabled-modal'}
      >
        <div className='delete-content'>
          <div className='delete-tips'>
            <p className='delete-tips-text-large'>
              <i className='tips-icon' />
              <span className='tips-text-span'>
                {intl.t('团队空间{name}一旦删除成功', {
                  // eslint-disable-next-line react/no-unescaped-entities
                  name: <span className='tips-text-span-red'>{teamName}</span>,
                })}
                {/* className='delete-tips-text' */}
                <span>  { title ? intl.t('空间内所有文档无法进行恢复，请谨慎操作') : intl.t('空间内所有内容将进您的回收站，30 天后自动彻底删除。如果你删除的内容中有属于他人的，其所有者将收到通知，请谨慎操作，空间关联行为较多删除后可还原内容包含：空间所在位置、空间内文件、空间内成员、空间成员权限、空间类型')}</span>
              </span>
            </p>
          </div>
          <p className='input-label'>{intl.t('请在下方输入{risk}进行再次确认', {
            risk: <span >&quot;{intl.t('以上风险我已知悉')}&quot;</span>,
          })}</p>
          <Input
            className='confirm-input'
            onChange={this.inputChange}
            placeholder={intl.t('以上风险我已知悉')}
          />
        </div>
      </PrimaryModal>
    );
  }
}

function deleteTeam(teamId, teamName, doneCallback, title) {
  const deleteModal = <DeleteTeam
    teamId={teamId}
    teamName={teamName}
    doneCallback={doneCallback}
    title={title}/>;
  mountAnywhere(deleteModal);
  return deleteModal;
}

export default deleteTeam;
