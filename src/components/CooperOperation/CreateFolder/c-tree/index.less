.c-tree {
  font-size: 14px;
  margin: 4px 0;
  padding: 2px 0;
  width: 336px;
  height: 250px;
  border-radius: 4px;
  overflow: auto;
  background-color: #fff;
  box-shadow: 0 2px 8px 0 rgba(#2f343c, .2);
  position: absolute;
  right: 4px;
  z-index: 1;
  top: 30px;

  img {
    width: 18px;
    margin-right: 4px;
    vertical-align: middle;
  }

  .triangle-container {
    cursor: pointer;

    > .triangle {
      display: inline-block;
      font-size: 14px;
      margin-right: 2px;
    }
  }

  .tree-node {
    height: 32px;
    line-height: 32px;
    padding-left: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    user-select: none;

    &:hover {
      background-color: #f0f3f7;
    }

    &.selected {
      background-color: rgba(#06f, .1);
    }

    &.no-permis {
      opacity: .7;
      cursor: not-allowed;
      position: relative;
    }

    > span {
      vertical-align: middle;
    }

    .hl {
      color: #06f;
    }
  }

  > p {
    margin-top: 80px;
    text-align: center;
    color: #94a0b0;
  }
}
