.create-folder {
  font-size: 14px;
  color: #2f343c;

  .ant-input {
    width: 336px;
    height: 32px;
    font-size: 14px;
    color: #2f343c;
    border-color: rgba(#94a0b0, .24);

    &:hover,
    &:focus {
      border-color: #06F;
      box-shadow: 0 0 0 2px rgba(#06f, 0.2);
    }
  }

  .name,
  .path {
    position: relative;
    display: flex;
    align-items: center;
  }

  .label {
    display: inline-block;
    width: 56px;
    margin-right: 20px;
    text-align: right;
    white-space: nowrap;
  }

  .name {
    margin-top: 16px;
    margin-bottom: 24px;
  }

  .path {
    margin-bottom: 36px;
    position: relative;

    img[alt=cancel],
    img[alt=down] {
      width: 24px;
      padding-left: 8px;
      background-color: #fff;
      position: absolute;
      right: 16px;
      top: 8px;
      cursor: pointer;
    }

    img[alt=down] {
      width: 22px;
      top: 9px;
    }
  }
  .create-folder-error {
    font-size: 12px;
    color: #ff405b;
    position: absolute;
    left: 78px;
    top: 32px;
    margin: 0;
  }
}

.create-folder-modal {
  :global {
    .ant-modal-content {
      border-radius: 8px;
    }
  }
}
