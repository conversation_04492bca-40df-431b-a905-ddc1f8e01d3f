// 提供dc的保存界面也采用本样式

.move-modal {
  .mv-tip {
    margin-top: 8px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    height: 17px;
  }

  .ant-modal-content {
    box-shadow: 0 4px 16px 0 rgba(#2f343c, .2);
    width: 580px;
    max-height: 502px;
    border-radius: 6px;
    overflow: hidden;
  }
  
  .ant-modal-body {
    padding: 0 24px 24px;
  }
  
  .ant-modal-header {
    border-bottom: none;
    padding: 16px 48px 16px 24px;
  }

  // 如果有样式重构，包在这儿
  .ant-modal-close-x {
    &::before {
      background-position: 24px 19px;
    }
  }
}

// 目前全局的madal关闭都使用这个样式，慎重修改
// 如果有样式重构，包起来
.ant-modal-close-x {
  width: 64px;

  &::before {
    // background-image: url(./icons/iconClose.png);
    background-size: 18px 18px;
    background-position: center center;
    background-repeat: no-repeat;
    content: "";
    height: 100%;
  }
}