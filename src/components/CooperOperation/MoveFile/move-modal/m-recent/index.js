import { intl, getLocale } from 'di18n-react';
import React from 'react';
import { Tooltip } from 'antd';

import cls from 'classnames';
import { MOVE, COPY } from '@/constants/cooper';
import { getRecentFolds } from '@/service/cooper/moveFile';
import './index.less';

class MRecent extends React.Component {
  static defaultProps = {
    type: MOVE,
    spaceId: 0,
    selected: {},
    onClick: () => {},
  };

  state = {
    items: [],
  };

  async componentDidMount() {
    const { spaceId, selected, businessName } = this.props;
    const res = await getRecentFolds(spaceId, businessName);
    res.forEach((item) => {
      item.selected = item.id === (selected && selected.id);
    });
    this.setState({
      items: res,
    });
  }

  componentWillReceiveProps(nextProps) {
    // 更新选中状态
    if (nextProps.selected !== this.props.selected) {
      const { items } = this.state;
      const selected = nextProps.selected || {};
      items.forEach((item) => {
        item.selected = item.id === selected.id;
      });
      this.setState({
        items,
      });
    }
  }

  handleClick = (item) => {
    if (!item.usable || item.selected) return; // 更新选中状态

    const { items } = this.state;
    items.forEach((im) => {
      im.selected = im.usable && im === item;
    });
    this.setState({
      items,
    }); // 触发回调

    if (item.usable) {
      this.props.onClick(item);
    }
  };

  render() {
    const { items } = this.state;
    return (
      items.length > 0 && (
        <div className='m-recent'>
          <span>{intl.t('最近')}</span>
          {items.map((item) => (
            <span
              key={item.id}
              className={cls('item', {
                disabled: !item.usable,
                selected: item.selected,
              })}
              onClick={() => this.handleClick(item)}
            >
              {!item.usable ? (
                <Tooltip
                  title={intl.t('该文件夹不属于当前团队')}
                  placement='right'
                >
                  <span>
                    <img
                      src={item.tinyImage}
                      alt='icon' />
                    {getLocale() === 'zh-CN' ? item.nameCn : item.nameEn}
                  </span>
                </Tooltip>
              ) : (
                <span>
                  <img
                    src={item.tinyImage}
                    alt='icon' />
                  {getLocale() === 'zh-CN' ? item.nameCn : item.nameEn}
                </span>
              )}
            </span>
          ))}
        </div>
      )
    );
  }
}

export default MRecent;
