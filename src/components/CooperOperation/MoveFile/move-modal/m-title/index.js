
import { intl } from 'di18n-react';
import React from 'react';
import { MOVE, COPY, SAVE, UPLOAD, DESSELECTOR } from '@/constants/cooper';
import './index.less';

class MTitle extends React.Component {
  static defaultProps = {
    type: SAVE,
    src: '文件',
    dst: '文件夹',
  };

  render() {
    const { type, src, dst } = this.props;
    return (
      <div className='m-title'>
        {type === MOVE && intl.t('将{src}移动到{dst}', { src: <span>{src}</span>, dst: <span>{dst}</span> })}
        {type === COPY && intl.t('将{src}复制到{dst}', { src: <span>{src}</span>, dst: <span>{dst}</span> })}
        {(type === SAVE || type === DESSELECTOR) && intl.t('将{src}保存到{dst}', { src: <span>{src}</span>, dst: <span>{dst}</span> })}
        {type === UPLOAD && intl.t('将{src}上传到{dst}', { src: <span>{src}</span>, dst: <span>{dst}</span> })}
      </div>
    );
  }
}

export default MTitle;
