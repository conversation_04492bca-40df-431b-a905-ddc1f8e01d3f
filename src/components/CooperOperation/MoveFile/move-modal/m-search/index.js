/* eslint-disable max-classes-per-file */
// /* eslint-disable max-classes-per-file */
// import { intl } from 'di18n-react';
// import React from 'react';
// import { Select } from 'antd';
// import debounce from 'lodash/debounce';
// import escape from 'lodash/escape';
// import { searchFolder } from '@/service/cooper/moveFile';
// import { HIGH_SEP, MOVE, COPY } from '@/constants/cooper';
// import iconSearch from '@/assets/icon/icon_sousuo.png';
// import iconClose from '@/assets/icon/close.png';
// import './index.less';;

// class SearchItem extends React.Component {
//   static defaultProps = {
//     icon: '',
//     highLightName: '',
//     pathName: '',
//   };

//   render() {
//     const { icon, highLightName, pathName } = this.props.item;
//     return (
//       <div className='mv-search-item'>
//         <img
//           src={icon}
//           alt='icon' />
//         <div>
//           <p
//             className='name'
//             dangerouslySetInnerHTML={{
//               __html: highLightName,
//             }}
//           />
//           <p>{pathName}</p>
//         </div>
//       </div>
//     );
//   }
// }

// class MSearch extends React.Component {
//   static defaultProps = {
//     type: MOVE,
//     spaceId: 0,
//     onSelect: () => { },
//   };

//   state = {
//     keyword: '',
//     results: [],
//     showNotFound: true,
//   };

//   handleSearch = debounce(async (keyword) => {
//     const results = await searchFolder(keyword, this.props.spaceId);
//     results.forEach((result) => {
//       const name = result.highLightName;
//       const regx = new RegExp(`${HIGH_SEP}(.*?)${HIGH_SEP}`, 'g');
//       result.highLightName = escape(name).replace(
//         regx,
//         (_, p1) => `<span class="hl">${p1}</span>`,
//       );
//       result.pathName = result.pathFromRoot.map((p) => p.name).join('>');
//     });
//     this.setState({
//       results,
//     });
//   }, 300);

//   handleSelect = (value) => {
//     const [id] = value.split(HIGH_SEP);
//     const { results } = this.state;
//     const { onSelect } = this.props;
//     const item = results.find((result) => result.id === Number(id));
//     if (item) onSelect(item);
//   };

//   handleChange = (value) => {
//     value = value.includes(HIGH_SEP) ? value.split(HIGH_SEP)[1] : value;
//     this.setState({
//       keyword: value,
//     });
//   };

//   clear = () => {
//     this.setState({
//       keyword: '',
//       results: [],
//     });
//   };

//   render() {
//     const { keyword, results, showNotFound } = this.state;
//     return (
//       <div className='m-search'>
//         <img src={iconSearch} alt='search' />
//         <Select
//           className='m-search-select'
//           dropdownClassName='m-search-dropdown'
//           mode='combobox'
//           value={keyword}
//           placeholder={intl.t('搜索')}
//           showSearch
//           onSearch={(v) => this.handleSearch(v)}
//           onSelect={(v) => this.handleSelect(v)}
//           onChange={(v) => this.handleChange(v)}
//           onBlur={() =>
//             this.setState({
//               showNotFound: false,
//             })
//           }
//           onFocus={() =>
//             this.setState({
//               showNotFound: true,
//             })
//           }
//         >
//           <Select.OptGroup label={<p>{intl.t('搜索结果')}</p>}>
//             {results.map((result) => {
//               return (
//                 <Select.Option key={result.id + HIGH_SEP + result.name}>
//                   <SearchItem item={result} />
//                 </Select.Option>
//               );
//             })}
//           </Select.OptGroup>
//         </Select>
//         <img
//           src={iconClose}
//           alt='cancel'
//           className={keyword ? '' : 'hidden'}
//           onClick={this.clear}
//         />

//         {keyword && results.length === 0 && showNotFound && (
//           <div className='not-found-content'>
//             {intl.t('没有找到匹配结果，尝试其他关键词进行搜索')}
//           </div>
//         )}
//       </div>
//     );
//   }
// }

// export default MSearch;

import { intl } from 'di18n-react';
import React from 'react';
import { Select } from 'antd';
import { debounce } from 'lodash-es';
import { noop } from 'lodash-es';
import { highlight } from '@/utils';
import { searchFolder } from '@/service/cooper/moveFile';
import { HIGH_SEP, MOVE, COPY } from '@/constants/cooper';
import iconSearch from '@/assets/icon/icon_sousuo.png';
import iconClose from '@/assets/icon/close.png';
import './index.less';

class SearchItem extends React.Component {
  static defaultProps = {
    icon: '',
    highLightName: '',
    pathName: '',
  };

  render() {
    const { icon, highLightName, pathName } = this.props.item;
    return (
      <div className='mv-search-item'>
        <img
          src={icon}
          alt='icon' />
        <div>
          <p
            className='name'
          >{highLightName}</p>
          <p>{pathName}</p>
        </div>
      </div>
    );
  }
}

class MSearch extends React.Component {
  static defaultProps = {
    type: MOVE,
    spaceId: 0,
    onSelect: noop,
  };

  state = {
    keyword: null,
    results: [],
    showNotFound: true,
  };

  handleSearch = debounce(async (keyword) => {
    const { type } = this.props;

    // if (type === MOVE) {
    //   __mirrorSendEvent('MOVE_MODAL_SEARCH');
    // } else if (type === COPY) {
    //   __mirrorSendEvent('COPY_MODAL_SEARCH');
    // }

    const results = await searchFolder(keyword, this.props.spaceId);
    results.forEach((result) => {
      const name = result.highLightName;
      const regx = new RegExp(`${HIGH_SEP}(.*?)${HIGH_SEP}`, 'g');
      result.highLightName = highlight(name);
      result.pathName = result.pathFromRoot.map((p) => p.name).join('>');
    });
    this.setState({
      results,
    });
  }, 300);

  handleSelect = (value) => {
    // __mirrorSendEvent(this.props.type === MOVE
    //   ? 'MOVE_MODAL_SEARCH_CLICK'
    //   : 'COPY_MODAL_SEARCH_CLICK');

    const [id] = value.split(HIGH_SEP);
    const { results } = this.state;
    const { onSelect } = this.props;
    const item = results.find((result) => result.id === Number(id));
    if (item) onSelect(item);
  };

  handleChange = (value) => {
    value = value?.includes(HIGH_SEP) ? value?.split(HIGH_SEP)[1] : value;
    this.setState({
      keyword: value,
    });
  };

  clear = () => {
    this.setState({
      keyword: '',
      results: [],
    });
  };

  _renderContent = () => {
    return (
      <div className='not-found-content'>
        {intl.t('没有找到匹配结果，尝试其他关键词进行搜索')}
      </div>
    )
  }

  render() {
    const { keyword, results, showNotFound } = this.state;
    return (
      <div className='m-search'>
        <img
          src={iconSearch}
          alt='search' />
        <Select
          className='m-search-select'
          dropdownClassName='m-search-dropdown'
          mode='combobox'
          value={keyword}
          placeholder={intl.t('搜索')}
          showSearch
          onSearch={(v) => this.handleSearch(v)}
          onSelect={(v) => this.handleSelect(v)}
          onChange={(v) => this.handleChange(v)}
          allowClear={true}
          showArrow={false}
          onBlur={() => this.setState({
            showNotFound: false,
          })
          }
          notFoundContent={this._renderContent()}
          onFocus={() => this.setState({
            showNotFound: true,
          })
          }
        >
          <Select.OptGroup label={<p>{intl.t('搜索结果')}</p>}>
            {results.map((result) => {
              return (
                <Select.Option key={result.id + HIGH_SEP + result.name}>
                  <SearchItem item={result} />
                </Select.Option>
              );
            })}
          </Select.OptGroup>
        </Select>
      </div>
    );
  }
}

export default MSearch;
