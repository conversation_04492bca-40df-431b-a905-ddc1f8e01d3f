.m-search {
  position: relative;

  .ant-select-selection__rendered {    
    .ant-select-selection__placeholder {
      padding-left: 8px;
    }
    .ant-select-search {
      padding-left: 8px;
    }
  }

  img[alt=search] {
    width: 18px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 16px;
    z-index: 999;
  }

  img[alt=cancel] {
    width: 16px;
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);

    &.hidden {
      display: none;
    }
  }

  .ant-select {
    width: 100%;
    font-size: 14px;
  }
  .m-search-select {
    input {
      padding: 0 27px !important;
    }
  }
  .ant-select-selection-item {
    padding: 0 27px !important;
  }
  .ant-select-selection__rendered {
    height: 100%;
    margin-left: 32px;
    margin-right: 32px;
  }
  .ant-select-selection-placeholder {
    color: #bbb;
    padding: 0 27px !important;
  }
  .ant-select-selection {
    height: 36px;
    border-color: #E6E6E6;
    background-color: #fff;
  }

  .ant-select-open .ant-select-selection,
  .ant-select-focused .ant-select-selection {
    // border: 1px solid #06f;
    box-shadow: none;
    // background-color: #fff;
  }

  .not-found-content {
    width: 100% !important;
    text-align: center !important;
  }
}
.m-search-dropdown {
  .ant-select-dropdown-menu {
    max-height: 334px;
  }

  .mv-search-item {
    color: #94a0b0;
    font-size: 12px;

    img {
      width: 26px;
      margin-right: 16px;
      vertical-align: initial; // 覆盖antd-4的基础样式
    }

    > div {
      display: inline-block;
      width: 380px;

      > p {
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .name {
      font-size: 14px;
      color: #2f343c;

      > span {
        color: #06f;
      }
    }
  }

  .ant-select-dropdown-menu-item-group-title {
    padding: 12px 16px 4px;
    font-size: 14px;
  }

  .ant-select-dropdown-menu-item {
    padding: 8px 24px !important;
    height: auto;

    &:hover {
      background-color: #f0f3f7;
    }
  }
}
