/* eslint-disable max-classes-per-file */
import { intl } from 'di18n-react';
import React from 'react';
import cls from 'classnames';
import { Tooltip } from 'antd';

import MNewfold from '../m-newfold';
import { getFolders, createFolder } from '@/service/cooper/moveFile';
import { N_NEWFOLD, N_PERSONAL, N_TEAM, MOVE, COPY } from '@/constants/cooper';
import './index.less';

class TreeNode extends React.Component {
  render() {
    const { node, selected, onClick, onCreate, onCancel } = this.props;
    const { children, depth } = node;
    return (
      <li>
        <div
          id={node.id}
          className={cls('tree-node', {
            selected: selected === node,
            'no-permis': !node.hasPermis,
          })}
          style={{
            paddingLeft: `${(depth - 1) * 14}px`,
          }}
          onClick={() => onClick(node)}
        >
          <i
            className='dk-iconfont dk-icon-shouqi'
            style={{
              opacity: node.hasChildren ? 1 : 0,
              transform: node.hasOpened ? 'rotate(90deg)' : 'none',
            }} />
          <img
            src={node.icon}
            alt='' />
          {node.id === N_NEWFOLD ? (
            <MNewfold
              onOk={onCreate}
              onCancel={onCancel} />
          ) : node.hasPermis ? (
            <span>{node.name}</span>
          ) : (
            <Tooltip
              title={intl.t('您没有该空间的编辑或上传权限')}
              placement='right'
            >
              {node.name}
            </Tooltip>
          )}
        </div>
        {!!children && children.length > 0 && (
          <ul
            style={{
              display: node.hasOpened ? 'block' : 'none',
            }}
          >
            {node.children.map((ch) => (
              <TreeNode
                {...this.props}
                key={ch.id}
                node={ch} />
            ))}
          </ul>
        )}
      </li>
    );
  }
}

class MListTree extends React.Component {
  static defaultProps = {
    type: MOVE,
    tree: {},
    onSelect: () => {},
  };

  constructor(props) {
    super(props);
    this.state = {
      tree: props.tree,
      selected: null,
    };
  }

  componentDidMount() {
    this.lastTreeId = this.props.tree.__id;
    this.initTree(this.props.tree);
  }

  componentWillReceiveProps(nextProps) {
    if (
      nextProps.tree !== this.state.tree
      || nextProps.tree.__id !== this.lastTreeId
    ) {
      this.lastTreeId = nextProps.tree.__id;
      this.initTree(nextProps.tree);
    }
  }

  initTree = (tree) => {
    const selected = this.findSelected(tree);
    this.setState({
      tree,
      selected,
    });

    if (selected) {
      const sel = selected.hasPermis ? selected : null;
      this.props.onSelect(sel);
      setTimeout(() => {
        const sel = document.getElementById(selected.id);

        if (sel) {
          sel.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        }
      }, 0);
    }
  };

  findSelected = (root) => {
    if (root.hasSelected) return root;

    for (const child of root.children || []) {
      const selected = this.findSelected(child);
      if (selected) return selected;
    }

    return null;
  };

  handleClick = async (node) => {
    // 未完成的新建文件夹、和无权限的不可被选中
    if (node.id === N_NEWFOLD || !node.hasPermis) return; // 1. 切换开闭状态

    node.hasOpened = !node.hasOpened; // 2. 加载子节点

    if (node.hasChildren && node.children.length === 0) {
      const res = await getFolders(node.id, node.teamId);

      if (res.length === 0) {
        node.hasChildren = false;
      } else {
        res.forEach((fold) => {
          fold.depth = node.depth + 1;
        });
        node.children = res;
      }
    } // 3. 设为选择节点

    this.setState({
      selected: node,
    }); // 4. 回调

    this.props.onSelect(node);
  };

  handleCreate = async (name) => {
    const { type } = this.props;

    if (type === MOVE) {
    //   __mirrorSendEvent('MOVE_MODAL_NEW_FOLDER');
      window.__OmegaEvent('ep_moveto_create_ck', '', {
        platform: 'new',
      });
    } else if (type === COPY) {
    //   __mirrorSendEvent('COPY_MODAL_NEW_FOLDER');
      window.__OmegaEvent('ep_copyto_create_ck', '', {
        platform: 'new',
      });
    }

    const { selected: sel, tree } = this.state; // 处理根目录下创建文件夹

    const selected = sel || tree;
    let parentId; let
      teamId;

    if (selected.type === N_PERSONAL) {
      parentId = 0;
      teamId = 0;
    } else if (selected.type === N_TEAM) {
      parentId = 0;
      teamId = selected.id;
    } else {
      parentId = selected.id;
      teamId = selected.teamId;
    }

    const fold = await createFolder(name, parentId, teamId);
    fold.depth = selected.depth + 1;
    selected.children.splice(0, 1, fold);
    selected.hasSelected = false;
    this.setState({
      selected: fold,
    });
    this.props.onSelect(fold);
    this.props.setAbleToCreate();
  };

  handleCancle = () => {
    const { selected: sel, tree } = this.state;
    const selected = sel || tree;
    selected.children.shift();
    selected.hasChildren = selected.children.length > 0; // 触发 re-render

    this.setState({
      selected,
    });
    this.props.setAbleToCreate();
  };

  render() {
    const { tree, selected } = this.state;
    const children = tree.children || [];
    return (
      <div className='m-list-tree'>
        {children.length === 0 && tree.hasPermis && (
          <p>{intl.t('该空间层级为空，可新建文件夹')}</p>
        )}
        {children.length === 0 && !tree.hasPermis && (
          <p>{intl.t('您没有该空间的编辑或上传权限')}</p>
        )}
        <ul>
          {children.map((ch) => (
            <TreeNode
              key={ch.id}
              selected={selected}
              node={ch}
              onClick={this.handleClick}
              onCreate={this.handleCreate}
              onCancel={this.handleCancle}
            />
          ))}
        </ul>
      </div>
    );
  }
}

export default MListTree;
