.m-list-tree {
  font-size: 14px;
  padding-right: 4px;

  img {
    width: 18px;
    margin-right: 4px;
    vertical-align: middle;
  }


  .tree-node {
    height: 32px;
    line-height: 32px;
    padding-left: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;

    &:hover {
      background-color: #f7f7f7;
    }

    &.selected {
      background-color: #f7f7f7;
    }

    &.no-permis {
      opacity: .7;
      cursor: not-allowed;
      position: relative;
    }

    > span {
      vertical-align: middle;
    }
  }
  .dk-icon-taoji<PERSON>-x<PERSON><PERSON><PERSON><PERSON><PERSON> {
    margin-right: 4px;
  }
  > p {
    margin-top: 80px;
    text-align: center;
    color: #94a0b0;
  }
}
