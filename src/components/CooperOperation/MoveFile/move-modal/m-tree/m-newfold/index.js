
import { intl } from 'di18n-react';
import React from 'react';
import { Input, message } from 'antd';
import { once } from 'lodash-es';
import { checkInputValue } from '@/utils/cooperutils';
import iconOk from '@/assets/icon/icon_queren_fill_green.svg';
import iconCancel from '@/assets/icon/icon_cuowu_fill_red.svg';
import './index.less';

class MNewfold extends React.Component {
  static defaultProps = {
    onOk: () => { },
    onCancle: () => { },
  };

  state = {
    value: intl.t('新建文件夹'),
  };

  handleOk = () => {
    const { value } = this.state;
    let s = checkInputValue(value);

    if (s.length > 0) {
      message.error(s);
    } else {
      this.props.onOk(value.trim());
    }
  };

  selectText = once((input) => {
    const endPos = input.value.length;
    input.setSelectionRange(0, endPos);
  });

  render() {
    const { onCancel } = this.props;
    const { value } = this.state;
    return (
      <div className='n-newfold'>
        <Input
          autoFocus
          value={value}
          onChange={(e) => this.setState({
            value: e.target.value,
          })
          }
          onPressEnter={this.handleOk}
          onFocus={(e) => this.selectText(e.target)}
        />
        <img
          src={iconCancel}
          alt='cancel'
          onClick={onCancel} />
        <img
          src={iconOk}
          alt='ok'
          onClick={this.handleOk} />
      </div>
    );
  }
}

export default MNewfold;
