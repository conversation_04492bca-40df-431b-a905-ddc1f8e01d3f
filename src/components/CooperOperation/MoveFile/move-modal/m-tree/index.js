import { intl } from 'di18n-react';
import React from 'react';

import MBread from './m-bread';
import MList from './m-list';
import MListTree from './m-list-tree';
import { N_ROOT, N_TEAMROOT, N_PERSONAL, N_TEAM, MOVE } from '@/constants/cooper';
import { getTeams, getFolders } from '@/service/cooper/moveFile';
import './index.less';
import { iconPersonalSpace, iconTeamSpace, GroupIcon } from '@/assets/icon/index';

const cooperRoot = {
  type: N_ROOT,
  id: N_ROOT,
  name: '<PERSON>',
};

const teamRoot = {
  type: N_TEAM,
  id: undefined,
  name: undefined,
  icon: iconTeamSpace,
  children: [],
  depth: 1,
  hasPermis: true,
  hasSelected: false,
  __id: 0,
  // 没有团队权限和一级目录权限的团队将被禁用
  disabled: false,
};
let __id = 1;

class MTree extends React.Component {
  static defaultProps = {
    type: MOVE,
    treeRoot: {
      type: N_ROOT,
    },
    disableRoot: false,
    onSelect: () => {},
  };

  state = {
    breads: [],
    list: null,
    tree: null,
  };

  teamListRoot = {
    type: N_TEAMROOT,
    id: N_TEAMROOT,
    name: intl.t('团队空间'),
    icon: GroupIcon,
  };

  personalRoot = {
    type: N_PERSONAL,
    id: 0,
    name: intl.t('个人空间'),
    icon: iconPersonalSpace,
    children: [],
    depth: 1,
    hasPermis: true,
    hasSelected: false,
    // 用来指示子组件是否 re-render，参考组件 m-list-tree
    __id: 0,
  };

  componentDidMount() {
    this.initTree(this.props.treeRoot);
  }

  componentWillReceiveProps(nextProps) {
    // 点击搜索结果、最近访问时，生成新树
    if (nextProps.treeRoot !== this.props.treeRoot) {
      this.initTree(nextProps.treeRoot);
    }
  }

  initTree = (root) => {
    cooperRoot.disabled = this.props.disableRoot;
    this.teamListRoot.disabled = this.props.disableRoot;

    switch (root.type) {
      case N_ROOT: {
        this.setState({
          breads: [cooperRoot],
          list: [this.personalRoot, this.teamListRoot],
          tree: null,
        });
        break;
      }

      case N_TEAMROOT: {
        const breads = [cooperRoot, this.teamListRoot];
        this.setState({
          breads,
          list: root.children,
          tree: null,
        });
        break;
      }

      case N_PERSONAL: {
        const breads = [cooperRoot, this.personalRoot];
        this.personalRoot.children = root.children;
        this.personalRoot.hasSelected = root.hasSelected;
        this.personalRoot.__id = __id++;
        this.setState({
          breads,
          list: null,
          tree: this.personalRoot,
        });
        break;
      }

      case N_TEAM: {
        const breads = [cooperRoot, this.teamListRoot, root];
        teamRoot.id = root.id;
        teamRoot.name = root.name;
        teamRoot.children = root.children;
        teamRoot.hasPermis = root.hasPermis;
        teamRoot.hasSelected = root.hasSelected;
        teamRoot.__id = __id++;
        this.setState({
          breads,
          list: null,
          tree: teamRoot,
        });
        break;
      }

      default:
        break;
    }
  };

  goPath = async (node) => {
    cooperRoot.disabled = this.props.disableRoot;
    this.teamListRoot.disabled = this.props.disableRoot;

    if (this.props.disableRoot && [N_ROOT, N_TEAMROOT].includes(node.id)) {
      // 不支持团队文件跨空间移动
      return;
    }

    switch (node.type) {
      case N_ROOT: {
        this.setState({
          breads: [cooperRoot],
          list: [this.personalRoot, this.teamListRoot],
          tree: null,
        });
        this.handleSelect(cooperRoot);
        break;
      }

      case N_TEAMROOT: {
        const res = await getTeams();
        const breads = [cooperRoot, this.teamListRoot];
        this.setState({
          breads,
          list: res,
          tree: null,
        });
        this.handleSelect(this.teamListRoot);
        break;
      }

      case N_PERSONAL: {
        const res = await getFolders(0);
        const breads = [cooperRoot, this.personalRoot];
        res.forEach((fold) => {
          fold.depth = 2;
        });
        this.personalRoot.children = res;
        this.personalRoot.__id = __id++;
        this.setState({
          breads,
          list: null,
          tree: this.personalRoot,
        });
        this.handleSelect(this.personalRoot);
        break;
      }

      case N_TEAM: {
        const res = await getFolders(0, node.id); // 既没有团队权限、也没有一级目录权限

        const childPermis = res.some((r) => r.hasPermis);

        if (!node.hasPermis && !childPermis) {
          const { list } = this.state;
          list.find((item) => item === node).disabled = true;
          this.setState({
            list,
          });
          return;
        } // 有上传权限

        const breads = [cooperRoot, this.teamListRoot, node];
        teamRoot.id = node.id;
        teamRoot.name = node.name;
        teamRoot.children = res;
        teamRoot.hasPermis = node.hasPermis;
        teamRoot.hasSelected = node.hasSelected;
        teamRoot.__id = __id++;
        teamRoot.children.forEach((child) => {
          child.depth = teamRoot.depth + 1;
        });
        this.setState({
          breads,
          list: null,
          tree: teamRoot,
        });
        this.handleSelect(teamRoot.hasPermis ? teamRoot : null);
        break;
      }

      default:
        break;
    }
  };

  handleSelect = (node) => {
    this.props.onSelect(node);
  };

  render() {
    const { breads, list, tree } = this.state;
    const { type, setAbleToCreate } = this.props;
    return (
      <div className='m-tree'>
        <MBread
          breads={breads}
          onClick={this.goPath} />
        <div className='container'>
          {list && <MList
            list={list}
            onClick={this.goPath} />}
          {tree && (
            <MListTree
              setAbleToCreate={setAbleToCreate}
              type={type}
              tree={tree}
              onSelect={this.handleSelect} />
          )}
        </div>
      </div>
    );
  }
}

export default MTree;
