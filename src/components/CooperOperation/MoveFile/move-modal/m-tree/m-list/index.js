import { intl } from 'di18n-react';
import React from 'react';
import { Tooltip } from 'antd';

import './index.less';

class MList extends React.Component {
  static defaultProps = {
    list: [],
    onClick: () => {},
  };

  render() {
    const { list, onClick } = this.props;
    return (
      <div className='m-list'>
        {list.length === 0 && <p>{intl.t('团队空间为空，请先创建团队')}</p>}
        {list.map((item) => (
          <div
            key={item.id}
            onClick={() => onClick(item)}
            className={item.disabled ? 'disabled' : ''}
          >
            {item.disabled ? (
              <Tooltip
                title={intl.t('您没有该空间的编辑或上传权限')}
                placement='right'
              >
                <span>
                  <img
                    src={item.icon}
                    alt='avatar' />
                  <span className='name'>{item.name}</span>
                </span>
              </Tooltip>
            ) : (
              <span>
                <img
                  src={item.icon}
                  alt='avatar' />
                <span className='name'>{item.name}</span>
              </span>
            )}
          </div>
        ))}
      </div>
    );
  }
}

export default MList;
