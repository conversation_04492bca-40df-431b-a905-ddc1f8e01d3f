
import { intl } from 'di18n-react';
import React from 'react';
import { Tooltip } from 'antd';

import './index.less';

class MBread extends React.Component {
  static defaultProps = {
    breads: [],
    onClick: () => {},
  };

  render() {
    const { breads, onClick } = this.props;
    return (
      breads.length >= 2 && (
        <div className='m-bread'>
          {breads[0].disabled ? (
            <Tooltip
              title={intl.t('请在当前团队空间内移动文件')}
              placement='right'
            >
              <span className='disabled'>{breads[0].name}</span>
            </Tooltip>
          ) : (
            <span
              className='bread-name'
              onClick={() => onClick(breads[0])}>{breads[0].name}</span>
          )}
          {breads.slice(1).map((bread) => (
            <span key={bread.id}>
              <i className='dk-iconfont dk-icon-youjiantou1' />
              {bread.disabled ? (
                <Tooltip
                  title={intl.t('请在当前团队空间内移动文件')}
                  placement='right'
                >
                  <span className='disabled'>{bread.name}</span>
                </Tooltip>
              ) : (
                <span
                  className='bread-name'
                  onClick={() => onClick(bread)}>{bread.name}</span>
              )}
            </span>
          ))}
        </div>
      )
    );
  }
}

export default MBread;
