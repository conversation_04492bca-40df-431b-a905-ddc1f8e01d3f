.m-bread {
  font-size: 14px;
  padding: 8px 20px 8px 16px; // 为了去掉滚动条距离右边的间距，将间距由m-tree调整至内侧的m-bread和m-list-tree
  color: #94a0b0;
  width: 100%;
  height: 40px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;

  img {
    width: 12px;
    margin: 0 4px;
  }

  > span {
    cursor: pointer;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    display: flex;
    align-items: center;

    &:hover {
      color: #06f;
    }

    &:last-child {
      color: #2f343c;
      font-weight: 500;
    }
    
  }
  .bread-name{
    max-width: 240px;
    display: inline-block;
    .ellipsis();
  }
  
  .disabled {
    cursor: not-allowed;
    display: flex;
    align-items: center;

    &:hover {
      color: #94a0b0;
    }
  }
}
