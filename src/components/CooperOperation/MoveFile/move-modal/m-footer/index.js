
import { intl } from 'di18n-react';
import React from 'react';
import { Button } from 'antd';

import { MOVE, COPY, SAVE, UPLOAD, DESSELECTOR } from '@/constants/cooper';
import { loadingPng } from '@/assets/icon/index';
import './index.less';

class MFooter extends React.Component {
  static defaultProps = {
    type: MOVE,
    disableCreate: true,
    disableMove: true,
    onCreate: () => {},
    onCancel: () => {},
    onMove: () => {},
  };

  render() {
    const {
      type,
      disableCreate,
      disableMove,
      onCreate,
      onCancel,
      onMove,
      loading = false,
    } = this.props;
    let text = intl.t('移动');
    if (type === COPY) text = intl.t('复制');
    if (type === SAVE || type === DESSELECTOR) text = intl.t('保存');
    if (type === UPLOAD) text = intl.t('上传');
    return (
      <div className='m-footer'>
        <Button
          disabled={disableCreate}
          onClick={onCreate}>
          {intl.t('新建文件夹')}
        </Button>
        <span className='right'>
          <Button onClick={onCancel}>{intl.t('取消')}</Button>
          <Button
            type='primary'
            disabled={disableMove}
            onClick={onMove}>
            {
              loading
                ? <img src={loadingPng}/>
                : text
            }
          </Button>
        </span>
      </div>
    );
  }
}

export default MFooter;
