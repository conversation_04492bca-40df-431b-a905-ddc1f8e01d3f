.m-footer {
  margin: 18px 0 0px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .ant-btn {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333;
    min-width: 90px;
    width: auto;
    height: 34px;
    border-radius: 3px;
    border: 1px solid #F4F4F4;

    &[disabled]:first-child {
      color: #333333;
      opacity: 60%;
      background-color: #fff !important;
    }
  }

  .right {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .ant-btn-primary {
      margin-left: 8px;
      background-color: #000;
      color: #fff;
      border: none;
      border-radius: 3px;
      width: 90px;
      height: 34px;
      display: flex;
      align-items: center;   
      justify-content: center;

      img {
        width: 18px;
        height: 18px;
        animation: rotate 1.5s linear infinite;
      }

      &[disabled] {
        background-color: #bbb ;
        color: #fff;
      }

      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }
    
        to {
          transform: rotate(360deg);
        }
      }
    }
  }
}
