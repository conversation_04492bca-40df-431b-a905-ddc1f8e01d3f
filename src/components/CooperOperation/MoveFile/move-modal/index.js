import { intl } from 'di18n-react';
import React from 'react';
import { Modal, message } from 'antd';
import MTitle from './m-title';
import MSearch from './m-search';
import MRecent from './m-recent';
import MTree from './m-tree';
import MFooter from './m-footer';
import showProgress from './m-progress/showProgress';
import { getTree, doMove, doCopy, doSave, getBussinessIds } from '@/service/cooper/moveFile';
import {
  COPY,
  MOVE,
  SAVE,
  UPLOAD,
  N_ROOT,
  N_TEAMROOT,
  N_PERSONAL,
  N_TEAM,
  N_NEWFOLD,
  N_DIR,
} from '@/constants/cooper';
import './index.less';
import { iconShareFolder } from '@/assets/icon/index';


class MoveModal extends React.Component {
  static defaultProps = {
    type: COPY,
    files: [
      {
        name: '文件',
      },
    ],
    teamId: 0,
    autoLocate: true,
    isShowPerson: !!localStorage.getItem('agent'),
    isDisabledFile: !!localStorage.getItem('agent'),
    doneCallback: () => { },
    cancelCallback: () => { },
    onClose: () => { },
  };

  state = {
    title: {},
    treeRoot: undefined,
    selected: null,
    disableCreate: false,
  };

  componentDidMount() {
    const { autoLocate, type, teamId, files } = this.props;
    this.formatTitle(); // 情况1：自动定位到当前目录

    // 修改getBussinessIds传参保证搜索下拿到正确的父级地址，parentLocationUrl字段为新搜索特有
    if (autoLocate) {
      const { parentId, teamId: _teamId } = getBussinessIds(files[0].parentLocationUrl);
      const { spaceType } = files[0];

      // 优先使用传入的 teamId
      const teamId = this.props.teamId || _teamId;

      if (parentId > 0) {
        // 文件夹
        this.initTree(parentId, N_DIR);
      } else if ((parentId === 0 && teamId === 0) || spaceType === 'PERSONAL_SPACE') {
        // 个人空间根目录
        this.initTree(parentId, N_PERSONAL);
      } else if (parentId === 0 && teamId > 0) {
        // 团队空间根目录
        this.initTree(teamId, N_TEAM);
      }

      return;
    } // 情况2：仅支持同团队下移动

    if (type === MOVE && teamId > 0) {
      this.initTree(teamId, N_TEAM);
    }
  }


  formatTitle = (dest = {}) => {
    const { files, type } = this.props;

    const src = files.length > 1
      ? files.length + intl.t('个文件')
      : (files[0] ? files[0].name || files[0].objectName || files[0].resourceName : '');
    const dst = dest.name ? dest.name : '...';

    const btnDisabled = !dest.name;

    this.setState({
      title: { type, src, dst },
      disableCreate: btnDisabled,
      disableMove: btnDisabled,
    });
  };

  handleSearchSelect = (item) => {
    // 更新 title
    this.formatTitle({
      name: item.name,
    }); // 刷新树

    this.initTree(item.id, item.type);
  };

  handleRecentSelect = (item) => {
    this.formatTitle({
      name: item.nameCn,
    });
    this.initTree(item.id, item.type);
  };

  handleTreeSelect = (item) => {
    if (!item || [N_ROOT, N_TEAMROOT].includes(item.id)) {
      this.formatTitle({
        name: '...',
      });
      this.setState({
        selected: null,
      });
    } else {
      this.formatTitle({
        name: item.name,
      });
      this.setState({
        selected: item,
      });
    }
  };

  createFolder = () => {
    const { selected } = this.state;

    if (!selected.hasPermis) {
      message.error(intl.t('您无权限创建'));
      return;
    }

    selected.hasChildren = true;
    selected.hasOpened = true;
    selected.children.unshift({
      id: N_NEWFOLD,
      name: '',
      icon: iconShareFolder,
      depth: selected.depth + 1,
      hasPermis: true,
    });
    this.setState({
      selected,
      disableCreate: true,
    });
  };

  calcDestIds = (item) => {
    let parentId; let
      toTeamId;

    if (item.id === N_PERSONAL) {
      parentId = 0;
      toTeamId = 0;
    } else if (item.type === N_TEAM) {
      parentId = 0;
      toTeamId = item.id;
    } else {
      parentId = item.id;
      toTeamId = item.teamId || 0;
    }

    return {
      parentId,
      toTeamId,
    };
  };

  handleMove = async () => {
    const ids = this.props.files.map((file) => file.resourceId || file.objectId || file.id);
    const fromTeamId = this.props.teamId;
    const { parentId, toTeamId } = this.calcDestIds(this.state.selected);
    doMove(ids, fromTeamId, parentId, toTeamId).then(() => {
      this.props.onClose();
      const link = toTeamId
        ? `/team-file/${toTeamId}/${parentId}`
        : `/files/${parentId}`;
      showProgress(MOVE, 0, ids.length, link, this.props.doneCallback);
    }).catch((err) => {
      if (err.errorCode === 501082) {
        message.error(intl.t('您没有权限! 请联系空间管理员添加目标位置的编辑/上传权限。'));
      } else {
        message.error(err.message);
      }
    })
  };

  handleCopy = async () => {
    const ids = this.props.files.map((file) => file.resourceId || file.objectId || file.id);
    const { parentId, toTeamId } = this.calcDestIds(this.state.selected);
    doCopy(ids, parentId, toTeamId).then((res) => {
      let copyId = res.fileProgressId
      this.props.onClose();
      const link = toTeamId
        ? `/team-file/${toTeamId}/${parentId}`
        : `/files/${parentId}`;
      showProgress(COPY, copyId, ids.length, link, this.props.doneCallback);
    }).catch((err) => {
      if (err.errorCode === 1082) {
        message.error(intl.t('您没有权限! 请联系空间管理员添加目标位置的编辑/上传权限。'));
      } else {
        message.error(err.message);
      }
    })
  };

  handleSave = async () => {
    const { files, doneCallback } = this.props;
    const ids = files.map((file) => file.id);
    const { shareId, shareType } = files[0];
    const { parentId, toTeamId } = this.calcDestIds(this.state.selected);
    doSave(ids, shareId, shareType, parentId, toTeamId).then((res) => {
      this.props.onClose();
      const link = toTeamId
        ? `/team-file/${toTeamId}/${parentId}`
        : `/files/${parentId}`;
      showProgress(SAVE, 0, ids.length, link, doneCallback);
    }).catch((err) => {
      if (err.errorCode === 901039) {
        message.error(intl.t('您没有权限! 请联系空间管理员添加目标位置的编辑/上传权限。'));
      } else {
        message.error(err.message);
      }
    });
  };

  handleUpload = () => {
    const { parentId, toTeamId } = this.calcDestIds(this.state.selected);
    const spaceInfo = {
      parentId,
      teamId: toTeamId,
    };
    this.props.doneCallback(spaceInfo);
  };

  handleCancel = () => {
    this.props.onClose();
    this.props.cancelCallback();
  };

  getMoveCallback = () => {
    const { type } = this.props;
    if (type === MOVE) return this.handleMove;
    if (type === COPY) return this.handleCopy;
    if (type === SAVE) return this.handleSave;
    if (type === UPLOAD) return this.handleUpload;
    return () => { };
  };

  initTree = async (id, type) => {
    const res = await getTree(id, type);
    this.setState({
      treeRoot: res,
    });
  };

  setAbleToCreate = () => {
    this.setState({ disableCreate: false });
  }

  render() {
    const { teamId, type } = this.props;
    const { title, treeRoot, selected, disableCreate } = this.state;
    const disableRoot = (type === MOVE) && teamId > 0;
    return (
      <Modal
        wrapClassName='move-modal'
        title={<MTitle {...title} />}
        width={520}
        visible={true}
        onCancel={this.handleCancel}
        footer={null}
      >
        <MSearch
          type={type}
          spaceId={teamId}
          onSelect={this.handleSearchSelect}
        />
        <MRecent
          type={type}
          spaceId={teamId}
          selected={selected}
          onClick={this.handleRecentSelect}
        />
        <MTree
          type={type}
          treeRoot={treeRoot}
          disableRoot={disableRoot}
          onSelect={this.handleTreeSelect}
          setAbleToCreate={this.setAbleToCreate}
        />
        <p className='mv-tip'>
          {disableRoot && (
            intl.t('请在当前团队空间内移动文件，如需跨团队操作请使用“复制”功能')
          )}
        </p>
        <MFooter
          type={type}
          disableCreate={!selected || disableCreate}
          disableMove={!selected}
          onCreate={this.createFolder}
          onCancel={this.handleCancel}
          onMove={this.getMoveCallback()}
        />
      </Modal>
    );
  }
}

export default MoveModal;
