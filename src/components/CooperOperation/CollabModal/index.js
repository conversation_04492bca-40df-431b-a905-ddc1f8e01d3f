import { intl } from 'di18n-react';
import React, { useState } from 'react';
import { Modal } from 'antd';
import classNames from 'classnames/bind';
import mountAnywhere from '@/utils/mountAnywhere';
import MemberListOfDK from '@/components/serviceComponents/MemberListOfDK';
import AddNewCollaborator from '@/components/serviceComponents/AddNewCollaborator';
import MemberListOfResource from '@/components/CooperFoldAuth/MemberListOfResource';
import styles from './style.module.less';

const cls = classNames.bind(styles);

const CollabModal = (props) => {
  const { onClose, id, info } = props;
  const { teamId, spaceId, team_id, space_id, sourceId, isDkPage, isTeam } = info;
  const [ step, setStep ] = useState(0);

  return (
    <Modal
      width={540}
      centered={true}
      destroyOnClose={true}
      className={cls('modal-setting')}
      visible={true}
      title={null}
      footer={null}
      onCancel={onClose}
      closable={false}
    >
      <div>
        {
          step === 0 && (
            <div className={cls('share-collaborator')}>
              {
                isDkPage ? (
                  <MemberListOfDK
                    isDkPage={isDkPage}
                    needDKDetail={true}
                    resourceId={id}
                    teamId={teamId || spaceId || team_id || space_id || sourceId}
                    teamIdAllTeam={teamId || spaceId || team_id || sourceId}
                    title={intl.t('协作者')}
                    permission={info.perm}
                    roleKey={info.roleKey}
                    handleBack={() => setStep(0)}
                    gotoNext={() => setStep(1)}
                    info={info}
                  />
                ) : (
                  <MemberListOfResource 
                    resourceId={id}
                    isInOperate={true}
                    teamId={teamId || spaceId || team_id || sourceId}
                    teamIdAllTeam={teamId || spaceId || team_id || sourceId}
                    isPersonalTeam={!isTeam}
                    changeCooperTotalCount={() => {}}
                    info={info}
                    showBack={false}
                    onRemoved={() => {}}
                    onPermChange={() => {}}
                    refreshDocSdk={() => {}}
                    toggleChildVisible={() => {}}
                    handleBack={() => setStep(0)}
                    gotoNext={() => setStep(1)}
                    onCloseUserModal={() => {}}
                    resourceType={'doc'}
                  />
                )
              }
            </div>
          )
        }
        {
          step === 1 && (
            <div className={cls('add-collaborator')}>
              <AddNewCollaborator
                isDkPage={isDkPage}
                resourceId={id}
                fileType={info.fileType}
                teamId={teamId || spaceId || team_id || sourceId}
                showBack={true}
                backToFirstStep={() => setStep(0)}
                handleBack={() => setStep(0)}
                closeShare={() => {
                  setStep(0);
                  onClose();
                }}
              />
            </div>
          )
        }
      </div>
    </Modal>
  )
}

export default function collabModal(id, info) {
  const Comp =(
    <CollabModal 
      id={id}
      info={info}
    />
  );
  mountAnywhere(Comp);
}

