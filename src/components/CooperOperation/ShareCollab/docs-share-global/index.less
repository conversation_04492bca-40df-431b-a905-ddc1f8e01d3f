

@color-primary: #06f;
.cooper-window__sharecoop {
  .ant-modal-body {
    padding: 16px 0;
  }

  .ant-modal-header {
    padding-top: 20px;
    padding-bottom: 20px;
  }
  .ant-modal-title {
    font-size: 16px;
    color: #333;
    line-height: 22px;
    font-weight: normal;
  }
  .ant-modal-close-x {
    font-size: 20px;
    line-height: 62px;
  }
  .ant-select-selection--single {
    height: 40px;
  }
  .ant-select-selection__placeholder, .ant-select-search__field__placeholder {
    height: 36px;
    line-height: 36px;
  }
  .ant-select-combobox .ant-select-search__field {
    height: 40px;
  }
  .ant-table-tbody > tr > td {
    border-bottom: none;
  }
  .ant-table-wrapper {
    max-height: 150px;
    overflow: scroll;
  }
  .ant-table-placeholder {
    border: none;
  }
}


.share-modal__form {
  padding: 0 0 24px 0;
  border-bottom: 1px solid #F1F1F4;

  .modal-field {
    margin-bottom: 24px;
    display: flex;
    &:last-child {
      margin-bottom: 0;
    }
    &.modal-field__radio {
      display: flex;
      align-items: flex-start;
      margin-bottom: 14px;
      .ant-radio-wrapper,
      .ant-checkbox-wrapper {
        position: relative;
        margin-right: 8px;
        bottom: 2px;
      }
      .ant-select-selection-selected-value {
        font-size: 14px;
        color: #17233e;
      }

      .ant-select-selection {
        margin-bottom: 0;
        border-color: #e4e9f3;
      }

      .ant-checkbox-inner {
        border-radius: 7px;
        border-color: #bdc5d2 !important;
      }

      .ant-checkbox-checked .ant-checkbox-inner {
        background-color: #3f81ff !important;
        border-color: #3f81ff !important;
      }

      .ant-select-selection:hover,
      .ant-select-selection:focus {
        border-color: #3f81ff;
      }
    }
  }

  .field-name {
    display: inline-block;
    width: 80px;
    line-height: 26px;
    margin-right: 20px;
    text-align: right;
    font-size: 14px;
    color: #17233E;
    vertical-align: middle;
    &.field-sharename {
      vertical-align: top;
    }
  }

  .ant-radio-group small{
    font-size: 14px;
    color: #8a93a8;
    margin-left: 8px;
  }

  span.ant-radio + * {
    font-size: 14px;
  }

  .ant-radio-button-wrapper-checked {
    color: @color-primary;
    border-color: @color-primary;
    box-shadow: -1px 0 0 0 @color-primary;
  }
  .ant-radio-button-wrapper-checked:hover {
    border-color: @color-primary;
    box-shadow: -1px 0 0 0 @color-primary;
  }
  .ant-radio-button-wrapper:hover, .ant-radio-button-wrapper-focused {
    color: @color-primary;
  }
}

.share-modal__shareform {
  margin-top: 24px;
  border: none;
  .share-input__wrapper {
    position: relative;
    display: inline-block;
    flex: 1;
    // width: 456px;
    border-radius: 4px;
    border: 1px solid #e4e9f3;
    input {
      display: inline-block;
      padding: 7px;
      line-height: 30px;
      margin-right: 0;
      border-radius: 4px;
      width: 100%;
      height: 30px;
      outline: none;
      overflow: hidden;
      text-overflow: ellipsis;
      border: none;
      font-size: 14px;
      color: #17233E;
    }
    &.active {
      padding-right: 147px;
    }
    .share-pwd {
      position: absolute;
      right: 0;
      top: 0;
      width: 140px;
      bottom: 0;
      border-left: 1px solid #e4e9f3;
      text-align: center;
      line-height: 32px;
      color: #8a93a8;
      font-size: 14px;
    }
  }
}

.share-modal__actions {
  // margin: 0 24px;
  margin-top: 16px;
  text-align: right;
  .btn-share__cancel {
      background: #FFF;
      color: #17233e;
      padding: 5px 19px;
      line-height: 20px;
      font-size: 16px;
      outline: none;
      font-weight: normal;
      cursor: pointer;
      border: 1px solid #e4e9f3;
      margin-right: 10px;
      border-radius: 4px;
      transition: color .3s ease;
      font-size: 14px;
      &:hover {
        color: @color-primary;
        border: 1px solid @color-primary;
      }
  }

  .btn-share__ok {
      background: none;
      background: @color-primary;
      color: #FFF;
      padding: 5px 15px;
      line-height: 20px;
      font-size: 14px;
      outline: none;
      font-weight: normal;
      cursor: pointer;
      border-radius: 4px;
      transition: background .3s ease;
      border: 1px solid transparent;
      &:hover {
        background: @color-primary;
      }
  }
}

.share-modal__tip {
  margin-top: 8px;
  text-align: center;
  color: #ff3f5a;
  font-size: 14px;
  padding-bottom: 20px;
}

.share-perm {
  .perm-label {
    color: #666;
    font-size: 14px;
  }

  .ant-checkbox-disabled + span .perm-label {
    color: #999;
  }

  .ant-checkbox-group {
    display: inline-block;
  }
}
