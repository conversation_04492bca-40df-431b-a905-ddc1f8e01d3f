import { intl } from 'di18n-react';
import React from 'react';

import PrimaryModal from '@/components/common/PrimaryModal';
import DocsShareGlobal from './docs-share-global';
import mountAnywhere from '@/utils/mountAnywhere';

class ShareCollab extends React.Component {
  static defaultProps = {
    id: 0,
    info: {},
    onClose: () => {},
  };

  render() {
    const { id, info, onClose } = this.props;
    return (
      <PrimaryModal
        title={intl.t('分享')}
        wrapClassName='share-collab'
        width={605}
        footer={null}
        onCancel={onClose}
      >
        <DocsShareGlobal
          id={id}
          info={info}
          onCancel={onClose}
        />
      </PrimaryModal>
    );
  }
}

export default function shareCollab(id, info) {
  const Comp = <ShareCollab
    id={id}
    info={info} />;
  mountAnywhere(Comp);
}
