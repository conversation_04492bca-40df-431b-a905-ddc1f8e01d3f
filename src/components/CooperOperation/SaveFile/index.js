/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-08-09 14:18:33
 * @LastEditTime: 2023-08-09 14:38:59
 * @Description: 简单描述
 * @FilePath: /knowledgeforge/src/components/CooperOperation/SaveFile/index.js
 *
 */
import React from 'react';
import OperateFileModal from '@/components/OperateFileModal';
import { SAVE } from '@/constants/cooper';
import mountAnywhere from '@/utils/mountAnywhere';
import store from '@/model';
import { Provider } from 'react-redux';

function saveFile(files, isBatch) {
  const Comp = (
    <Provider store={store}>
      <OperateFileModal
        type={SAVE}
        files={files}
        autoLocate={false}
        onClose={() => close()}
        isFromPerson={false}
        isBatch={isBatch}
    />
    </Provider>

  );
  let close = mountAnywhere(Comp);
}

export default saveFile;
