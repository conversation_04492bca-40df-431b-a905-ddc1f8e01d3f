@import '../../../components/theme.scss';

.ant-modal-mask {
  background-color: rgba(#000, 0.1);
}

.do-copy {
  .ant-modal-body {
    padding: 0;
  }

  .ant-confirm-body {
    span {
      display: inline-block;
      width: 100%;
      height: 56px;
      line-height: 56px;
      border-bottom: 1px solid #e5e6ec;
      padding-left: 24px;
    }
  }

  .ant-confirm-title,
  .ant-confirm-content {
    font-size: 16px;
    font-weight: $fontWeight;
    color: $textColor;
  }

  .ant-confirm-content {
    margin-left: 0;
    line-height: 22px;

    .tree-wrap {
      height: 250px;
      margin: 16px 24px 40px;
      border: 1px solid #e4e9f3;
      border-radius: 4px;
      padding-top: 8px;
    }
  }

  .ant-confirm-btns {
    margin: 0 24px 16px;

    button {
      min-width: 68px;
      font-weight: $fontWeight;

      &:first-child {
        border-color: #e4e9f3;
        color: $textColor;
      }

      &:first-child:hover {
        color: $hoverColor;
        border-color: $hoverColor;
      }

      &.ant-btn-primary {
        background-color: $brandColor;
        border: none;

        &:hover {
          background-color: $hoverColor;
        }
      }
    }
  }

  .anticon-question-circle {
    display: none;
  }

  .icon-close {
    position: absolute;
    top: 22px;
    right: 24px;
    display: block;
    width: 12px;
    height: 12px;
    background-image: url(./icon/icon_guanbi.svg);
    background-size: 12px 12px;
    cursor: pointer;

    &:hover {
      background-image: url(./icon/icon_guanbi_hover.svg);
    }
  }
}

.copy-progress {
  width: 380px;
  box-shadow: 0 2px 8px 0 rgba(23, 35, 62, 0.2);
  padding: 0;
  margin: 0;
  right: 32px;
  top: 13px;

  .ant-notification-notice-message,
  .ant-notification-notice-close-x {
    display: none;
  }

  .outer {
    position: relative;
    height: 36px;
  }

  .inner {
    width: 40%;
    background-color: rgba(#108ee9, 0.2);
    height: 100%;
  }

  .text {
    position: absolute;
    text-indent: 50px;
    height: 36px;
    font-size: 14px;
    line-height: 36px;
    color: #17233e;
    top: 0px;
  }
}
