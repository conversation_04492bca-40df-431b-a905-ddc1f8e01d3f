import { intl } from 'di18n-react';
import React from 'react';
import { message } from 'antd';
import PrimaryModal from '@/components/common/PrimaryModal';
import cooperConfirm from '@/components/common/CooperConfirm';
import { get, post } from '@/utils/request/cooper';
// import { OWNER_CHECK, APPLY_OWNER } from '../../../api';
import api from '@/utils/request/api/CooperApi';
import './index.less';
import { sendEvent } from '@/constants/cooperMirrorEvent';
import { isDiDiTenant } from '@/utils/entryEnhance'

let debounceFlag =  false
class ApplyOwner extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      apvEn: '',
      apvCn: '',
      apvRole: 0,
      reason: '',
      bpmUrl: 'TBD',
      confirmLoading: false,
    };
    this.textareaRef = null;
    this.textareaFocused = false;
  }

      /**
   * 
   * @params 
   * ['tips1', {
   *    apvEn: 'liuruimin',
   *    apvCn: '刘睿敏'
   *  }, 'tips2']
   * 
   */
  intlTips = (params) => {
    return params.map(item => {
      if (typeof item == 'object') {
        return <a
        className='user-link'
        target='_blank'
        href={`dchat://im/start_conversation?name=${item.apvEn}`}
      >
        {item.apvCn}
      </a>
      } else {
        return intl.t(item)
      }
    })
  }

  async componentDidMount() {
    try {
      const { docId } = this.props;
      const res = await get(api.OWNER_CHECK.replace(':id', docId));
      const { code, approver, bpmUrl, applicant } = res;
      const { ldap: apvEn, cnName: apvCn, role: apvRole } = approver;
      const { ldap: appEn, cnName: appCn, role: appRole } = applicant;
      if (code === 10002) {
        cooperConfirm({
          type: 'warn',
          title: this.intlTips(['已有团队空间成员',{ apvEn: appEn, apvCn: appCn ? appCn : appEn }, '提交空间所有权的申请，你无法提交。如需申请可先联系',{ apvEn: appEn, apvCn: appCn ? appCn : appEn },'撤销所有权申请后你再尝试申请。']),
          onOk: null,
          okText: intl.t('知道了'),
          hiddenCancel: true
        });
        this.props.onClose();
      }  else if (code === 10001) {
        // 申请重复
        cooperConfirm({
          type: 'success',
          title: intl.t(
            '你的申请已提交，由{one}进行审批，请耐心等待。如需查看进度可前往BPM（倚天流程中心）',
            {
              one: <a
              className='user-link'
              href={`dchat://im/start_conversation?name=${apvEn}`}
              target='_blank'
            >
              {apvCn}
            </a>,
            },
          ),
          onOk: () => {
            window.open(bpmUrl);
          },
          okText: intl.t('查看详情'),
          cancelText: intl.t('知道了'),
        });
        this.props.onClose();
      } else {
        // 未申请(bpmUrl='')
        this.setState({
          apvEn,
          apvCn,
          apvRole,
          bpmUrl,
        });
      }
    } catch (err) {
      message.info(err?.errorMessage);
      this.props.onClose();
    }
  }

  componentDidUpdate() {
    if (!this.textareaFocused && this.textareaRef) {
      this.textareaFocused = true;
      this.textareaRef.focus();
    }
  }

  // doApply = async () => {
  //   try {
  //     const { docId } = this.props;
  //     const { reason } = this.state;
  //     sendEvent('APPLY_TEAM_OWNER_OK');
  //     this.setState({
  //       confirmLoading: true,
  //     });
  //     const res = await post(api.APPLY_OWNER.replace(':id', docId), {
  //       reason,
  //     });
  //     if (res) {
  //       const { approver, bpmUrl } = res;
  //       const { cnName: apvCn } = approver;
  //       this.setState({
  //         confirmLoading: false,
  //       });
  //       this.props.onClose();
  //       cooperConfirm({
  //         type: 'success',
  //         title: isDiDiTenant() ? intl.t(
  //           '你的申请已提交，由{one}进行审批，请耐心等待。如需查看进度可前往BPM（倚天流程中心）',
  //           {
  //             one: apvCn,
  //           },
  //         ) : intl.t(
  //           '您的申请已提交，将由 ({one}) 进行审批。',
  //           {
  //             one: apvCn,
  //           },
  //         ),
  //         onOk: isDiDiTenant() ? () => window.open(bpmUrl) : () => {},
  //         okText: isDiDiTenant() ? intl.t('查看详情') : intl.t('知道了'),
  //         cancelText: isDiDiTenant() ? intl.t('知道了') : '',
  //       });
  //     }
  //   } catch (err) {
  //     message.info(err.response.data.message);
  //   }
  // };

  doApply = async () => {
    if (!debounceFlag) {
      debounceFlag = true
      try {
        const { docId } = this.props;
        const { reason } = this.state;
        sendEvent('APPLY_TEAM_OWNER_OK');
        this.setState({
          confirmLoading: true,
        });
        const res = await post(api.APPLY_OWNER.replace(':id', docId), {
          reason,
        });
        if (res) {
          const { approver, bpmUrl, code } = res;
          const { cnName: apvCn, ldap: apvEn, } = approver;
          this.setState({
            confirmLoading: false,
          });
          this.props.onClose();
          // 判断是成功，还有有其他错误审批逻辑,否则默认走成功逻辑
          if (code == 10004) {
            cooperConfirm({
              type: 'success',
              title: intl.t('你提交的申请已通过, 您已成为空间所有者'),
              onOk: () => {},
              okText: intl.t('知道了'),
              cancelText: '',
            });
          } else {
            cooperConfirm({
              type: 'success',
              title: isDiDiTenant() ? intl.t(
                '你的申请已提交，由{one}进行审批，请耐心等待。如需查看进度可前往BPM（倚天流程中心）',
                {
                  one: <a
                  className='user-link'
                  href={`dchat://im/start_conversation?name=${apvEn}`}
                  target='_blank'
                >
                  {apvCn}
                </a>,
                },
              ) : intl.t(
                '您的申请已提交，将由 ({one}) 进行审批。',
                {
                  one: <a
                  className='user-link'
                  href={`dchat://im/start_conversation?name=${apvEn}`}
                  target='_blank'
                >
                  {apvCn}
                </a>,
                },
              ),
              onOk: isDiDiTenant() ? () => window.open(bpmUrl) : () => {},
              okText: isDiDiTenant() ? intl.t('查看详情') : intl.t('知道了'),
              cancelText: isDiDiTenant() ? intl.t('知道了') : '',
            });
          }
        }
        // 等待成功之后 再放开操作
        debounceFlag = false
      } catch (err) {
        message.info(err.response.data.message);
      }
    } else {
      return () => {}
    }
  };

  render() {
    const { onClose } = this.props;
    const { apvEn, apvCn, reason, bpmUrl, apvRole, confirmLoading } = this.state; // 已申请过

    if (bpmUrl) return null;
    return (
      <PrimaryModal
        title={intl.t('申请成为团队空间所有者')}
        okText={intl.t('确认申请')}
        onCancel={onClose}
        onOk={this.doApply}
        confirmLoading={confirmLoading}
        selfClassName='team-apply-owner-ap'
      >
        <div
          className='apply-owner'
          id='team-apply-owner'>
          <div className='title'>
            {intl.t('申请理由')}
            <span className={reason.length >= 200 ? 'warn' : ''}>
              {reason.length}/200
            </span>
          </div>
          <textarea
            value={reason}
            onChange={(e) => this.setState({
              reason: e.target.value.slice(0, 200),
            })
            }
            ref={(r) => { this.textareaRef = r }}
          />
          <div className='foot'>
            {/* <span className='warn'>*</span> */}
            {/* apvRole：0 - 所有者，1 - 直接上级， 2 - 申请者上级 */}
            {/* {apvRole === 0 && intl.t('此申请会提交到空间所有者')}
            {apvRole === 1 && intl.t('此申请会提交到空间原所有者直属上级')}
            {apvRole === 2 && intl.t('此申请会提交到你的直属上级{apv}进行审批，审批通过后变更生效', {
              apv: <a href={`dchat://im/start_conversation?name=${apvEn}`}>{apvCn}</a>,
            })} */}
             <a  className='approveTips' href='https://cooper.didichuxing.com/knowledge/share/book/V2fuj0ncesoZ/2202845586005' target='_blank'>{intl.t('点击查看审批人逻辑')} <i class="dk-iconfont dk-icon-shouqi more-tip"></i></a>
          </div>
        </div>
      </PrimaryModal>
    );
  }
}

export default ApplyOwner;
