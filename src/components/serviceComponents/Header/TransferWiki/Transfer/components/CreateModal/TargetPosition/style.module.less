.container {
  margin-top: 30px;

  .label {
    font-weight: 500;
  }

  .radio {
    margin-top: 8px;
    margin-bottom: 30px;

    :global(.ant-radio-inner) {
      width: 16px;
      height: 16px;
      box-shadow: none !important;
    }

    :global(.ant-radio-wrapper-checked) {
      :global(.ant-radio-inner) {
        border: 4.5px solid #047ffe !important;
      }
    }

    :global(.ant-radio-wrapper) {
      &>span:nth-child(2) {
        padding-left: 6.5px !important;
      }
    }

    :global(.ant-radio-wrapper) {
      margin-right: 24px !important;
    }

  }

 .tri {
      position: absolute;
      bottom: -16px;
      left: 7%;
      transform: translate(-50%, 0);
      width: 0;
      height: 0;
      border: 10px solid;
      border-color: transparent transparent #f4f6f8 transparent;
    }

  .desc {
    border-radius: 4px;
    padding: 16px 12px;
    background-color: #f4f6f8;
    margin-top: 16px;
  }
  .urlIpt {
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
    .label {
      font-weight: 500;
    }

    .ipt {
      flex: 1;
      display: flex;
      input {
        // flex: 1;
        width: 100%;
        border-radius: 4px;
        padding: 6px 12px;
        outline: none;
        border: 1px solid #d3d4d7;

        &::placeholder {
          color: #bdc0c3;
        }

        &:focus {
          border-color: #047FFE;
        }
      }
      .check{
        // width: 60px;
        height: 36px;
        margin-left: 8px;
      }
    }
  }
}