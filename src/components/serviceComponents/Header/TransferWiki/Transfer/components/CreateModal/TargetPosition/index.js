/* eslint-disable react/jsx-max-props-per-line */
/* eslint-disable operator-linebreak */
import styles from './style.module.less';
import classBind from 'classnames/bind';
import { Radio, Button } from 'antd';
import { useEffect, useState, useCallback } from 'react';
import useNotification from '@/hooks/useNotification';
import { wikiTaskSubnodeLink } from '@/service/knowledge/wikiTransfer';
import NotificationStatus from '@/constants/notification';

const cx = classBind.bind(styles);
const urlPattern = /^https?:\/\/(cooper(-(test|qa|vpn))?\.didichuxing\.com)\/knowledge/;

const TargetStep = ({ check, data: { targetValue, urlTarget,url, calculatedCapacity: savedCapacity, isUrlValidated: savedUrlValidated, urlrange, knowledgeId: savedKnowledgeId, rangValue }, setData }) => {
  const [checkButton, setCheckButton] = useState(false);// 校验按钮状态
  const [isValidating, setIsValidating] = useState(false);// loading
  const [calculatedCapacity, setCalculatedCapacity] = useState(savedCapacity || '待计算');
  const [isUrlValidated, setIsUrlValidated] = useState(savedUrlValidated || false);//是否已校验过

  const checkUrlValid = useCallback((v) => {
    return urlPattern.test(v);
  }, []);
  const notification = useNotification();
  // 检查容量是否为负数
  const isCapacitySufficient = useCallback((capacity) => {
    if (capacity === '待计算' || capacity === '计算中...') {
      return false;
    }
    if (capacity < 0) {
      return false;
    } else {
      return true; 
    }
  }, []);

  // 重置校验状态的公共函数
  const resetValidationState = useCallback((capacity = '待计算', isValidated = false, knowledgeId = null, taskId = null) => {
    setCalculatedCapacity(capacity);
    setIsUrlValidated(isValidated);
    setData((v) => ({
      ...v,
      calculatedCapacity: capacity,
      isUrlValidated: isValidated,
      knowledgeId: knowledgeId,
      taskId: taskId,
    }));
  }, [setData]);

  const handleValidation = useCallback(async () => {
    if (!checkButton || isValidating) {
      return;
    }

    setIsValidating(true);
    
    try {
      const params = {
         knowledgeUrl: urlTarget,
         wikiUrl: rangValue ? url : urlrange,
         subNodeImportType: rangValue ? 'space': 'node'
      }
      const res = await wikiTaskSubnodeLink({ ...params});

      // 检查链接有效性
      if (!res.knowledgeUrlValid) {
        notification(NotificationStatus.ERROR, res.knwoledgeValidDetail);
        resetValidationState();
        return;
      }

      const { knowledgePagesLeft, knowledgeId, taskId } = res;
      
      // 处理容量计算结果
      if (knowledgePagesLeft === null) {
        const errorMsg = res.pagesCountStatus || '容量计算有误,请稍后重试';
        notification(NotificationStatus.ERROR, errorMsg);
        resetValidationState();
        return;
      }

      if (knowledgePagesLeft <= 0) {
        notification(NotificationStatus.ERROR, '目标知识库容量不足');
        resetValidationState(knowledgePagesLeft);
        return;
      }

      // 容量充足，校验成功
      resetValidationState(knowledgePagesLeft, true, knowledgeId, taskId);
      
    } catch (error) {
      console.error('校验失败:', error);
      resetValidationState();
    } finally {
      setIsValidating(false);
    }
  }, [checkButton, isValidating, urlTarget, urlrange, url, notification, resetValidationState]);

  // 输入或粘贴已有知识库链接
  const handChange = useCallback((e) => {
    const { value } = e.target;
    setData((v) => ({ ...v, urlTarget: value }));
    const isValid = checkUrlValid(value);
    setCheckButton(isValid);

    // 当用户重新输入时，检查是否是新的URL
    if (value !== urlTarget) {
      // 如果是新的URL，重置校验状态
      setIsUrlValidated(false);
      setCalculatedCapacity('待计算');
      setData((v) => ({
        ...v,
        calculatedCapacity: '待计算',
        isUrlValidated: false,
        knowledgeId: null,
        taskId: null
      }));
    }
  }, [checkUrlValid, urlTarget, setData]);

  const onChange = (e) => {
    const { value } = e.target;
    const isNewKb = value === '新建同名知识库并迁移';
    setData((v) => ({
      ...v,
      targetValue: isNewKb,
      // 选择"新建同名知识库并迁移"时清空 urlTarget
      urlTarget: isNewKb ? '' : v.urlTarget
    }));

    if (isNewKb) {
      // 切换选项时重置状态
      setCalculatedCapacity('待计算');
      setIsUrlValidated(false);
      setData((v) => ({
        ...v,
        calculatedCapacity: '待计算',
        isUrlValidated: false,
        knowledgeId: null,
        taskId: null,
      }));
      
    } else {
      // 切换到"迁移至已有知识库"时，检查URL格式
      if (urlTarget) {
        const isValid = checkUrlValid(urlTarget);
        setCheckButton(isValid);
      } else {
        setCheckButton(false);
      }
    }
  };

  // 当从其他步骤返回时，恢复保存的状态
  useEffect(() => {
    if (savedCapacity && savedCapacity !== '待计算') {
      setCalculatedCapacity(savedCapacity);
    }
    if (savedUrlValidated) {
      setIsUrlValidated(savedUrlValidated);
    }
  //   // 恢复knowledgeId
  //   if (savedKnowledgeId) {
  //     setData((v) => ({
  //       ...v,
  //       knowledgeId: savedKnowledgeId
  //     }));
  //   }
  }, [savedCapacity, savedUrlValidated, savedKnowledgeId, setData]);

  // 监听urlTarget变化，更新校验按钮状态
  useEffect(() => {
    if (!targetValue && urlTarget) {
      // 只有在选择"迁移至已有知识库"且有URL时才检查
      const isValid = checkUrlValid(urlTarget);
      setCheckButton(isValid);
    }
  }, [urlTarget, targetValue, checkUrlValid]);

  useEffect(() => {
    // 根据是否选择"新建"或"已校验URL"来决定"下一步"按钮是否可用
    // 同时检查容量是否为负数
    const canProceed = targetValue || (isUrlValidated && isCapacitySufficient(calculatedCapacity));
    check(canProceed);
  }, [targetValue, isUrlValidated, calculatedCapacity, isCapacitySufficient, check]);

  return (
    <div className={cx('container')}>
      <div className={cx('label')}>
        选择迁移目标位置
      </div>
      <div className={cx('radio')}>
        <Radio.Group
          value={targetValue ? '新建同名知识库并迁移' : '迁移至已有知识库'}
          onChange={onChange}
        >
          <Radio
            value='新建同名知识库并迁移'
            style={{ position: 'relative' }}
          >
            新建同名知识库并迁移
          </Radio>
          <Radio
            value='迁移至已有知识库'
            style={{ position: 'relative' }}
          >
            迁移至已有知识库
          </Radio>
        </Radio.Group>
      </div>

      {!targetValue && <div>
        <div className={cx('urlIpt')}>
          <span className={cx('label')}>输入或粘贴已有知识库链接</span>
          <div className={cx('ipt')}>
            <input
              autoFocus
              placeholder='输入或粘贴已有知识库链接'
              value={urlTarget}
              onChange={handChange}
            />
            <Button
              type="primary"
              loading={isValidating}
              onClick={handleValidation}
              disabled={!checkButton || isValidating}
              className={cx('check')}
            >校验</Button>
          </div>
          {!targetValue && <div className={cx('tri')} />}
        </div>

        <div className={`${cx('desc')} custom-rich-text`}>
          <ul className='text-sm text-color-para'>
            <li>
              新建知识库：需对知识库链接进行容量校验后，才能进入下一步。
            </li>
            <li>
              新建知识库：预计迁移后页面剩余容量: 
              <span style={{ 
                color: typeof calculatedCapacity === 'number' ? (calculatedCapacity < 0 ? '#FF563B' : '#047FFE') : 'inherit'
              }}>
              &nbsp;{calculatedCapacity}
              </span>
            </li>
            <li>
              迁移到已有知识库：迁移后，会将迁移内容存放在当前知识库的顶层目录进行展示
            </li>
          </ul>
        </div>
      </div>
      }
    </div>
  )
}

export default TargetStep;
