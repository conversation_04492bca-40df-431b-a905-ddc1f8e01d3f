.container {
  margin-top: 30px;

  .label {
    font-weight: 500;
  }

  .radio {
    margin-top: 8px;
    margin-bottom: 16px;

    :global(.ant-radio-inner) {
      width: 16px;
      height: 16px;
      box-shadow: none !important;
    }

    :global(.ant-radio-wrapper-checked) {
      :global(.ant-radio-inner) {
        border: 4.5px solid #047ffe !important;
      }
    }

    :global(.ant-radio-wrapper) {
      &>span:nth-child(2) {
        padding-left: 6.5px !important;
      }
    }

    :global(.ant-radio-wrapper) {
      margin-right: 24px !important;
    }

    .tri {
      position: absolute;
      bottom: -16px;
      left: 60%;
      transform: translate(-50%, 0);
      width: 0;
      height: 0;
      border: 10px solid;
      border-color: transparent transparent #f4f6f8 transparent;
    }
  }

  .desc {
    border-radius: 4px;
    padding: 16px;
    background-color: #f4f6f8;
  }
}