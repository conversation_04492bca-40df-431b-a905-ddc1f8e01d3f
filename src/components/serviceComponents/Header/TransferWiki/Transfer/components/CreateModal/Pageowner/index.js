/* eslint-disable react/jsx-max-props-per-line */
/* eslint-disable operator-linebreak */
import styles from './style.module.less';
import classBind from 'classnames/bind';
import { Radio } from 'antd';
import { useEffect, useState } from 'react';
import { getUserNameFromCookie } from '@/utils/cooperutils';

const cx = classBind.bind(styles);

const Pageowner = ({ check, data: { pageValue, userlap}, setData }) => {
  const onChange = (e) => {
    const { value } = e.target;
    const isCurrentUser = value === '当前用户（我）';
    setData((v) => ({ 
      ...v, 
      pageValue: isCurrentUser,
      userlap: isCurrentUser ? getUserNameFromCookie() : ''
    }));
  }

  const handChange = (e) => {
    const { value } = e.target;
    setData((v) => ({ ...v, userlap: value }));
    const isValid = value.trim() !== '';
    check(isValid);
  }

  useEffect(() => {
    check(pageValue || userlap?.trim() !== '');
   }, [pageValue]);

  return (
    <div className={cx('container')}>
      <div className={cx('label')}>
        指定页面所有者
      </div>
      <div className={cx('radio')}>
        <Radio.Group
          defaultValue={pageValue ? '当前用户（我）' : '指定页面所有者'}
          onChange={onChange}
        >
          <Radio
            value='当前用户（我）'
            style={{ position: 'relative' }}
          >
            当前用户（我）
            <div className={cx('tri')} />
          </Radio>
          <Radio
            value='指定页面所有者'
            style={{ position: 'relative' }}
          >
            指定页面所有者
            {/* {!member && <div className={cx('tri')} />} */}
          </Radio>
        </Radio.Group>
      </div>
      <div className={`${cx('desc')} custom-rich-text`}>
          <ul className='text-sm text-color-para'>
            <li>
              新建知识库：迁移者默认将成为空间所有者
            </li>
            <li>
              新建知识库：设置页面指定所有者后将页面所有者加入知识库中成为管理员
            </li>
            <li>
              迁移到已有知识库：单节点迁移，迁入已有知识库，迁移人员将不加入已有知识库
            </li>
          </ul>
      </div>
      {!pageValue && <div className={cx('urlIpt')}>
        <span className={cx('label')}>选择页面所有者</span>
        <div className={cx('ipt')}>
          <input
            autoFocus
            placeholder='请输入用户邮箱前缀'
            defaultValue={userlap}
            onChange={handChange}
          />
        </div>
      </div>}
    </div>
  )
}

export default Pageowner;
