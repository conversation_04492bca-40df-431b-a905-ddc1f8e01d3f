/* eslint-disable react/jsx-max-props-per-line */
/* eslint-disable operator-linebreak */
import styles from './style.module.less';
import classBind from 'classnames/bind';
import { Radio } from 'antd';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

const cx = classBind.bind(styles);

const MemberStep = ({ check, data: { member }, setData }) => {
  const { cooperLinkConf } = useSelector((state) => state.GlobalData);

  const onChange = (e) => {
    const { value } = e.target;
    setData((v) => ({ ...v, member: value === '迁移' }));
  }

  useEffect(() => {
    check(true);
  }, []);

  return (
    <div className={cx('container')}>
      <div className={cx('label')}>
        是否迁移Wiki空间的成员及权限？
      </div>
      <div className={cx('radio')}>
        <Radio.Group
          defaultValue={member ? '迁移' : '不迁移'}
          onChange={onChange}
        >
          <Radio
            value='迁移'
            style={{ position: 'relative' }}
          >
            迁移
            {member && <div className={cx('tri')} />}
          </Radio>
          <Radio
            value='不迁移'
            style={{ position: 'relative' }}
          >
            不迁移
            {!member && <div className={cx('tri')} />}
          </Radio>
        </Radio.Group>
      </div>
      <div className={`${cx('desc')} custom-rich-text`}>
        <p className='text-sm mb-4'><strong>成员及权限迁移说明：</strong></p>
        {
          member &&
            <ul className='text-sm text-color-para'>
              <li>
                迁移者（您）为知识库创建者，原Wiki空间的在职用户会同步迁移，用户组迁移到知识库为「自定义成员组」。
              </li>
              <li>
                迁移者（您）为知识库所有者，具有最高权限；原Wiki空间权限迁移逻辑
                <a href={cooperLinkConf.wiki_task_perm_illustration} target="_blank" className='underline'>详见说明</a>，
                其中Wiki页面单独设置权限不会迁移，迁移后的知识库页面权限默认继承知识库权限。
              </li>
              <li>
                系统将您全员类型成员组移除，其中包含：confluence-administrators，confluence-users，confluence-waibao
              </li>
            </ul>
        }
        {
          !member &&
            <p className='text-sm text-color-para'>迁移者（您）为知识库所有者，原Wiki空间成员无权限访问迁移后的知识库，需您在知识库手动添加成员后，原空间成员才能访问。</p>
        }
      </div>
    </div>
  )
}

export default MemberStep;
