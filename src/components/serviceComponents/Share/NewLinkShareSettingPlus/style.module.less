.share-content {
  margin-top: 32px;
  width: 100%;
  // border-radius: 4px;
  // background: #f7f8f9;
  // padding: 14px 16px;
  display: flex;
  flex-direction: column;
  gap: 14px;
  .share-link-expiration-en-tip {
    font-size: 14px;
    color: #909499;
    margin-left: 110px;
    margin-top: -4px;
  }
  .share-link-setting {
    :global {
      .ant-checkbox-inner {
        border-radius: 3px;
      }
      .ant-select .ant-select-selector {
        &:hover {
          border: 1px solid #047ffe !important;
          opacity: 0.8 !important;
        }
      }
      .ant-checkbox-disabled + span {
        color: rgba(144, 148, 153, 1);
      }
      .ant-checkbox-disabled .ant-checkbox-inner {
        background-color: rgba(189, 192, 195, 1);
        border-color: rgba(189, 192, 195, 1) !important;
      }
      .ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner::after {
        border-color: #fff;
      }
    }
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #909499;
    flex-wrap: wrap;
    > span:first-child {
      display: inline-block;
      width: 86px;
      margin-right: 24px;
      color: #656a72;
    }
    .password-box {
      font-size: 14px;
      color: #4e555d;
      margin-right: 10px;
    }
    .setting-password-reset {
      font-size: 14px;
      color: #047ffe;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }
    span {
      // height: 18px;
    }
    .share-expiration-date {
      margin-right: 4px;
      color: #4e555d;
    }
    .expiration-date-disable {
      color: #909499;
    }
    .setting-password {
      display: flex;
      height: 24px;
      .setting-password-date {
        border-radius: 4px 0px 0px 4px;
        background: @blueGray-13;
        border: 1px solid @blueGray-10;
        border-right: none;
        padding: 0px 10px;
        display: inline-flex;
        align-items: center;
      }
      .setting-password-reset,
      .setting-password-copy {
        border-radius: 0px 4px 4px 0px;
        background: #ffffff;
        border: 1px solid @blueGray-10;
        margin-right: 10px;
        padding: 0px 10px;
        cursor: pointer;
        color: #383f49;
        height: 24px;
        display: inline-flex;
        align-items: center;
        &:hover {
          background-color: @blueGray-12;
        }
        i {
          font-size: 12px;
          margin-right: 2px;
        }
      }
      .setting-password-reset {
        border-radius: 4px;
      }
      .reset-disabled {
        cursor: no-drop;
        pointer-events: none;
      }
    }
    .link-setting-password {
      // height: 24px;
      // padding-top: 3px;
    }
    .link-setting-label {
      flex-shrink: 0;
    }
    .share-link-setting-disabled {
      cursor: no-drop;
      .setting-password-reset {
        pointer-events: none;
      }
      :global {
        .dropdown-checkbox,
        .dropdown-radio {
          pointer-events: none;
        }
        .dropdown-checkbox__value,
        .dropdown-radio__value {
          i,
          span {
            color: #909499;
          }
        }
      }
    }
  }
  :global {
    .ant-select-focused:not(.ant-select-disabled) .ant-select-selector {
      border: 1px solid #d3d4d7 !important;
    }
    .ant-checkbox-input:focus + .ant-checkbox-inner {
      border-color: rgba(189, 192, 195, 1) !important;
    }
    .ant-select-dropdown {
      width: auto;
    }
    .ant-checkbox-wrapper {
      color: #4e555d;
    }
    .dropdown-checkbox {
      width: auto;
      min-width: auto;
      cursor: pointer;

      .dropdown-checkbox__value {
        border: none;
        background: transparent;
        padding: 0 4px;
        border-radius: 2px;
        span {
          font-size: 12px;
          height: 100%;
          color: @blueGray-3;
          align-items: normal;
        }
        .dk-icon-customize {
          font-size: 8px !important;
          color: @blueGray-3;
        }
      }
      .dropdown-checkbox__value:hover {
        background: #e8e9ea;
      }
    }

    .dropdown-radio {
      width: auto;
      min-width: auto;
      border: none;
      cursor: pointer;
      margin-right: 10px;
      height: 18px;
      display: flex;
      .dropdown-radio__value {
        padding: 0 4px;
        border-radius: 2px;
        span {
          font-size: 14px;
          height: 100%;
          color: @blueGray-3;
        }
        .dk-icon-customize {
          font-size: 8px !important;
          color: @blueGray-3;
        }
      }
      .dropdown-radio__shareitem {
        width: auto;
        padding-right: 36px;
        color: #222a35;
      }
      .dropdown-radio__disableitem {
        cursor: not-allowed;
        color: #bec5d2;
      }
    }

    .dropdown-radio:hover {
      background: #f2f3f3;
    }

    .dropdown-radio.active {
      box-shadow: none;
    }
    .dropdown-radio__wrapper {
      width: auto;
      min-width: 160px;
      left: 0;
      right: auto;
    }
  }
}

:global {
  .share-link-expiration-drop {
    width: auto !important;
  }
}
