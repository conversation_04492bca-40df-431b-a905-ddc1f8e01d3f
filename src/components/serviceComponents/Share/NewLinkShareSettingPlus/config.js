import { intl } from "di18n-react";

export const accessingLinkOption = () => [
  {
    label: intl.t("将自动成为文档协作者"),
    id: 0,
    value: 0,
  },
  {
    label: intl.t("将不会成为文档协作者"),
    id: 1,
    value: 1,
  },
];

export const permissionOptions = () => [
  {
    label: intl.t("查看/评论"),
    id: 0,
    value: 1,
    perm: 1,
    desc: intl.t("可查看/评论"),
    disabled: true,
  },
  {
    label: intl.t("编辑/下载"),
    id: 1,
    value: 34,
    perm: 34,
    desc: intl.t("可编辑/下载"),
  },
];

export const permissionOptionsDoc = () => [
  {
    label: intl.t("查看/评论"),
    id: 0,
    value: 1,
    perm: 1,
    desc: intl.t("可查看/评论，不能编辑"),
    disabled: true,
  },

  {
    label: intl.t("编辑"),
    id: 1,
    value: 2,
    perm: 2,
    desc: intl.t("可查看/评论/编辑，不能分享给他人"),
  },
  {
    label: intl.t("下载"),
    id: 2,
    value: 32,
    perm: 32,
    desc: intl.t("可查看/下载，不能编辑"),
  },
];

export const passwordOptions = ()  => [
  {
    label: intl.t("开启"),
    id: 0,
    access_type: 'Secret'
  },
  {
    label: intl.t("关闭"),
    id: 1,
    access_type: 'Public'
  },
];
