import { intl } from "di18n-react";
import { getLocale } from "di18n-react";
export const CreateLinkSelectTypeData = (access_type = "Public", linkSelectType = 0, isOuter = false) => [
  {
    value: 0,
    label: intl.t("仅协作者可访问"),
    extra: intl.t(
      "非协作者查看链接需申请权限"
    ),
    icon: getLocale() === "en-US" ? "safer_en" : "safer",
    type: "0",
    check: linkSelectType === 0,
    disabled: false,
    pendant: true,
    iconStyle: {
      marginLeft: "20px"
    }
  },
  {
    value: 1,
    label: intl.t("需密码访问"),
    extra:intl.t("非协作者查看链接需输入密码"),
    icon: "secret",
    type: "1_0",
    check: linkSelectType === 1 && access_type === "Secret",
    disabled: false,
    iconStyle: {}
  },
  {
    value: 1,
    label: intl.t("所有人可访问"),
    extra: intl.t("企业内员工都可通过链接访问"),
    icon: "qiye",
    type: "1_1",
    check: linkSelectType === 1 && access_type === "Public",
    disabled: isOuter,
    iconStyle: {}
  },
];

export const PERIOD_MAP = {
  1: "permanently",
  2: "month",
  3: "week",
  4: "day"
}