.no-perm {
  .label {
    font-size: 12px;
    color: #909499;
    margin-bottom: 10px;
  }
  .link-selecttype-item {
    display: flex;
    align-items: center;
    padding: 16px 16px 20px 16px;
    gap: 16px;
    border-radius: 6px;
    background: #f4f6f8;
    margin-bottom: 24px;
    .context-box {
      display: flex;
      flex-direction: column;
      padding: 0px;
      gap: 2px;
      .title {
        font-size: 14px;
        font-weight: 500;
        color: #222a35;
      }
      .des {
        font-size: 12px;
        color: #909499;
      }
    }
  }
  .link-label {
    font-size: 14px;
    color: #656a72;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    > span {
      color: #ff563b;
    }
  }
  .link-label-secret {
    >a {
      display: flex;
      align-items: center;
      span {
        color: #047FFE;
        margin-left: 4px;
        margin-right: 2px;
        cursor: pointer;
        &:hover{
          opacity: 0.8;
        }
      }
    }
    
  }
  .url-box {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    .url {
      border-radius: 4px;
      background: #ffffff;
      box-sizing: border-box;
      border: 1px solid #d3d4d7;
      padding: 6px 12px;
      height: 32px;
      font-size: 14px;
      color: #656a72;
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 隐藏溢出的内容 */
      text-overflow: ellipsis; 
      flex: 1;
      input {
        width: 100%;
        /* 去除边框和背景 */
        border: none;
        background: none;
        
        /* 去除轮廓线（聚焦时的蓝色边框） */
        outline: none;
        
        /* 重置内边距和外边距 */
        padding: 0;
        margin: 0;
        
        /* 重置字体样式 */
        font-family: inherit;
        font-size: inherit;
        color: inherit;
        
        /* 去除默认的圆角（某些浏览器会添加） */
        border-radius: 0;
        
        /* 去除 iOS 上的圆角和内阴影 */
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        caret-color: transparent;
        cursor: default;
      }
    }
    .btn {
      border-radius: 4px;
      background: #047ffe;
      padding: 6px 20px;
      font-size: 14px;
      color: #FFFFFF;
      height: 32px;
      box-sizing: border-box;
      cursor: pointer;
      &:hover{
        opacity: 0.8;
      }
    }
  }
}


