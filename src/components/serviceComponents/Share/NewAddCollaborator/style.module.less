.add-collaborator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: @blueGray-1;

  &-title {
    display: inline-flex;
    align-items: center;
    position: relative;
  }
}

.icon-tip {
  margin-left: 2px;
  color: @blueGray-7;
  padding: 3px;
  &:hover{
    padding: 3px;
    border-radius: 4px;
    background: #e8e9ea;
  }
}

.add-search {
  display: flex;
  margin-top: 7px;

  .search-input {
    width: 100%;
    height: 36px;
    line-height: 36px;
    border-radius: 4px;
    border: 1px solid #D3D4D7;
    // background: url('../search.png') 12px center no-repeat;
    // background-size: 18px 18px;
    // padding-left: 32px;
    font-size: 14px;
    &:focus {
      border: 1px solid @primary-color;
      box-shadow: none;
    }
    &:hover {
      border: 1px solid @primary-color;
      box-shadow: none;
    }
  }
}

.auth-control {
  color: @primary-color;
  cursor: pointer;
  padding: 3px 4px;
  font-weight: 400;
  display: flex;
  align-items: center;
}

.auth-control:hover {
  border-radius: 4px;
  background: rgba(4, 127, 254, 0.1);
}

.popover-container {
  padding: 10px;
  color: @blueGray-1;
  width: 216px;

  a {
    color: @primary-color;
    cursor: pointer;
    font-weight: 500;
    &:hover{
      color: @primary-color;
      text-decoration: underline;
    }
  }
}

.share-disabled {
  .auth-control {
    color: rgba(4, 127, 254, 0.4);
    cursor: not-allowed;
  }
  
  .auth-control:hover {
    border-radius: 4px;
    background-color: transparent;
  }
}
  
.batch-add-auth-control{
  display: flex;
  .batch-add {
    padding: 4px;
    border: none;
    height: 32px !important;
    color: @primary-color;
    margin-right: 24px;
    display: flex;
    align-items: center;
    // padding: 0 12px;
    &:hover {
      color: @primary-color;
      border-radius: 4px;
      background: rgba(4, 127, 254, 0.1);
    }
    &::after{
      content: '';
      position: absolute;
      right: -12px;          // 贴紧父元素右侧
      top: 50%;          // 垂直居中
      transform: translateY(-50%); // 精确垂直居中
      width: 1px;
      height: 14px;
      background-color: @blueGray-10; 
    }
    i{
      margin-right: 4px !important;
    }
  }
  // .batch-add:hover {
  //   border-color: @primary-color !important;
  //   color: @primary-color !important;
  //   // padding: 0 12px;
  
  //   i {
  //     color: @primary-color;
  //   }

  //   :global {
  //     .ant-btn[disabled] {
  //       i {
  //         color: #6A707C;
  //       }
  //     }
  //   }
  // }
  .batch-add-disabled{
    position: relative;
    border-radius: 4px;
    button{
      padding: 0;
      background: transparent;
      border: none;
      height: 100%;
      color: rgba(4, 127, 254, 0.4);
      cursor: not-allowed;
    }
    &:hover{
      background-color: #fff;
    }
  }
}