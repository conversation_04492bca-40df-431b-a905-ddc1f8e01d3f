.link-share {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: @blueGray-1;
  margin-top: 28px;

  &-title {
    display: inline-flex;
    align-items: center;
    position: relative;
  }
}

.icon-tip {
  margin-left: 2px;
  padding: 3px;
  color: @blueGray-7;
  &:hover{
    border-radius: 4px;
    background: #e8e9ea;
  }
}

.contact-owner {
  color: @primary-color;
  cursor: pointer;
  padding: 3px 4px;
  display: inline-flex;
  align-items: center;
  font-weight: 400;
}

.contact-owner:hover {
  color: @primary-color;
}

.contact-owner:hover {
  border-radius: 4px;
  background: rgba(4, 127, 254, 0.1);
}

.contact-owner-dc {
  width: 16px;
  margin-right: 4px;
}

.popover-container {
  padding: 10px;
  color: @blueGray-1;
  width: 155px;

  a {
    color: @primary-color;
    cursor: pointer;
    font-weight: 500;
    &:hover{
      color: @primary-color;
      text-decoration: underline;
    }
  }
}

.share-option {
  margin-top: 10px;
  display: flex;
  // border: 1px solid @blueGray-10;
  // height: 66px;
  align-items: center;
  justify-content: space-between;
  // border-radius: 4px;
  // position: relative;

  &-left {
    width: 44px;
    background: rgba(4, 127, 254, 0.1);
    height: 44px;
    text-align: center;
    position: absolute;
    border-radius: 50%;

    :global {
      i {
        position: absolute;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
        font-size: 20px;
      }
      .icon-no-share {
        color: #3BBF94;
      }
      .icon-intern-share {
        color: #047FFE;
      }
    }
  }

  .no-share {
    background-color: #EFFCF8;
  }

  .intern-share {
    background-color: rgba(4, 127, 254, 0.1);
  }

  &-right {
    margin-left: 50px;
    flex: 1;
  }
  .share-operate-copy{
    margin-left: 30px;
    border-radius: 4px;
    background: #FFFFFF;
    border: 1px solid @blueGray-9;
    padding: 8px 10px;
    font-size: 12px;
    cursor: pointer;
    color: @blueGray-1;
    display: flex;
    align-items: center;

    i {
      font-size: 14px;
      color: #6A707C;
      margin-right: 4px;
    }
    &:hover {
      background-color: #F2F3F3;
    }
  }

  .option {
    margin-left: 8px;
    :global {
      .ant-select-selector {
        border: none !important;
        border-radius: 4px !important;
        padding: 0 4px !important;
        height: 26px !important;
        align-items: center;
        .ant-select-selection-item{
          font-weight: 500;
        }
      }
      .ant-select-selector:hover {
        border: none !important;
        background-color: @blueGray-12 !important;
        align-items: center;
      }
      .ant-select-selector:focus {
        border: none !important;
        background-color: @blueGray-12 !important;
      }
      .ant-select-focused:not(.ant-select-disabled) .ant-select-selector {
        border: none !important;
      }
      .ant-select-single.ant-select-open .ant-select-selection-item {
        color: unset;
      }
      .ant-select-arrow {
        right: 4px !important;
        margin-top: -5px;
        .dk-icon-customize{
          color: #000;
          font-size: 10px;
        }
      }

      .ant-select-dropdown {
        border: 1px solid #E8E9EA;
        box-shadow: 0px 4px 16px 0px rgba(60, 68, 88, 0.16);
      }

      .ant-select-item {
        padding: 8px 10px !important;
        border-radius: 6px !important;
        &:hover{
          background: #F2F3F3;
        }
      }

      .ant-select-item-option-selected {
        background-color: #fff !important;
      }

      .ant-select-item-option-content {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        cursor: pointer;

        .dropdown-option {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
        }

        .dropdown-option-left {
          flex: 1;
          margin-right: 40px;
        }

        .dropdown-option-right {
          i {
            color: @primary-color;
          }
        }

        .dropdown-option-item {
          font-size: 14px !important;
          font-weight: 500 !important;
          color: #222A35 !important;
        }
        .dropdown-option-item-tip {
          font-size: 12px !important;
          color: #909499 !important;
          font-weight: normal;
        }
      }
      .ant-select-disabled{
        .ant-select-selector{
          color: #909499;
          &:hover{
            background-color: #fff !important;
          }
          .ant-select-selection-item{
            color: #909499;
          }
        }
        .ant-select-arrow{
          .dk-icon-customize{
            color: #909499;
          }
        }
      }
    }
  }

  .option-desc {
    font-size: 12px;
    color: @blueGray-6;
    margin-top: 2px;
    margin-left: 12px;
  }
}

.share-content {
  margin-top: 18px;
  background: #F7F8F9;
  border-radius: 4px;
  padding: 14px 16px;
  color: @blueGray-6;
  font-size: 12px;
  position: relative;

  &-line {
    margin-top: 8px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  &-line:first-child {
    margin-top: 0;
  }

  &-text {
    color: #4E555D;
    margin-right: 4px;
  }

  &-margin {
    margin-left: 36px;
  }

  &-divider {
    height: 1px;
    width: 100%;
    background-color: @blueGray-10;
    margin: 8px 0 10px;
  }

  &-operate {
    display: flex;
  }

  .share-operate-tip {
    padding: 0px 3px;
    border-radius: 4px;
    text-align: center;
    display: inline-block;
  }

  .share-operate-tip:hover {
    background-color: @blueGray-10;
  }

  .share-operate-setting{
    color: #047FFE;
    border-radius: 4px;
    padding: 3px 4px;
    font-size: 12px;
    height: 24px;
    line-height: 24px;
    display: inline-flex;
    align-items: center;
    cursor: pointer;

    i {
      margin-right: 2px;
      font-size: 14px;
    }
  }

  .share-operate-setting:hover,
  .share-operate-copy:hover {
    background: rgba(4, 127, 254, 0.1);
  }

  .share-operate-divider {
    height: 12px;
    width: 1px;
    background-color: @blueGray-10;
    margin: 0 10px;
    display: inline-block;
  }

  :global {
    .ant-tabs-tab {
      line-height: 32px;
    }
  }
}

.link-no-share{
  // padding-bottom: 12px;
}

.link-share-disabled {
  .share-operate-setting {
    color: rgba(4, 127, 254, 0.4);
    cursor: not-allowed;
  }
}



.link-selecttype-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0px;
  gap: 6px;
  width: 160px;
  padding-top: 24px;
  padding-bottom: 20px;
  border-radius: 4px;
  background: #FFFFFF;
  border: 1px solid #E8E9EA;
  position: relative;
  cursor: pointer;
  :global {
    .icon-style {
      font-size: 24px;
      color: #6A707C;
    }
  }
  &:hover {
    border: 1px solid rgba(4, 127, 254, 0.5);
  }
  .icon {
    // width: 24px;
    // height: 24px;
  }
  .title {
    font-size: 14px;
    font-weight: 500;
    color: #222A35;
    width: 98px;
    text-align: center;
  }
  .des {
    font-size: 12px;
    color: #909499;
    width: 110px;
    text-align: center;
  }
  .radio {
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 18px;
    color: #BBBBBB;
  }
}

.link-selecttype-item-checked {
  border: 1px solid #047FFE;
}

.link-selecttype-item-disabled {
  border: 1px solid #E8E9EA;
  background: #F6F7F7;
  cursor: not-allowed;
  &:hover {
    border: 1px solid #E8E9EA;
  }
}

// .share-link-box {
//   display: flex;
//   // justify-content: space-between;
//   // align-items: center;
//   // margin-top: 28px;
//   // gap: 8px;
//   .show {
//     padding: 6px 12px;
//     flex: 1;
//     white-space: nowrap; /* 防止文本换行 */
//       overflow: hidden; /* 隐藏溢出的内容 */
//       text-overflow: ellipsis; 
//     // border-radius: 4px;
//     // background: #FFFFFF;
//     // box-sizing: border-box;
//     // border: 1px solid #D3D4D7;
//     // font-size: 14px;
//     // color: #656A72;
//     // height: 32px;
//     // box-sizing: border-box;
//     // display: flex;
//     // align-items: center;
//     // gap: 12px;
//     // .url {
//     //   display: inline-block;
//     //   flex-grow: 1;
//     //   white-space: nowrap; 
//     //   overflow: hidden; 
//     //   text-overflow: ellipsis;
//     // }
//     // .bar {
//     //   border-left: 1px solid #E8E9EA;
//     //   width: 0px;
//     //   height: 16px;
//     // }
//     // .password {
//     //   display: inline-block;
//     //   width: 71px;
//     // }
//   }
//   .share-operate-copy{
//     // border-radius: 4px;
//     // background: #047FFE;
//     // font-size: 14px;
//     // color: #FFFFFF;
//     // cursor: pointer;
//     // display: flex;
//     // align-items: center;
//     // justify-content: center;
//     // height: 32px;
//     // &:hover {
//     //   opacity: 0.8;
//     // }
//   }
// }

.share-link-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 28px;
  margin-bottom: 12px;
  gap: 8px;
  .show {
    padding: 6px 12px;
    flex: 1;
    border-radius: 4px;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px solid #D3D4D7;
    font-size: 14px;
    color: #656A72;
    height: 32px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    gap: 12px;
    overflow: hidden;
    .url {
      display: inline-block;
      flex-grow: 1;
      white-space: nowrap; 
      overflow: hidden; 
      text-overflow: ellipsis;
      input {
        width: 100%;
        /* 去除边框和背景 */
        border: none;
        background: none;
        
        /* 去除轮廓线（聚焦时的蓝色边框） */
        outline: none;
        
        /* 重置内边距和外边距 */
        padding: 0;
        margin: 0;
        
        /* 重置字体样式 */
        font-family: inherit;
        font-size: inherit;
        color: inherit;
        
        /* 去除默认的圆角（某些浏览器会添加） */
        border-radius: 0;
        
        /* 去除 iOS 上的圆角和内阴影 */
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        caret-color: transparent;
        cursor: default;
      }
    }
    .bar {
      border-left: 1px solid #E8E9EA;
      width: 0px;
      height: 16px;
    }
    .password {
      display: inline-block;
      cursor: default;
    }
  }
  .share-operate-copy{
    border-radius: 4px;
    background: #047FFE;
    font-size: 14px;
    color: #FFFFFF;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    padding: 6px 20px;
    box-sizing: border-box;
    &:hover {
      opacity: 0.8;
    }
  }
}


.popover-container-new {
  padding: 10px;
  color: #222a35;
  width: 105px;

  a {
    color: #047ffe;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    &:hover {
      color: #047ffe;
      text-decoration: underline;
    }
  }
}

