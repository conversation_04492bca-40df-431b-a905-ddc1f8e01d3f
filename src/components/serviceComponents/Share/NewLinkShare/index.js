import { Popover, Input, Select, Tooltip } from 'antd';
import classNames from 'classnames/bind';
import { intl, getLocale } from 'di18n-react';
import { useEffect, useMemo, useState } from 'react';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { createShareLink, modifyShareLink } from '@/service/knowledge/share';
import { createShareLinkCooper, modifyShareLinkCooper } from '@/service/cooper/home';
import { ROLE_TYPE_DOC } from '../../MemberListOfDK/constants';
import NotificationStatus from '@/constants/notification';
import useNotification from '@/hooks/useNotification';
import usePermission from '@/hooks/usePermission';
import pathUtil from '@/utils/path';
import { getType, sendFileTypeEvent } from '@/utils/type'
import NewLinkShareSettingPlus from '../NewLinkShareSettingPlus';
import {CreateLinkSelectTypeData} from '../constants';

import styles from './style.module.less';

const cx = classNames.bind(styles);
const { Option } = Select;

const DisabledTip = (props) => {
  return (
    <Tooltip
      title={props.title}
      placement={props.placement || 'top'}
      {...props}
    >
      {props.children}
    </Tooltip>
  )
}

const LinkSelectTypeItem = ({data = {}, onSelect = () =>{}}) => {
  const content = <div className={`${cx('link-selecttype-item')} ${data.check ? cx('link-selecttype-item-checked') : ''} ${data.disabled ? cx('link-selecttype-item-disabled') : ''}`} onClick={() => !data.disabled && onSelect(data)}>
  <img src={require(`../${data.icon}.svg`)} style={data.iconStyle} />
  <div className={cx('title')}>{data.label}</div>
  <div className={cx('des')}>{data.extra}</div>
  {data.check ? <i className={`dk-iconfont dk-icon-danxuanyixuan ${cx('radio')}`} style={{color: '#047FFE'}} /> : <i className={`dk-iconfont dk-icon-danxuanweixuan ${cx('radio')}`} />}
</div>

  return  data.disabled ? <DisabledTip title={intl.t('外部文档不能设为所有人访问')}>{content}</DisabledTip> : content
}

const LinkShare = (props) => {
  const { resourceId, disabled, linkInfo, refreshLinkInfo, isDkPage, isInDK, info, cooperLinkConf, isModalShare } = props;
  const [ selectedValue, setSelectedValue ] = useState(0);
  const { accessRole, permission, linkSelectType, password, expiration, spaceType, access_type } = linkInfo;
  const notification = useNotification();
  const { checkOperationPermission } = usePermission();
  const isEN = getLocale() === "en-US";
  const adminPerm = useMemo(() => {
    if (isDkPage) {
      return checkOperationPermission('MANAGE_PAGE_MEMBER', linkInfo.currentUserPerm);
    } else {
      return [ROLE_TYPE_DOC.Owner, ROLE_TYPE_DOC.Admin].includes(linkInfo.currentUserRole);
    }
  }, [linkInfo.currentUserPerm]);

  useEffect(() => {
    setSelectedValue(linkSelectType);
  }, [linkSelectType]);

  const generatePwd = () => {	
    return Math.floor(100000 + Math.random() * 900000);	
  };

  const getOpenDefaultParam = (type = '') => {
    if (spaceType === 'IN_OUT' || type === '1_0') {
      return {
        'resource_id': resourceId,
        'permission': permission || (isDkPage ? 35 : 3),  //权限类型：查看/评论，编辑/下载
        'expiration': expiration || 3,  // 1: 永久优先； 2: 1个月； 3: 1周； 4: 1天
        'accessRole': accessRole || 0,  // 0: 成为文档协作者; 1: 不成为文档协作者
        'access_type': 'Secret',
        password: generatePwd(),
        linkSelectType: 1
      }
    }

    return {
      'resource_id': resourceId,
      'permission': permission || (isDkPage ? 35 : 3),  //权限类型：查看/评论，编辑/下载
      'expiration': expiration || 3,  // 1: 永久优先； 2: 1个月； 3: 1周； 4: 1天
      'accessRole': accessRole || 0,  // 0: 成为文档协作者; 1: 不成为文档协作者
      'access_type': 'Public',
      linkSelectType: 1
    }
  }

  const getCloseDefaultParam = () => {
    return {
      'resource_id': resourceId,
      'linkSelectType': 0,
    }
  }

  const linkShareContent = () => {
    return (
      <div className={cx('popover-container-new')} style={{width: isEN ? 155 : 105}}>
        <a href={isDkPage ? cooperLinkConf.link_share_dk_new : cooperLinkConf.link_share_doc_new} target='_blank'>
          {intl.t('查看详细说明')}
        </a>
      </div>
    )
  }
  const handleChange = async({value, type}) => {
    // 开启链接分享
    if (value === 1 && !linkInfo?.invite_link_id) {
      (isDkPage ? createShareLink : createShareLinkCooper)({...getOpenDefaultParam(type)})
      .then(() => {
        notification(NotificationStatus.SUCCESS, intl.t('修改成功'));
        refreshLinkInfo();
      }).catch(() => {
        notification(NotificationStatus.ERROR, intl.t('修改失败'));
      })
    } else {
      // 修改链接分享
      const defaultParams = value === 0 ? getCloseDefaultParam() : getOpenDefaultParam(type);
      (isDkPage ? modifyShareLink : modifyShareLinkCooper)({
        ...defaultParams,
        'invite_link_id': linkInfo.invite_link_id
      }).then(() => {
        notification(NotificationStatus.SUCCESS, intl.t('修改成功'));
        refreshLinkInfo();
      }).catch(() => {
        notification(NotificationStatus.ERROR, intl.t('修改失败'));
      });
    }

    
    const params = {};
    const isType = [
      'docs',
      'sheets',
      'slides',
      'flowchart',
      'xmind',
      'pages',
    ].includes(getType(info?.fileType));
    params.source = isType ? getType(info.fileType) : '';
    switch(type) {
      case "0":
        window.__OmegaEvent('ep_share_scope_collaborator_ck', '', { ...params });
        break;
      case "1_0":
        window.__OmegaEvent('ep_share_scope_password_ck', '', { ...params });
        break;
      case "1_1":
        window.__OmegaEvent('ep_share_scope_all_ck', '', { ...params });
        break;
    }
  };

  const getCopyText = (isUrlOnly = false) => {
    let copyPath = '';
    let url = location.origin + location.pathname;
    let name = document.title;

    // isInDK 在知识库页面中点击的分享，使用document.title即可，否则需要从接口信息中获取titile
    if (!isInDK) {
      url = pathUtil.getDkPageUrl(info.space_id || info.spaceId || info.sourceId , resourceId);
      name = info.fileName || info.objectName;
    }

    if (isDkPage) {
      url = pathUtil.getDkPageUrl(info.space_id || info.spaceId || info.sourceId , resourceId);
    }

    // 有密码，使用文件名称、链接、密码的形式
    // 无密码，使用链接#标题的形式
    if(isUrlOnly) {
      if (isDkPage) {
        copyPath = `${url}`;
      } else {
        copyPath = `${location.origin}${pathUtil.getCoopPath(resourceId, info.mime_type || info.mimeType)}`;
      }
    }else {
      if (password && adminPerm) {
        if (isDkPage) {
          copyPath = `${intl.t('文件名称')}：${name}\n${intl.t('链接')}：${url}\n${intl.t('密码')}：` + password;
        } else {
          copyPath = `${intl.t('文件名称')}：${name}\n${intl.t('链接')}：${location.origin}${pathUtil.getCoopPath(resourceId, info.mime_type || info.mimeType)}\n${intl.t('密码')}：` + password;
        }
      } else {
        if (isDkPage) {
          copyPath = `${intl.t('文件名称')}：${name}\n${intl.t('链接')}：${url}`;
        } else {
          copyPath = `${intl.t('文件名称')}：${name}\n${intl.t('链接')}：${location.origin}${pathUtil.getCoopPath(resourceId, info.mime_type || info.mimeType)}`;
        }
      }
    }
    

    return copyPath;
  }

  const LinkSelectTypeData = useMemo(
    () => CreateLinkSelectTypeData(access_type, linkSelectType, spaceType === 'IN_OUT'),
    [access_type, linkSelectType, spaceType]
  );

  return (
    <div className={cx({ 'link-share-disabled': disabled , 'link-no-share': selectedValue === 0})}>
      <div className={cx('link-share')}>
        <div className={cx('link-share-title')}>
          <span>{intl.t('链接分享设置')}</span>
          <Popover
            trigger='hover'
            placement='top'
            content={linkShareContent}
            zIndex={1001}
            getPopupContainer={(e) => e.parentNode}
            overlayClassName={isModalShare ? 'link-share-tip-placement-top_modal__reset_new' : 'link-share-tip-placement-top__reset'}
          >
            <i className={cx('dk-iconfont', 'dk-icon-tishi-xianxing', 'icon-tip')} />
          </Popover>
        </div>
      </div>
      <div className={cx('share-option')}>            
        {LinkSelectTypeData.map(i => <LinkSelectTypeItem data={i} onSelect={handleChange} />)}
      </div>
      { selectedValue === 1 && ( <NewLinkShareSettingPlus disabled={disabled} isDkPage={isDkPage} linkInfo={linkInfo} refreshLinkInfo={refreshLinkInfo} info={info} adminPerm={adminPerm} /> ) }
      <div className={cx('share-link-box')}>
          <div className={cx('show')}>
            <span className={cx('url')}><input type='text' value={getCopyText(true)} /></span>
            {password && 
              <>
              <div className={cx('bar')}></div>
              <span className={cx('password')}>{password}</span>
              </>
            }
            </div>
          <CopyToClipboard
            text={getCopyText()}
            onCopy={() => {
              if (password && adminPerm) {
                notification(NotificationStatus.SUCCESS, intl.t('已复制链接和密码'));
              } else {
                notification(NotificationStatus.SUCCESS, intl.t('已复制链接'));
              }
              sendFileTypeEvent('ep_share_link_copylink_ck',info?.fileType);
            }}
          >
            <div className={cx('share-operate-copy')}>
              {
                password ? (
                  adminPerm
                    ? intl.t('复制链接和密码') : intl.t('复制链接')
                ) : intl.t('复制链接')
              }
            </div>
          </CopyToClipboard>
      </div>
   </div>
  )
}

export default LinkShare;
