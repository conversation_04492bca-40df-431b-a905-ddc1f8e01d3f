
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Dir } from '@/assets/icon';
import { Tooltip } from 'antd';

export const image = {
  arrow: 'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNTk1NDkyNTgyODQyIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjQzMzUiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PGRlZnM+PHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPjwvZGVmcz48cGF0aCBkPSJNMjczLjA2NjY2NyAxNDAuOGMtMTIuOCAxNy4wNjY2NjctMTIuOCA0Ni45MzMzMzMgOC41MzMzMzMgNjRsMzY2LjkzMzMzMyAzMDcuMi0zNjYuOTMzMzMzIDMwNy4yYy0xNy4wNjY2NjcgMTIuOC0yMS4zMzMzMzMgMzguNC04LjUzMzMzMyA1NS40NjY2NjdsNC4yNjY2NjYgNC4yNjY2NjZjMTcuMDY2NjY3IDE3LjA2NjY2NyA0Mi42NjY2NjcgMjEuMzMzMzMzIDY0IDQuMjY2NjY3bDQwOS42LTM0MS4zMzMzMzNjMTcuMDY2NjY3LTE3LjA2NjY2NyAxMi44LTQ2LjkzMzMzMy00LjI2NjY2Ni02NGwtNDA5LjYtMzQxLjMzMzMzNGMtMjEuMzMzMzMzLTE3LjA2NjY2Ny00Ni45MzMzMzMtMTIuOC02NCA0LjI2NjY2N3oiIGZpbGw9IiM5NEEwQjAiIHAtaWQ9IjQzMzYiPjwvcGF0aD48L3N2Zz4=',
  personal: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAeCAYAAADdGWXmAAACr0lEQVRIS+2VT0iTcRjHv8/v3Vw5KY0RKpiv29pmUhBineraJYigWVGHCIL0WIcUysgC7VBHC4LoUBQuCOrUtU4lEhTmu7V3vkvQCMkEZ+n2/p54I8OtvW5Ne0+95+f5fn7f599LcPAjB1n4D1uXapddxvb2dvfM3HwnMTrB1ADiaSYM+zbXDI+OjmbLeU1ZsKamcKPLLa5LyGMAFAZMAhQApoB4lMvKC5OT8alSwJIwy9GX2YW7EvIkAR+ZKAaJaQg0EHOUgW0C4v6WuurTpRyWhDUHwycgcY+AKSY67qv1jliiP8v6NdNBzA8ZaITAqXQy/mA1dyVhqj/8lIGDILrpq/X2rnz9L+AAmM8R4Zmhxw+tERZ5LcG7iaknPaHdKBRrbomcZ+JBAXpjpLQ9a4Q56MzRnjk6jVYPHNuz5Yb/viASRwGq/xcXRDSFw/WuJfhMQW67SVMkZ3NVmJmMxz8BkHZxtnumhnZG2Mz2gdEB8AYQhO1YswWg7yCMkOLuNxLvtGKxf8CCwaDHRFVUSvMOAA+ABQAZEHKrwFwAvACqASwKoZxRsBRLJpOLK3MKYUINtEaZ5W0rkUDPAX5CitBNyRnbMgrysikDAB1m8AGAMkTUZejjsZVlzYMFAoGtJrtjDN4PIFaz0XV2bGxsFgCXuugAqK2trW7+W856aJRALxTKRnVd/7ycmwdTt+/YCylfMjDnUWRrIpGYKQOSFxIKhXyLptAI2AQh9hkf3r8qDvO3djHkEECP0ykt+reg5fhmfyQG8BGC6DZS47dsYJE+Bl8B4Vpaj1+qGBYIXwXjIoEuGymt3w7Ww+ABAvUaKW2wUpjqjxTVyetZS0t4l0noVhhDExPxt5XC7HQKR59UVfUYhmHtRzkTaHssiumU/FNX6q6sC7Ke4oVajjr7AUjbXS4M+zosAAAAAElFTkSuQmCC',
  team: 'data:image/png;base64,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',
  space: 'https://img-ys011.didistatic.com/static/cooper_yx/do1_qtNmas7nXzARicfxIVXE',
  COOPER_DOC: CooperDoc,
  DI_DOC: DiDoc,
  DIR: Dir,
  SHIMO2_WORD: 'https://img-ys011.didistatic.com/static/cooper_cn/smDocNew.png',
}

export const rmCommonPrefix = (arr1) => {
  let prefix = 0; let
    common = true
  let start = arr1[0].node
  for (let i = 0; i < start.length; i++) {
    for (let j = 1; j < arr1.length; j++) {
      if (arr1[j].node[i] !== start[i]) {
        common = false
        break
      }
    }
    if (!common) break
    prefix++
  }
  for (let i = 0; i < arr1.length; i++) {
    arr1[i].node = arr1[i].node.slice(prefix)
  }
  return arr1
}

export const groupingByPrefix = (arr) => {
  let list = []
  // 按照现有前缀分组
  for (let i = 0; i < arr.length; i++) {
    let current = arr[i]
    Array.isArray(list[current.node[0]]) ? list[current.node[0]].push(current) : list[current.node[0]] = [current]
  }
  let res = []
  // 处理每个分组
  for (let i = 0; i < list.length; i++) {
    let group = list[i]
    if (group) {
      if (group[0].node.length < 2 || group.length < 2) {
        let a = group.slice(1)
        res.push({
          ...group[0],
          children: a.length ? groupingByPrefix(a) : [],
        })
      } else {
        group.length && res.push(...groupingByPrefix(group.map((v) => {
          v.node = v.node.slice(1)
          return v
        })))
      }
    }
  }
  return res
}

export const getsubChecked = (root) => {
  let arr = []
  const fn = (node) => {
    arr.push(node.key)
    if (node.children) {
      node.children.map((v) => {
        fn(v)
      })
    }
  }
  fn(root)
  return arr
}

export const formateTree = (node, isAll = true) => {
  return node.map((v) => {
    return {
      ...v,
      isAll,
      key: v.resourceId,
      isLeaf: v.resourceType !== 'DIR',
      title: <Tooltip
        title={v.resourceName}
        placement='topLeft'><span className='file-node-item-title'>{v.resourceName}</span></Tooltip>,
      icon: <img
        src={image[v.resourceType]}
        height={18}
        style={{ display: 'block' }}/>,
      children: v.children ? formateTree(v.children) : [],
      type: v.type,
    }
  })
}

export const updateTreeData = (list, key, children) => {
  return list.map((node) => {
    if (node.key === key) {
      return {
        ...node,
        children,
      }
    }
    if (node.children) {
      return {
        ...node,
        children: updateTreeData(node.children, key, children),
      }
    }
    return node
  })
}

export const updateTreeNode = (list, key, cb) => {
  return list.map((node) => {
    if (node.key === key) {
      return cb(node)
    }
    if (node.children) {
      return {
        ...node,
        children: updateTreeNode(node.children, key, cb),
      }
    }
    return node
  })
}

export const getNodeById = (id, root) => {
  if (Array.isArray(root)) {
    for (let i = 0; i < root.length; i++) {
      let res = getNodeById(id, root[i])
      if (res) {
        return res
      }
    }
  } else {
    if (root.resourceId === id) {
      return root
    } if (root.children.length) {
      for (let i = 0; i < root.children.length; i++) {
        let res = getNodeById(id, root.children[i])
        if (res) {
          return res
        }
      }
    }
  }

  return null
}

export const deepClone = (node) => {
  return JSON.parse(JSON.stringify(node))
}

export const formateTreeToParams = (root, originRoot, spaceId) => {
  return root.map((v) => {
    let currentNode = originRoot.find((v1) => v1.pos === v.pos)
    currentNode = currentNode.node
    let children = v.children ? formateTreeToParams(v.children, originRoot, spaceId) : []
    return {
      resourceId: currentNode.resourceId,
      resourceName: currentNode.resourceName,
      resourceType: currentNode.resourceType,
      spaceId,
      children,
    }
  })
}

export const formateParamsTree = (checkList, spaceId) => {
  // 1. 排序
  let arr = checkList?.map((v) => v.pos)
  arr.sort((v1, v2) => v1.length - v2.length)
  // 2. 格式化数据
  let newArr = arr.map((v, i) => ({ pos: v, node: v.split('-') }))
  let groups = groupingByPrefix(deepClone(newArr))
  // 3. 组装成给后端的参数
  let params = formateTreeToParams(groups, checkList, spaceId)
  return params
}


