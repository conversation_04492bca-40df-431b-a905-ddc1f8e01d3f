.file-tree-wrap {
  :global {
    .ant-tree-iconEle {
      margin-top: 6px;
    }
    .ant-tree-switcher.ant-tree-switcher-noop::after {
      content: '';
    }
    .ant-tree-treenode {
      width: 100%;
      line-height: 40px;
      height: 40px;
      border-radius: 6px;
      transition: all 0.2s;
      &.ant-tree-treenode-selected {
        background: rgba(4, 127, 254, 0.09);
      }
      &:hover {
        background: #F7F7F7;
      }
      .ant-tree-checkbox {
        margin: 12px 4px 0 0;
      }
    }
    .ant-tree-switcher {
      line-height: 40px;
    }
    .ant-tree-treenode{
      .ant-tree-node-content-wrapper {
        flex: 1;
        display: flex;
        align-items: center;
        line-height: 40px;
        height: 40px;
        cursor: default;
        overflow: hidden;
        .ant-tree-title {
          flex: 1;
          position: relative;
          overflow: hidden;
        }
        .file-node-item-title {
          .ellipsis();
          width: 100%;
          display: block;
        }
        &:hover {
          background: none;
        }
        &.ant-tree-node-selected {
          background: none;
        }
        
      }
    }
   
    
  }
}