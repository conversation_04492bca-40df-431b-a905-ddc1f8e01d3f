.switch-search-input {
  background-color: #F4f4f4 !important;
  height: 36px !important;
  line-height: 36px !important;
  border-radius: 4px !important;
  color: #222A35 !important;
  padding-top: 4px !important;
  font-size: 14px !important;
  padding-left: 12px !important;
  border: none !important;
  &:focus {
    border: none;
    outline: none;
    box-shadow: none;
  }
}
.switch-search-icon {
  position: absolute;
  z-index: 20;
  top: 22px;
  left: 20px;
  color: #bbb;
}

.search-popover-content {
  padding: 4px 8px;
  width: 220px;
  ul {
    margin: 0 -8px;
    padding: 10px 8px;
    max-height: 180px;
    overflow: auto;
    li {
      padding: 8px 12px;
      line-height: 20px;
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: inherit;
      cursor: pointer;
      border-radius: 4px;

      &:hover {
        background: #F7F8F9;
      }
      &.active {
        background-color: rgba(11,131,255,.08);
        color: #0B83FF;
      }
      &.search-item {
        display: flex;
        cursor: pointer;
        span {
          flex: 1;
          padding-right: 10px;
          overflow: hidden;
          text-overflow:ellipsis;
          white-space: nowrap;
          color: rgba(34, 42, 53, 0.5);
        }
        span:first-child {
          flex: 1.6;
          font-weight: 500;
          color: #222A35;
        }
        span:last-child {
          padding-right: 0;
        }
      }
    }
  }
  .check-msg{
    color: #FE0B19;
    margin: 12px;
    font-size: 12px;
  }
}
