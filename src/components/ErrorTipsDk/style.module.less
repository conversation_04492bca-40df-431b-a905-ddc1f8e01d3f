.error-wrap {
  width: 396px;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  // 弥补图片上边的空白导致的不上下居中
  margin-top: -60px;
  
  .title {
    margin-top: 12px;
    height: 20px;
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    line-height: 20px;
  }
  .desc{
    user-select: text;
    margin-top: 3px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(34,42,53,.7);
    line-height: 20px;
  }
  .icon {
    width: 200px;
  }
}
.error-tip-inPhone{
  width: 80%;
}