.comment-content {
  padding: 0;
  width: 100%;
}
.cooper-component-comment {
  width: 100%;
  margin: 100px 0 70px 0;
  font-family: PingFangSC-Regular, PingFang SC;
  font-size: 14px;
  overflow: auto;
  .header-comment{
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-bottom: 17px;
    padding: 0 0 10px 0;
    border-bottom: 1px solid #F2F4F6;
    .header-left{
      font-size: 22px;
      font-weight: 500;
      color: #252525;
      line-height: 30px;
      font-family: PingFangSC-Medium, PingFang SC;
    }
    .add-btn{
      border: none;
      padding: 5px 8px;
      background-color: #F6F6F6;
      border-radius: 2px;
      color: #505050;
      line-height: 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      &:hover {
        background-color: #EEEEEF;
      }

      .add-btn-icon{
        width: 16px;
        height: 16px;
        margin: 0 7px;
      }
      .add-btn-text{
        flex: 1;
      }
    }
  }
  .comment-item-wrap-style{
    padding: 12px 12px 12px 0;
    margin-top: 4px;
    .editor-wrap{
      display: flex;
      .editor-avatar{
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-right: 12px;
        border: 1px solid #F4F4F4;
      }
      .right-active-editor{
        flex: 1;
        max-width: 100%;
        overflow: hidden;
      }
    }
  }

  .none-comment{
    display: flex;
    justify-items: center;
    margin-top: 24px;
    // align-items: center;
    .user-avatar{
      width: 32px;
      height: 32px;
      margin-right: 12px;
      border-radius: 50%;
      border: 1px solid #F4F4F4;
    }
    .none-tips{
      line-height: 20px;
      background: #F5F6F7;
      border-radius: 4px;
      padding: 10px 12px;
      color: #AAAAAA;
      margin: 0;
      &:hover{
        background: #EFF1F2;
      }
    }
    .right-active-editor{
      flex: 1;
      cursor: pointer;
      max-width: calc(100% - 45px);
    }
  }
  .hide {
    display: none;
  }
  .reply-comment{
    margin-left: 44px;
    margin-top: 4px;
    margin-bottom: 12px;
  }
  .reply-comment-sub{
    margin-left: 88px;
    margin-top: 4px;
    margin-bottom: 12px;
  }
  .animated{
    animation: fadeIn 3s infinite alternate;
    animation-iteration-count: 1;
  }
  @keyframes fadeIn {
    0% {background: #F0F4FA;}
    100% {background: #FFFFFF;}
  }
  #bottom-div-add-comment{
    margin-top: 90px;
  }

  .ProseMirror{
    position: relative;
    word-wrap: break-word;
    white-space: normal !important;
    font-feature-settings: none;
    font-variant-ligatures: none;
    min-height: 0 !important;
    padding: 0 !important;
    line-height: 1.7;
    outline: none;
    > p {
      padding-left: 0 !important;
      min-height: 30px !important;
      line-height: 22px !important;
      padding-top: 0 !important;
      margin: 0 !important;
    }
  }

}
.cooper-ant-tooltip__reset{
  max-width: 280px !important;
  padding-bottom: 1px !important;
  .ant-tooltip-arrow {
    display: none;
  }
  &.hide {
    display: none;
  }
  .ant-tooltip-inner {
    padding: 2px 8px;
    min-height: auto;
    background-color: #2A2D31;
    font-weight: 500;
    font-size: 12px;
  }
}
.page-detail-loading {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
}
.confirm-modal{
  margin: auto;

  .ant-modal-content {
    border-radius: 6px;
    width: 480px;
    .ant-modal-confirm-body{
      .ant-modal-confirm-title {
        height: 33px;
        line-height: 33px;
        font-size: 24px;
        font-weight: 500;
        font-family: PingFangSC-Medium, PingFang SC;;
        color: #252525;
      }
  
      .ant-modal-confirm-content {
        margin-left:0;
        font-size: 16px;
        font-weight: 400;
        color: #444B4F;
        line-height: 24px;
      }
    }

  

    .ant-modal-confirm-btns {
      margin-top: 36px;

      .ant-btn {
        width: 96px;
        height: 44px;
        border-radius: 4px;
        color: #444B4F;
        background: #F6F6F6;
        border: none;
        font-size: 16px;

        &.ant-btn-primary {
          color: #FFFFFF;
          background: #0B83FF;
          margin-left: 16px;
        }
      }
    }
  }
}


div.didoc-editor-pop-up-modal-mask {
  z-index: 1000;
}

div.didoc-editor-pop-up-element {
  z-index: 1000;
}


