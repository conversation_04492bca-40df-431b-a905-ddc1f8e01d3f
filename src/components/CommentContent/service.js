import { get, post, del } from '@/utils/request';
import api from '@/utils/request/api/DkApi';

export function doEditComment(params) {
  return post(api.EDIT_COMMENT, params);
}

export function doDeleteComment(commentId, url) {
  let reqUrl = url || api.DELETE_COMMENT;
  return del(reqUrl.replace(':commentId', commentId));
}

export function doDeleteShareComment(params, url) {
  let reqUrl = url || api.SHARE_DELETE_COMMENT;
  return post(reqUrl, params);
}

export function getCommentList(params, url) {
  let reqUrl = url || api.GET_COMMENT_LIST;
  return get(reqUrl, {
    params: {
      pageId: params.pageId,
    },
  });
}

export function getShareCommentList(params, url) {
  let reqUrl = url || api.SHARE_COMMENT;
  return get(reqUrl, {
    params,
  });
}
