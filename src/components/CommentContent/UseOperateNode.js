import { useEffect, useState, useMemo, useCallback, useRef } from 'react';

const formatTree = (data, formatNode, flattenData) => {
  return data.map((v) => {
    let node = v;
    if (!node.isFormated) {
      node = formatNode(v);
    }
    node.isFormated = true;
    flattenData[node.key] = node;
    node.children = v?.children?.length ? formatTree(v.children, formatNode, flattenData) : [];
    return node;
  });
};

export default function (formatNode = (v) => v) {
  const [update, setUpdate] = useState('init');
  const flattenData = useRef({});
  const treeData = useRef({ children: [] });
  const proxyData = useMemo(() => {
    return new Proxy(treeData.current.children, {});
  }, [update]);

  const setData = (data, root = '0') => {
    const rootData = formatTree(data, formatNode, flattenData.current);
    treeData.current = { key: root, children: rootData };
    flattenData.current[root] = treeData.current;
    setUpdate(`set${Math.random()}`);
  };

  const updateNode = (key, newState, clear = true) => {
    
    let currentNode = getNodeByKey(key);
    if (newState.children) { // 这里的更新是push的操作
      newState.children = clear
        ? formatTree(newState.children, formatNode, flattenData.current)
        : [...currentNode.children, ...formatTree(newState.children, formatNode, flattenData.current)];
    }
    let { parentId } = currentNode;
    let parent = getNodeByKey(parentId);

    let { children } = parent;
    const index = children.findIndex((v) => v.key === key);
    let node = {
      ...currentNode,
      ...newState,
    };
    flattenData.current[key] = children[index] = node;
    setUpdate(`update${Math.random()}`);
  };

  const addNode = (key, node, position) => {
    let currentNode = getNodeByKey(key);
    let { children } = currentNode;
    currentNode.isLeaf = false;

    let [child] = formatTree([node], formatNode, flattenData.current);

    position === undefined ? children.push(child) : children.splice(position, 0, child);
    setUpdate(`add${Math.random()}`);
  };

  const deleteNode = (key, cancelUpdate = false) => {
    let { parentId } = getNodeByKey(key);
    let parentNode = getNodeByKey(parentId);
    let { children } = parentNode;
    const index = children.findIndex((v) => v.key === key);
    flattenData.current[key] = undefined;
    children.splice(index, 1);
    if (!children.length) {
      parentNode.isLeaf = true;
    }
    if (!cancelUpdate) { // 移动的时候会先删除节点，但不触发更新
      setUpdate(`delete${Math.random()}`);
    }
  };
  const getNodeByKey = (key) => flattenData.current[key];

  const getChildKeyById = (node, arr) => {
    arr?.push(node.key);
    if (node.children?.length) {
      node.children.map((v) => getChildKeyById(v, arr));
    }
  };

  const getChildKeysById = (key) => {
    const node = getNodeByKey(key);
    let arr = [];
    getChildKeyById(node, arr);
    return arr;
  };

  return {
    treeData: proxyData,
    addNode,
    deleteNode,
    setData,
    updateNode,
    getNodeByKey,
    getChildKeysById,
  };
}
