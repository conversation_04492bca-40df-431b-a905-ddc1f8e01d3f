import { useMemo, useState } from 'react';
import cls from 'classnames';
import { intl } from 'di18n-react';
import { blurTime } from '@/utils/index';
import { Popconfirm, Tooltip } from 'antd';
import { previewImages } from '@didi/didoc-plugin-image'
import { previewFile } from '@didi/didoc-upload-file-node'
import { ReplyIcon, EditIcon, DeleteIcon } from '../iconConstant'
import './style.less';

export default ({
  commentItem,
  user,
  isSub,
  isShare,
  onReplyComment,
  onDeleteComment,
  onUpdateComment,
}) => {
  const { ldap } = user;
  const { commentator, createdOn, hasEdited, content, commentId, parentId, replyUser } = commentItem;
  const { avatar, name, ldap: commentatorLDap, orgMemberId } = commentator;
  const [deletePopoverVisible, setDeletePopoverVisible] = useState(false);

  const hasOperatorPerm = useMemo(() => {
    return ldap === commentatorLDap;
  })

  const onDeleteCancel = () => {
    setDeletePopoverVisible(false);
  }

  const showDeletePopconfirm = () => {
    window.__OmegaEvent && window.__OmegaEvent('ep_dkpc_essaycomment_deletecomment_ck', '', { type: isShare ? '分享态' : '预览态' });
    setDeletePopoverVisible(true);
  }

  const handlePreview = (e) => {
    if (e.target.nodeName === 'IMG') {
      const domlist = document.querySelectorAll(`#comment_${commentId} .item-comment img`)
      const images = Array.from(domlist).map((v) => v.src)
      const index = images.findIndex((v) => v === e.target.src)
      previewImages({
        images,
        index,
      })
    } else if (e.target.className.includes('icon-yulan') || e.target.className.includes('didoc_editor_file_link_container')) {
      let parent = e.target.parentNode;
      if (e.target.className === 'didoc_editor_file_link_container') {
        parent = e.target
      } else {
        while (!parent.getAttribute('data-filename') && parent) {
          parent = parent.parentNode
        }
      }

      const title = parent.getAttribute('data-filename')
      const href = parent.getAttribute('data-href')
      // 预览
      previewFile({
        title,
        href,
      })
    }
  }


  const commentHtml = (replyUserTemp) => {
    if (!replyUserTemp?.name) return { __html: content };

    let preHtml = (
      `<span class='reply-pre'>
      <span class='reply-pre-default'>${intl.t('回复')}</span>
      <span class='reply-pre-at'>@${replyUserTemp.name}</span>
      <span class='reply-pre-default'>:</span>
    </span>`);
    let editorEle = document.createElement('div');
    editorEle.innerHTML = content;

    let firstElement = editorEle.firstElementChild;
    firstElement.insertAdjacentHTML('afterbegin', preHtml);

    return { __html: editorEle.innerHTML };
  };


  return (
    <div
      className={cls('comment-item-content', {
        sub: isSub,
      })}
      key={commentId}>

      <img
        src={avatar}
        alt=""
        className='item-avatar' />
      <div className='item-main'>
        <div className='item-title'>
          <span className='item-name'>{name}</span>
          <span className='item-time'>{blurTime(createdOn)}</span>
          {hasEdited && <span className='item-edited item-time'>({intl.t('已更新')})</span>}
        </div>
        <div
          className='item-comment ProseMirror'
          dangerouslySetInnerHTML={commentHtml(replyUser)}
          onClick={handlePreview}/>
        <div className='item-operate'>
          <Tooltip
            title={intl.t('回复')}
            placement="top"
            overlayClassName="cooper-ant-tooltip__reset"
          >
            <div
              className='operate-icon-wrap'
              onClick={() => {
                window.__OmegaEvent && window.__OmegaEvent('ep_dkpc_essaycomment_replycomment_ck', '', { type: isShare ? intl.t('分享态') : intl.t('预览态') });
                onReplyComment(isSub, commentId, orgMemberId, parentId)
              }}
            >
              <img
                src={ReplyIcon}
                alt=""
                className='operate-icon' />
            </div>
          </Tooltip>
          {
            hasOperatorPerm && (
              <>
                <Tooltip
                  title={intl.t('编辑')}
                  placement="top"
                  overlayClassName="cooper-ant-tooltip__reset"
                >
                  <div
                    className='operate-icon-wrap'
                    onClick={() => {
                      window.__OmegaEvent && window.__OmegaEvent('ep_dkpc_essaycomment_editcomment_ck', '', { type: isShare ? intl.t('分享态') : intl.t('预览态') });
                      onUpdateComment(commentId, content)
                    }}>
                    <img
                      src={EditIcon}
                      alt=""
                      className='operate-icon' />
                  </div>
                </Tooltip>
                <Popconfirm
                  title={intl.t('确认删除此条{slot0}吗？', { slot0: isSub ? intl.t('回复') : intl.t('评论') })}
                  visible={deletePopoverVisible}
                  icon={<i />}
                  onConfirm={() => {
                    setDeletePopoverVisible(false);
                    onDeleteComment(commentId);
                  }}
                  overlayClassName={'confirm-wrap-del'}
                  onCancel={onDeleteCancel}
                  okText={intl.t('删除')}
                  cancelText={intl.t('取消')}
                >
                  <Tooltip
                    title={intl.t('删除')}
                    placement="top"
                    overlayClassName="cooper-ant-tooltip__reset"
                  >
                    <div
                      className='operate-icon-wrap'
                      onClick={showDeletePopconfirm}>
                      <img
                        src={DeleteIcon}
                        alt=""
                        className='operate-icon' />
                    </div>
                  </Tooltip>
                </Popconfirm>
              </>
            )
          }
        </div>

      </div>
    </div>
  );
};


