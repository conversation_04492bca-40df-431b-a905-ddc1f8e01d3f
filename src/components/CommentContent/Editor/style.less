#comment-editor-container {
  position: relative;
  transition: height 0.3s;
  z-index: 1;
  opacity: 1;
  max-height: 445px;
  overflow: hidden;

  &.comment-editor-hide {
    opacity: 0;
    z-index: -1;
  }
  border: 1px solid #D2D2D2;
  border-radius: 4px;
  .comment-editor-btns {
    min-width: 136px;
    padding-right: 16px;
    &>* {
      margin-left: 8px;
    }
    >:first-child {
      background: #F6F6F6;
    }
    >:last-child:disabled {
      background: rgba(4, 127, 254, 0.5);
      border: none;
      color: #fff;
    }
  }
  .editor-menu {
    padding: 0 12px;
  }
  .editor-menu-wrapper {
    flex: 1;
    min-width: 100px;
    display: block !important;
    .editor-menu {
      background: transparent;
      color: #333;
    }
  }
}
#component-comment-editor-container {
  box-sizing: content-box;
  min-height: 100px;
  max-height: 400px;
  overflow: auto;
  cursor: auto;
}
#component-comment-editor-container .ProseMirror {
  outline: none;
  padding: 16px 12px 0 12px !important;
  p {
    min-height: 22px;
    line-height: 22px;
    margin: 0;
  }
  .empty-placeholder {
    transform: translateY(-100%);
  }
}

#component-comment-editor-container .ProseMirror {
  ol, ul {
    padding-left: 19px;
  }
}
