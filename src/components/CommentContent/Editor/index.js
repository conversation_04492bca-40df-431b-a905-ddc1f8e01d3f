import { intl } from 'di18n-react';
import ReactDOM from 'react-dom';
import { useContext, useEffect, useRef, useState } from 'react';
import DidocEditor from '@didi/didoc-core';
import PlaceHolder from '@didi/didoc-plugin-placeholder';
import MentionPerson from '@didi/didoc-plugin-mention-person'; // at 用户
import Image from '@didi/didoc-plugin-image';
import Tabindent from '@didi/didoc-mark-tabindent';

import Strong from '@didi/didoc-mark-strong'; // 粗体
import Italics from '@didi/didoc-mark-italics'; // 斜体
import Listitem from '@didi/didoc-node-listitem'; // 列表每一项（？）
import Orderlist from '@didi/didoc-node-orderlist'; // 有序列表
import Bulletlist from '@didi/didoc-node-bulletlist'; // 无序列表
import Link from '@didi/didoc-mark-link'; // 链接
import StrikeThrough from '@didi/didoc-mark-strikethrough'; // 中划线（删除线）
import TextColor from '@didi/didoc-mark-text-color'; // 文字颜色
import TextHighlight from '@didi/didoc-mark-text-highlight'; // 背景高亮
import underline from '@didi/didoc-mark-underline'; // 下划线
import Checklist from '@didi/didoc-node-checklist'; // 任务列表
import ChecklistItem from '@didi/didoc-node-checklist-item'; // 任务列表的项
import UploadFile from '@didi/didoc-upload-file-node'; // 上传文件(包括图片)

import MemuBar from '@didi/didoc-editor-menubar';

import { post } from '@/utils/request';

import './style.less';
import { Button, message } from 'antd';
/**
  pageId: 文档id 必传
  content: 评论内容 html字符串，编辑评论时需要传，新建无需
  commentId： 评论id，编辑时传，新建时不用
  replayUserId： 回复的用户id,评论回复区里回复某人时传，一级评论或者回复一级评论无需传
  replayId： 回复的评论的评论id
  onSubmit: 当提交时触发的方法，如果传此参数，将不会执行默认方法
  submitUrl: 提交时候的地址 优先级是 onSubmit > submitUrl > defaultSubmit
  afterDefaultSubmit: 提交成功后触发
  containerSelector: 渲染的domid 或者class名称 如 ‘#comment-1' 或者 ‘.comment-1’
* */

const menubarContent = {
  undo: false,
  redo: false,
  formatBrush: false,
  split_line1: false,
  heading: false,
  fontfamily: false,
  fontsize: false,
  split_line2: false,
  image: false,
  tabindent: false,
  decreaseIndent: false,
  text_align: false,
  line_height: false,
  split_line6: false,
  table: false,
  flowchart: false,
  split_line7: false,
  horizontal_rule: false,
  code_block: false,
  blockquote: false,
  uploadFileNodes: {
    width: 28,
    text: intl.t('文件'),
    icon: 'icon-file',
    showTip: true,
    tooltip: intl.t('文件'),
    macToolTip: intl.t('文件'),
  },

  selectGroup: {
    width: 36,
    children: {},
  },
};

export default (props) => {
  const {
    isShare,
    shareType,
    shareId,
    pageId,
    foceUpdate,
    content,
    commentId,
    replayUserId = '0',
    replayId = '0',
    onCancel,
    onSubmit,
    afterDefaultSubmit = () => {},
    containerSelector,
    submitUrl,
    height = 54,
  } = props;
  const [hide, setHide] = useState(true);
  const [btnDisable, setBtnStatus] = useState(true);
  const [loading, setLoading] = useState(false);
  const [selectionStates, setSelectionStates] = useState();
  let domRef = useRef();
  let submitRef = useRef();
  let editorRef = useRef();

  const handleSubmit = async () => {
    try {
      if (editorRef.current?.state?.doc.textContent.length > 10000) {
        message.error(intl.t('评论字数过多'));
        return;
      }
      window.__OmegaEvent
        && window.__OmegaEvent('ep_dkpc_essaycomment_sendcomment_ck', '', {
          type: isShare ? intl.t('分享态') : intl.t('预览态'),
        });

      setLoading(true);
      const editorContent = editorRef.current.getHTML();

      const params = {
        commentId,
        pageId,
        commentParentId: replayId,
        responseUser: replayUserId,
        commentContent: editorContent,
      };

      let res;
      if (onSubmit) {
        res = await onSubmit(params);
        setLoading(false);
      } else {
        const reqParams = isShare ? { ...params, shareType, shareId } : params;
        const url = submitUrl
          || `/cooper_gateway/metis/v1${isShare ? '/share' : ''}/comment`;
        res = await post(url, reqParams, {
          headers: {
            'X-App-Id': 4,
          },
        });

        setLoading(false);
        setTimeout(() => {
          afterDefaultSubmit(res);
        }, 300);
      }
    } catch (e) {
      setLoading(false);
    }
  };

  useEffect(() => {
    domRef.current = document.querySelector('#comment-editor-container');
    new DidocEditor({
      applications: [
        PlaceHolder.configure({
          placeholder: intl.t('enter为换行，shift+enter为发送'),
        }),

        Strong, // 加粗
        Italics, // 倾斜
        Orderlist, // 有序列表
        Listitem,
        Bulletlist, // 无序列表
        Image.configure({
          stopAutoWidth: true,
          callback: (view) => {
            view.chain().newLine().focus().run();
          },
        }),

        Link.configure({
          stopScroll: false,
        }),

        MentionPerson.configure({
          resourceId: pageId,
          url: `${location.protocol}//${location.host}/cooper_gateway/metis/v1/search/at`,
        }),
        // @功能
        TextColor, // 颜色
        TextHighlight, // 背景高亮
        StrikeThrough, // 删除线
        Checklist, // 任务列表
        ChecklistItem,
        underline, // 下划线
        Tabindent,
        UploadFile,
      ],

      target: document.querySelector('#component-comment-editor-container'),
      app: {
        content: '',
      },

      offline: true,
      editable: true,
      supportPreview: [
        'doc',
        'docx',
        'xls',
        'xlsx',
        'ppt',
        'pptx',
        'pdf',
        'mp4',
        'mov',
        'mkv',
        'ogg',
        '3gp',
      ],
      onStateChange(val) {
        setSelectionStates(val);
      },
      eventHandler: {
        init(state, view) {
          editorRef.current = view;
        },
        onChange(state, view) {
          if (view.getHTML() === '<p></p>') {
            setBtnStatus(true);
          } else {
            setBtnStatus(false);
          }
        },
        onKeydown(state, view, event) {
          event.stopPropagation();
          const { key, shiftKey } = event;
          if (key === 'Enter' && shiftKey) {
            submitRef.current();
          }
        },
      },
    });

    return () => {};
  }, []);

  useEffect(() => {
    submitRef.current = handleSubmit;
  }, [replayId, replayUserId, commentId]);

  useEffect(() => {
    if (containerSelector) {
      const dom = document.querySelector(containerSelector);
      if (dom) {
        domRef.current && dom.appendChild(domRef.current);
        setHide(false);
        let currentContent = content;
        if (!commentId) {
          editorRef.current.commands().focus();
          currentContent = null;
        }
        setTimeout(() => {
          editorRef.current.template.import({
            content: currentContent,
            title: '',
          });
        }, 0);
      } else {
        setHide(true);
      }
    } else {
      setHide(true);
    }
  }, [containerSelector, foceUpdate]);
  useEffect(() => {
    if (hide) {
      document.querySelector('#comment-editor-container').style.height = `${0}px`;
    } else {
      document.querySelector('#comment-editor-container').style.height = `${Math.max(height, 90) + 45 + 12}px`;
      setTimeout(() => {
        document.querySelector('#comment-editor-container').style.height = 'auto';
      }, 300);
    }
  }, [hide]);

  const handleClick = () => {
    editorRef.current.focus();
  };

  return (
    <div
      id='comment-editor-container'
      className={hide ? 'comment-editor-hide' : ''}
    >
      <div
        id='component-comment-editor-container'
        onClick={handleClick} />

      <div style={{ display: 'flex', alignItems: 'center' }}>
        <MemuBar
          editorView={editorRef.current}
          options={menubarContent}
          selectionStates={selectionStates}
        />

        <div className='comment-editor-btns'>
          <Button
            onClick={() => {
              window.__OmegaEvent
                && window.__OmegaEvent(
                  'ep_dkpc_essaycomment_cancelcomment_ck',
                  '',
                  { type: isShare ? intl.t('分享态') : intl.t('预览态') },
                );
              onCancel();
            }}
          >
            {intl.t('取消')}
          </Button>

          <Button
            type='primary'
            onClick={handleSubmit}
            disabled={btnDisable}
            loading={loading}
          >
            {commentId ? intl.t('确认') : intl.t('发送')}
          </Button>
        </div>
      </div>
    </div>
  );
};
