
.white-screen-container{
  z-index: 100;
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  position: relative;

  .content{
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .img{
      width: 300px;
      height: 300px;
    }
  }

  .load-error-wrap{
    font-weight: bold;
    .solution-li{
      list-style-type: decimal;
      font-weight: 400;
    }
  }
  

  .link{
    color: @primary-color;
    margin-left: 4px;
    font-weight: bold;
    cursor: pointer;
    z-index: 2147483648 !important; // 大于iframe
  }
  .fresh-btn{
    background: none;
    border: none;
    color: #047FFE;
    font-weight: bold;
    line-height: 22px;
    margin-left: 2px;
    padding: 0 4px;
    cursor: pointer;
  }
  
  
 
}