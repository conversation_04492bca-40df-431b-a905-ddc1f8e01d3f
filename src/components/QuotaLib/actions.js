// import { SPACE_QUOTA_APPLY } from '../../api';
import api from '@/utils/request/api/CooperApi';
// import { post } from '../request/request';
import { post } from '@/utils/request/cooper';

export const COOPER_SPACE_QUOTA_APPLY = 'COOPER_SPACE_QUOTA_APPLY';
export const spaceQuotaApply = ({ teamId = 0, surplusQuota, reason }) => {
  return () => post(api.SPACE_QUOTA_APPLY.replace(':teamId', teamId), { surplusQuota, reason });
};
