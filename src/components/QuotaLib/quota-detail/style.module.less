.team-quota-container {
  padding-left: 8px;
  padding-right: 8px;
  border-left: 1px solid #E8E9EA;
  .quota-btn {
    display: none;
  }
}

.team-quota-container-hover {
  &:hover {
    .quota-detail {
      display: none;
    }
    .quota-btn {
      display: block;
      margin-top: -4px;
      width: 90px;
    }
  }
}

.quota-detail {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  text-indent: 18px;
  height: 14px;
  background: url('../assets/disk.svg') no-repeat left;
  background-size: 14px 14px;
 

  .quota-img {
    width: 16px;
    height: 16px;
  }
  .quota-word {
    font-size: 12px;
    color: #6A707C;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 160px;
  }
  .red-word {
    color: #FF563B;
  }
}
.quota-detail-tooltip-inner {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .tooltip-inner-front {
    font-size: 12;
    color: #fff;
  }
  .tooltip-inner-end {
    font-size: 12;
    color: #0066ff;
    cursor: pointer;
  }
}
.space-quota-modal {
  .team-quota-hint {
    font-size: 14px;
    color: #8a93a8;
    .team-quota-hint-num {
      font-size: 14px;
      color: #0066ff;
    }
  }
  .ant-confirm-btns button {
    padding: 0;
  }
  .icon-local-container {
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0 16px;
  }
}
