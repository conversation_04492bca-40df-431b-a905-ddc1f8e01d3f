/* eslint-disable max-len */
import { intl } from 'di18n-react';
import React from 'react';
import { connect } from 'react-redux';
import { Button, message, Progress } from 'antd';
// import SmallBtn from '../../small-btn';
// import QuotaDetail from '../quota-detail';
// import cooperConfirm from '@/components/common/CooperConfirm';
import { Subtr } from '@/utils';
import { spaceQuotaApply } from '@/service/cooper/teamSpace';
import cooperConfirm from '@/components/common/CooperConfirm';
import './style.less';
import { isDiDiTenant } from '@/utils/entryEnhance';

class TeamQuota extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      // ellipsisData: [],
    };
  }
  // qoutaSuccess = ({ approver = {}, bpmUrl }) => {
  //   confirm({
  //     type: 'success',
  //     title: intl.t(
  //       '您的申请已提交到BPM(倚天流程管理平台)中，将由 {one} 进行审批，详情请到BPM中查看',
  //       {
  //         one: approver.name || '',
  //       },
  //     ),
  //     okText: intl.t('去查看'),
  //     cancelText: intl.t('知道了'),
  //     onOk: () => {
  //       window.open(bpmUrl);
  //     },
  //   });
  // };

  requestQuota = (e) => {
    e.stopPropagation();
    e.preventDefault();
    const {
      teamId,
      success,
    } = this.props;

    cooperConfirm({
      className: 'space-quota-modal',
      type: 'warn',
      title: teamId === 0 ? intl.t('个人空间扩容') : intl.t('团队空间扩容'),
      content: (
        <div className={'team-quota-hint'}>
          {teamId === 0 ? (
            <p>
              {intl.t('个人空间每次可申请扩容')}
              <span className={'team-quota-hint-num'}>{'50G'}</span>
              {intl.t('，申请后需等待您的直属上级审核')}
            </p>
          ) : (
            <p>
              {intl.t('每个团队空间每次可申请扩容')}
              <span className={'team-quota-hint-num'}>{'50G'}</span>
              {intl.t('，申请后需等待您的直属上级审核')}
            </p>
          )}
        </div>
      ),
      okText: (
        <div className={'icon-local-container'}>
          <div
            id='icon-local-word'
            style={{
              display: 'block',
            }}
          >
            {intl.t('确认申请')}
          </div>
        </div>
      ),
      cancelText: intl.t('取消'),
      onOk: (cb) => {
        spaceQuotaApply({
          teamId,
          surplusQuota: 50000000000,
          reason: '',
        })
          .then((data) => {
            message.success(intl.t('扩容申请中，可前往{bpm}查看', {
              bpm: <a
                onClick={(e) => { e.stopPropagation(); this.junmpTo(data.bpmUrl) }}
                style={{ color: '#1A6EFF' }}>{intl.t('BPM')}</a>,
            }))
            this.setState({
              showBPMButton: true,
            })
            cb();
            success();
            // this.qoutaSuccess(data);
          })
          .catch(() => {
            cb();
          });
      },
    });
  };

  junmpTo = (url) => {
    if (url) window.open(url);
  };

  renderOperationTooltipInner = (bpmUrl) => (
    <div className={'quota-detail-tooltip-inner'}>
      <div className={'tooltip-inner-front'}>
        <Button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            this.junmpTo(bpmUrl)
          }}
          className='quota-btn'>{intl.t('扩容审核中，前往BPM查看')}</Button>
      </div>
      {/* <div
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          junmpTo(bpmUrl);
        }}
        className={cx('tooltip-inner-end')}>
        {'BPM'}
      </div> */}
    </div>
  );

  renderTooltipInner = () => (
    <div className={'quota-detail-tooltip-inner'}>
      <div className={'tooltip-inner-front'}>
        <Button
          className='quota-btn'
          onClick={() => { this.junmpTo(`dchat://im/start_conversation?name=${this.props.ownerLdap}`) }}>
          {intl.t('联系空间所有者扩容')}
        </Button>
      </div>
    </div>
  );

  renderHint = (isOwner, applied, bpmUrl) => {
    if (isOwner) {
      if (applied) {
        return this.renderOperationTooltipInner(bpmUrl);
      }
      return null;
    }
    return this.renderTooltipInner();
  };

  render() {
    const { useUp, total, isOwner, applied, bpmUrl, teamId, unit = 'G' } = this.props;
    const { showBPMButton } = this.state;
    const isTransboundary = total > 0 && Subtr(total, useUp) < 5;
    const initToolTip = !(!isTransboundary || (isTransboundary && isOwner && !applied));

    // const isTransboundary = true;
    return (
      <div className='team-quota'>
        <div className={'quota-detail'}>
          <span className={'team-quota-capacity'}>
            <div className={'dot'} />
            {`${intl.t('空间容量')}`}
          </span>
        </div>
        <div className='quota-tip'>
          {
            isDiDiTenant() ? intl.t('当空间剩余容量低于5G时，会自动开通空间容量申请渠道，空间所有者可以提交申请') : intl.t('当空间容量不够用时可联系租户管理员进行申请')
          }
        </div>
        <div
          className={'quota-word'}
          title={`${useUp}${unit}/${total}${unit}`}
        >
          <span className={`${isTransboundary ? 'red-word' : ''}`}>{useUp}</span>
          <span className={`${isTransboundary ? 'red-word' : ''}`}>{unit}</span>
          <span>/</span>
          <span>{total}</span>
          <span>{unit}</span>
          {/* <Tooltip title={intl.t('当空间剩余容量低于5G时，会自动开通空间容量申请渠道，空间所有者可以提交申请')}>
              <InfoCircleOutlined className="quota-word-icon" />
            </Tooltip> */}
        </div>
        <div className={'quota-progress'}>
          <Progress
            percent={(useUp * 100) / total}
            showInfo={false}
            strokeColor={isTransboundary ? '#FF563B' : 'rgba(26, 110, 255, 0.5)'}
            trailColor={isTransboundary ? 'rgba(255, 86, 59, 0.1)' : 'rgba(26, 110, 255, 0.1)'}
          />
        </div>
        {isOwner && !applied && isTransboundary && !showBPMButton && isDiDiTenant() && (
          <Button
            onClick={this.requestQuota}
            className={'quota-btn'}>{intl.t('申请空间容量')}</Button>
        ) }
        {(initToolTip || showBPMButton) && isDiDiTenant() && (this.renderHint(isOwner, showBPMButton || applied, bpmUrl))}
      </div>
    );
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    spaceQuotaApply: (quota) => dispatch(spaceQuotaApply(quota)),
  };
};

export default connect(null, mapDispatchToProps)(TeamQuota);
