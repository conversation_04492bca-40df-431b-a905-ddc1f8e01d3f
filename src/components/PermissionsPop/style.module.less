.permission-tip {
  text-align: center;
  color: @blueGray-6;
  font-size: 12px !important;
  display: inline-block;
 
}
.team-set {
  margin-left: 4px;
}
.permissions-tips-content {
  display: inline-block;
  height: 23px;
  line-height: 23px;
  padding: 0 4px;
  margin-left: 4px;
  font-size: 12px;
  color: @blueGray-6;
  align-items: center;
  justify-content: center;
}
.hasHover,.defaultHover{
  cursor: pointer;
  &:hover {
    color: @blueGray-1;
    background-color: #E8E9EA;
    border-radius: 4px;
    .permission-tip {
      color: @blueGray-1;
    }
  }
}
.defaultHover{
  cursor: default;
}

.pop {
  // width: 357px;
  padding: 12px;
  font-family: PingFang SC;

  .pop-desc-tip {
    font-size: 12px;
    color: #909499;
    margin-bottom: 10px;
  }


  .pop-title {
    font-size: 16px;
    color: @blueGray-1;
    font-weight: 500;
    line-height: 24px;
    margin-bottom: 6px;
  }

  .pop-content {
    width: 100%;
    border-radius: 4px;
    // border: 1px solid @blueGray-10;
    border: 1px solid rgba(34,42,53,.1);

    .pop-content-item-con{
      padding: 12px;
      height: 37px;
      line-height: 23px;
      color: rgba(34, 42, 53, .9);
    }

    .pop-content-item-title {
      background-color: #F7F9FA;
      color: @blueGray-1;
      font-weight: 500;
      font-size: 14px;
    }
   
    // .pop-content-item-key{
    //   width: 100px;
    // }
    .pop-content-item-value{
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
      text-align: left;
      font-size: 14px;
    }
    
  }

  .pop-desc {
    font-size: 12px;
    color: rgba(34, 42, 53, .5);
    height: 18px;
    line-height: 18px;
    margin-top: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
}

// .pop-us {
//   // width: 460px;

//   .pop-content {
//     .pop-content-item-key {
//       width: 140px;
//     }
//   }
// }