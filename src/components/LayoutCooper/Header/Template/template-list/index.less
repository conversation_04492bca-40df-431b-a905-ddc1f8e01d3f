@color-primary: #1A75FF;

.template-modal-content{
  .modal-title{
    font-size: 18px;
    line-height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 11px;
    padding-bottom: 8px;
    .modal-title-item{
      margin-right: 72px;
      text-align: center;
      color: #BBBBBB;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      .item-line{
        width: 24px;
        height: 2px;
        border-radius: 1px;
        margin-top: 4px;
      }
      .item-checked{
        background-color: @color-primary;
      }
    }
    .modal-title-item:last-child{
      margin-right: 0;
    }
    .modal-title-item-right{
      margin-right: 0;
    }
    .item-text-checked{
      color: #333333;
     
    }
  }
  .template-modal-main{
    display: flex;
    height: 443px;
    background: #F8F8F8;
    border-top:1px solid #EFF1F3;
    border-radius: 8px;
    .template-type-container{
      background: #ffffff;
      border-bottom-left-radius: 8px;
      .template-type-item{
        width: 134px;
        padding: 10px;
        text-align: center;
        color: #333330;
        cursor: pointer;
        .template-name{
          font-size: 14px;
          line-height: 24px;
        }
      }
      .template-type-item-active{
        color: @color-primary;
        font-weight: 600;
        background: rgba(26, 117, 255, 0.08);
        
      }
    }
    .template-list-container{
      border-left: 1px solid #E6E6E6;
      background: #F8F8F8;
      padding: 20px 0 20px 20px;
      overflow-y: scroll;
      overflow-x: hidden;
      border-bottom-right-radius: 8px;
      // margin-right: 8px;
      flex: 1;
      .one-template-type-list{
        .one-template-type-title{
          font-size: 16px;
          line-height: 22px;
          font-weight: 600;
          margin-bottom: 10px;
        }
        .one-template-type-main{
          display: flex;
          flex-wrap: wrap;
          .one-template-container{
            &:nth-child(4n) {
              margin-right: 0;
            }
            margin-bottom: 22px;
            margin-right: 20px;
            text-align: center;
            width: 116px;
            .template-img-container{
              position: relative;
              padding: 10px;
              border: 0.5px solid #E8E8E8;
              border-radius: 4px;
              background-color: #ffffff;
              cursor: pointer;
                &:hover{
                box-shadow: 0px 1px 13px 0px rgba(0, 0, 0, 0.1);
                .hover-container{
                  transform: scale(1.05, 1.05) ;
                  transition-duration: 0.3s;
                }
                .hover-button-wrap{
                  opacity: 1;
                }
              }
              .hover-container{
                font-size: 0;
               
                .hover-container-img{
                  width:100%;
                  height: 79px;
                  object-fit: cover;
                }
                .hover-container-doc{
                  height: 120px;
                }
              }
              
              .hover-button-wrap{
                opacity: 0;
                background: #ffffff;
                position: absolute;
                bottom: 8px;
                left: 0;
                z-index: 1;
                width: 100%;
                height: calc(100% - 16px);
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                align-items: center;
                background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.57) 22%, #FFFFFF 99%);
                .btn-hover{
                  width: 84px;
                  height: 24px;
                  border: 1px solid rgba(0, 0, 0, 0.15);
                  border-radius: 3px;
                  background-color: #fff;
                  cursor: pointer;
                  outline: none;
                }
                .btn-hover-use{
                  margin-top: 4px;
                  background-color: #333333;
                  color: #FFFFFF;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                }
              }
            }

            .template-name{
              color: #666666;
              line-height: 17px;
              font-size: 12px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              margin: 8px 0 0 0;
            }
          }
        }
      }
    }

  }
  .template-moddal-iframe {
    padding: 20px;

    iframe {
      width: 660px;
      height: 100%;
      border: none;
    }
  }
  .empty-wrap{
    background: #F8F8F8;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    // margin-left: -20px;
    .empty-img{
      width: 70px;
      height: 70px;
    }
    .empty-title{
      margin-top: 24px;
      color: #BBBBBB;
      font-size: 12px;
      line-height: 17px;
    }
  }
}