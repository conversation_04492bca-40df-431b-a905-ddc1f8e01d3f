.template-modal-content{
 
  .preview-item{
    height: 395px;
    overflow-y: scroll;
    z-index: 1;//transform时会重写overflow
    text-align: center;
    padding-top: 40px;
    background: #F8F8F8;
    border-bottom: 1px solid #EFF1F3;
    border-top: 1px solid #EFF1F3;
    .preview-img{
      width: 634px;
      height: auto;
      box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.06);
    }
  }

  @keyframes swiper-preloader-spin {
    100% {
      transform: rotate(360deg); } 
  }

  .loading-div{
    text-align: center;
    color: #bbb;
    font-size: 12px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 40px;
    img {
      width: 16px;
      vertical-align: middle;
      margin-right: 6px;
      position: relative;
      top: -1px;
      animation: swiper-preloader-spin 1s infinite linear;
    }
  }

  .swipe-bottom-btn{
    background: #ffffff;
    padding: 24px;
    display: flex;
    border-bottom-right-radius: 8px;
    border-bottom-left-radius: 8px;
    font-size: 14px;
    line-height: 20px;
    .swipe-button-wrap{
      flex: 1;
      .swipe-button{
        background: none;
        line-height: 20px;
        padding: 7px 24px;
        color: #333333;
        border: 1px solid #E6E6E6;
        text-align: center;
        border-radius: 3px;
        cursor: pointer;

        &:focus {
          outline: none;
        }
      }
      .swipe-button-prev{
        margin-right: 12px;
      }
      .grey-btn{
        border: 1px solid #E6E6E6;
        color: #999999;
      }
      
    }
    .use-template{
      background: #000000;
      border: 1px solid #212224;
      border-radius: 3px;
      padding: 7px 10px;
      color: #ffffff;
      height: auto;
      cursor: pointer;
        &:focus {
          outline: none;
        }
    }
    
  }
}