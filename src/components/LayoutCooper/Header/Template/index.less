.template-modal-wrap{
  color: #333333;
  .ant-modal-close{
    top: 4px;
  }

  .ant-modal-close-x{
    position: absolute;
    top: 12px;
    right: 12px;

    &::before{
      // background-image: url('../icon/modal-close.png');
      background-size: 20px 20px;
    }
  
  }

  .ant-modal-header{
    padding: 32px 32px 0px 32px;
    border-bottom: none;
    border-radius: 8px;
    .ant-modal-title{
      .title-container{
        display: flex;
        align-items: center;
        .icon-arrow-up{
          width: 18px;
          height: 25px;
          // background-image: url('../icon/icon-arrow-left.svg');
          background-size: 18px 18px;
          background-repeat: no-repeat;
          background-position: center center;
          margin-right: 4px;
          cursor: pointer;
        }
        p{
          font-size: 18px;
          line-height: 25px;
          color: #000000;
          font-weight: 600;
        }
      }
      .title-container-style{
        margin-bottom: 14px;
      }
    }
   
    
  }
  .ant-modal-body{
    padding: 0;
  }
  .ant-modal-content{
    border-radius: 8px;
  }
  .hidden{
    display: none;
  }
}