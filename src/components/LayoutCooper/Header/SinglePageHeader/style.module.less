.single-page-header{
  background: @white;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 106;
  border-bottom: 1px solid rgba(34,42,53,.08);
  background-color: #fff;
  min-width: 1185px;
  .left{
    margin: 18px;
  }
  .middle{
    display: flex;
  }
  .right{
    margin: 18px 24px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 260px;
    height: 56px;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 19px;
  }
}