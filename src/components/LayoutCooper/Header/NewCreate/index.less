.create {
  width: 390px;
  .tenant-tips {
    width: 360px;
    height: auto;
    border-radius: 4px;
    opacity: 1;
    color: #1a6eff;
    background: rgba(26,110,255,.09);
    /* line-height: 27px; */
    text-align: start;
    padding-left: 6px;
    margin-bottom: 7px;
  }
  >div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    img {
      width: 32px;
      height: 32px;
      margin: 0 8px 0 12px;
    }
    .create-li {
      cursor: pointer;
      border-radius: 4px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #222A35;
      position: relative;
      &:hover {
        background-color: #F2F3F3;
      }
    }
    .create-li-new {
      background: #FF563B;
      border-radius: 2.75px;
      color: #ffffff;
      height: 12px;
      line-height: 11px;
      padding: 0 2px;
      font-size: 11px;
      position: absolute;
      top: 3px;
      left: 26px;
      transform: scale(0.9);
    }
    .create-li-beta {
      border-radius: 20px;
      background: #EBEEF1;
      color: #6A707C;
      height: 13px;
      line-height: 13px;
      padding: 0 4px;
      margin-left: 4px;
      font-size: 11px;
    }
  }
  &-folder {
    .create-li {
      width: 100%;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
  }
  &-space {
    padding-top: 4px;
    border-bottom: 1px solid #F6F7F7;
    .create-li {
      width: 100%;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
  }
  &-collab {
    margin-top: 4px;
    border-top: 1px solid #F6F7F7;
    padding-top: 4px;
    .create-li {
      width: 194px;
      height: 42px;
      display: flex;
      align-items: center;
    }
  }
  &-collab-old {
    margin-top: 4px;
    border-top: 1px solid #F6F7F7;
    padding-top: 4px;
    .create-li {
      width: 194px;
      height: 42px;
      display: flex;
      align-items: center;
      .old-tag {
        margin-left: 4px;
        font-family: PingFangSC-Medium;
        width: 32px;
        height: 18px;
        line-height: 18px;
        text-align: center;
        background-color: #EEEEEF;
        border-radius: 2px;
        font-size: 12px;
        color: #909499;
      }
    }
  }
  &-import {
    margin-top: 4px;
    border-top: 1px solid #F6F7F7;
    padding-top: 4px;
    .create-li {
      width: 100%;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
  }
  &-template {
    margin-top: 4px;
    border-top: 1px solid #F6F7F7;
    padding-top: 4px;
    .create-li {
      width: 100%;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
  }
}