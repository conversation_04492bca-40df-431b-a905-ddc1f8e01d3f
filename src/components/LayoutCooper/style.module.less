// 左右，右上下结构
.layout {
  display: flex;
  height: 100%;
  position: relative;
  flex: 1;
  overflow: hidden;
  // transition: all 2s ease;

  &.diditenant{
  }


  .left-wrap{
    position: relative;
    display: flex;
    // cursor: auto;
    height: 100%;
    z-index: 106; // 消息通知被挂载，需要消息通知在“最近访问”上、在知识库上；要在整屏弹窗下

    &:hover{
      .aside-operate{
        visibility:inherit;
      }
    }

    .left {
      height: 100%;
      background-color: #F7F9FA;
      position: relative;
      display: flex;
      transition: all 2s ease;
      overflow-x: hidden;
      overflow-y: overlay;
      overflow-y: auto;
    }

    .aside-btn-handle{
      width: 20px;
      height: 100%;
      cursor: pointer;
      position: absolute;
      right: -20px;
      top: 0;
      background-color: transparent;
      &:hover{
        .aside-operate{
          visibility: inherit;
        }
      }
    }
   
  }
  

  .right {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    
    .body-wrap {
      flex: 1;
      overflow-y: hidden;
      min-width: 745px;
      position: relative;
    }
  }


}

.aside-operate{
  width: 14px;
  height: 56px;
  border-radius: 64px;
  display: flex;
  border: 1px solid @blueGray-16;
  visibility: hidden;
  align-items: center;
  justify-content: center;
  position: relative;
  top: 69px;
  right: 8px;
  background-color: @white;
  z-index: 999;
  cursor: pointer;
  .operate-icon{
    color: @blueGray-8;
    font-size: 14px;
  }
  
  &:hover {
    .operate-icon{
      color: @blueGray-3;
    }
  }
}
.isZhankai{
  .operate-icon{
    transform: rotateY(180deg);
  }
}



