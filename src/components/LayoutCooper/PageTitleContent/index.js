import { intl } from 'di18n-react';
import classBind from 'classnames/bind';
import { useLocation } from 'react-router-dom';
import styles from './style.module.less';

const cx = classBind.bind(styles);
/**
 *
 * @param {value} 标题名，不传默认根据url判断
 * @param {titleIcon} 标题图标
 * @param {titleRight} 标题右侧内容  如：信息透传icon
 * @param {children} 右侧操作项 如:上传 新建
 * @returns
 */
const PageTitleContent = ({ titleIcon, value, titleRight, children }) => {
  const { pathname } = useLocation();

  const titleText = () => {
    let text = '';
    switch (pathname) {
      case '/':
        text = intl.t('首页');
        break;
      case '/disk':
        text = intl.t('个人空间');
        break;
      case '/knowledge':
        text = intl.t('知识库');
        break;
      case '/portalList':
        text = intl.t('知识门户');
        break;
      case '/team-folder':
        text = intl.t('团队空间');
        break;
      case '/favorite':
        text = intl.t('收藏');
        break;
      case '/share':
        text = intl.t('分享');
        break;
      case '/trash':
        text = intl.t('回收站');
        break;
      default:
        break;
    }
    return text;
  };
  return (
    <div className={cx('page-title-wrap')}>
      <div className={cx('title-name')}>
        {titleIcon && <img
          src={titleIcon}
          className={cx('title-icon')} />}
        {value ?? titleText()}
        {titleRight}
      </div>
      {children}
    </div>
  );
};

export default PageTitleContent;
