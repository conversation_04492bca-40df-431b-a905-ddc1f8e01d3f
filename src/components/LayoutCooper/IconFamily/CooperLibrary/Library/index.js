/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-07-25 10:31:44
 * @LastEditTime: 2023-07-28 15:36:40
 * @Description: 简单描述
 * @FilePath: /knowledgeforge/src/components/LayoutCooper/IconFamily/CooperLibrary/Library/index.js
 *
 */

import { intl } from 'di18n-react';
import React from 'react';
import './index.less';
import { isDC } from '@/utils/index';
import { fileFlow } from '@/assets/icon/fileIcon'
import { Helmet } from 'react-helmet';


class CooperLib extends React.Component {
  componentDidMount() {
    window.document.title = `${intl.t('实验室')} - Cooper`;
  }

  createDraw = () => {
    // __mirrorSendEvent('LAB_FLOWCHART_CLICK');
    window.__OmegaEvent('ep_lab_flowchart_ck', '', {
      platform: 'new',
    });

    if (isDC()) {
      window.open('/docs/flow/draw');
    } else {
      const win = window.open('about:blank');
      win.location.replace('/docs/flow/draw');
    }
  }

  render() {
    return (
      <div className='v3-cooper-lib'>
        <Helmet>
          <link
            rel="shortcut icon"
            type="image/png"
            href="//img-ys011.didistatic.com/static/cooper_cn/do1_IdgHKEBYk8gvDIAQMp8C" />
        </Helmet>
        <h1 className='lib-title'>{intl.t('实验室')}</h1>
        <div className='card-box'>
          <div
            className='card-item'
            onClick={this.createDraw}>
            <img
              className='card-img'
              src={fileFlow}
              alt='' />
            <div className='card-text-box'>
              <h2 className='card-header'>{intl.t('流程图文档')}</h2>
              <p className='card-detail'>{intl.t('默认保存在个人空间，暂不支持多人协作')}</p>
            </div>
          </div>

        </div>
      </div>
    );
  }
}

export default CooperLib;
