.cooper-notify-popover {
  .ant-popover-inner-content {
    padding: 0 !important;
  }

  .ant-popover-inner-content li {
    height: auto !important;
  }
}

.cooper-notify-icon {
  display: inline-block;
  position: relative;
  text-align: center;
  cursor: pointer;
  // width: 24px;
  text-align: center;
  // height: 24px;
  // line-height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 18px;
  &:hover {
    background: #EBEEF1;
  }
  .dk-icon-zhanneixin {
    font-size: 16px;
    color: @blueGray-15;
    width: 24px;
    height: 24px;
    line-height: 24px;
  }
}
.notify-list-item-detail-desc {
  .title-con {
    width: 87%;
    word-break: break-word;
  }
}

.cooper-notify > img {
  width: 20px;
  height: 20px;
  vertical-align: middle;
  cursor: pointer;
  margin-bottom: 4px;
}

.cooper-notify-position {
  position: relative;
}

.cooper-notify-count {
  position: absolute;
  top: -6px;
  left: 10px;
  background-color: #ff563a;
  border: 1px solid #ffffff;
  color: #fff;
  display: inline-block;
  min-width: 16px;
  height: 16px;
  font-size: 12px;
  line-height: 14px;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  padding: 0 4px;
  z-index: 100;
}

.cooper-notify .notify-list {
  width: 400px;
  height: 435px;
  // position: absolute;
  bottom: 32px;
  left: 0px;
  background-color: #fff;
  z-index: 999;
  text-align: left;
  // margin-left: 8px;
  border-radius: 8px;

  // &::after {
  //   content: '';
  //   position: absolute;
  //   bottom: -4px;
  //   left: 48px;
  //   width: 10px;
  //   height: 10px;
  //   background-color: #fff;
  //   box-shadow: -4px -4px 12px 0 rgba(0, 0, 0, .15);
  //   transform: rotate(45deg);
  //   z-index: 9999;
  // }

  &.hide {
    display: none;
  }

  &-box {
    width: 400px;
    border-radius: 8px;
    // box-shadow: 0 4px 16px 0 rgba(0, 0, 0, .15);
  }

  &-header {
    display: flex;
    line-height: 40px;
    padding: 0 16px;
    color: #8a93a8;

    :nth-child(1) {
      margin-right: 40px;
    }

    &-ceil {
      position: relative;
      cursor: pointer;
      user-select: none;
      &::before {
        content: '';
        display: none;
        position: absolute;
        left: 50%;
        bottom: -1px;
        width: 14px;
        border-bottom: 1px solid #fff;
        transform: translateX(-50%);
      }
      &::after {
        content: '';
        display: none;
        position: absolute;
        left: 50%;
        bottom: -5px;
        width: 10px;
        height: 10px;
        border-top: 1px solid #e4e9f3;
        border-right: 1px solid #e4e9f3;
        transform: translateX(-50%) rotate(-45deg);
      }
      &:not(.disabled):hover {
        font-weight: bold;
        color: #17233e;
      }

      &.active {
        font-weight: bold;
        color: #17233e;
        &::after, &::before {
          display: block;
        }
      }
      &.disabled {
        color: #ccc;
        cursor: not-allowed;
      }
    }

    &-allAsRead {
      background-color: #fff;
      border: none;
      margin-left: auto;
      cursor: pointer;
      &:hover {
        color: #0066FF;
      }
      &:focus {
        outline: none;
      }
      &.hide {
        display: none;
      }
    }
  }

  &-body {
    overflow: hidden;
    width: 100%;
    height: 393px;
    border-top: 1px solid #e4e9f3;
    word-break: break-all;

    &-box {
      padding-top: 4px;
    }

    &-empty {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      color: #bec5d2;
      &-box {
        text-align: center;
      }
      &-icon {
        display: inline-block;
        width: 14px;
        height: 14px;
        // background: url('../../common/images/<EMAIL>') no-repeat center center;
        background-size: cover;
      }
    }

    &-loading {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      li {
        list-style: none;
      }
    }
  }
  .notify-list-item {
    .notice-bg-tips {
      background: #fde093;
      padding: 4px 6px;
      border-radius: 2px;
      margin-top: 5px;
    }
    .bold {
      font-weight: 600;
    }
  }

  &-item {
    padding: 0 16px;
    &:not(:first-child) {
      .notify-list-item-box {
        border-top: 1px solid #e4e9f3;
      }
    }
    &:hover {
      background-color: rgba(63, 129, 255, .06) !important;
    }
    &.unread {
      background-color: #e4e9f330;
    }
    &-active {
      color: #0066FF;
    }
    &-box {
      padding: 12px 0;
    }
    &-main {
      display: flex;
    }
    &-avatar {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      overflow: hidden;
      img {
        position: relative;
        width: 100%;
        min-height: 100%;
        font-size: 0;
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: #eee;
        }
      }
    }
    &-detail {
      flex: 1;
      margin-left: 8px;
      font-size: 12px;
      &-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        line-height: 16px;
        color: #8a93a8;
        &-left {
          width: 100%;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
          word-wrap: break-word;
          white-space: nowrap;
          max-width: 300px;
          // .tag-global {
          //   flex: 1;
          // }
        }
      }
      &-desc {
        line-height: 16px;
      }
      &-name {
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        word-wrap: break-word;
        white-space: nowrap;
        max-width: 120px;
        a {
          color: inherit;
        }
      }
      &-department {
        display: inline-block;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        word-wrap: break-word;
        white-space: nowrap;
      }
      &-time {
        margin-left: 10px;
        flex: 0 0 auto;
      }
      &-text-area {
        margin: 8px 0 22px 0;
        border-radius: 2px;
        > textarea {
          font-size: 14px;
          border-radius: 2px;
          background-color: @blueGray-12 !important;
        }
      }
    }

    &-access-setting {
      margin-top: 8px;
      line-height: 16px;
      .ant-checkbox {
        &-wrapper + .ant-checkbox-wrapper {
          margin-left: 20px;
        }
        &-inner {
          border-color: #BEC5D2;
        }

        &-wrapper:hover .ant-checkbox-inner,
        &:hover .ant-checkbox-inner,
        &-input:focus + .ant-checkbox-inner,
        &-checked .ant-checkbox-inner,
        &-indeterminate .ant-checkbox-inner {
           border-color: #0066FF;
        }
        &-checked .ant-checkbox-inner,
        &-indeterminate .ant-checkbox-inner {
          background-color: #0066FF;
        }

        & + span {
          padding-left: 4px;
          padding-right: 0;
        }
        &-inner {
          border-radius: 50%;
        }
        &-checked::after {
          border-radius: 50%;
        }

        &-disabled {
          & + span {
            color: #8A93A8;
          }
          .ant-checkbox-inner {
            border-color: #BEC5D2 !important;
            background-color: #fff;
          }
          &.ant-checkbox-checked {
            .ant-checkbox-inner {
              background-color: #BEC5D2;
              &::after {
                border-color: #fff;
              }
            }
          }
        }
      }
    }

    &-btns {
      margin-top: 12px;
      display: flex;
      justify-content: flex-end;
      line-height: 26px;
      a {
        border-radius: 4px;
        font-size: 12px;
      }
    }
    &-reject {
      padding: 1px 15px;
      border: 1px solid #e4e9f3;
      background-color: #fff;
      transition: all .3s;
      &:hover {
        color: @primary-color;
        border-color: @primary-color;
      }
    }
    &-accept {
      padding: 2px 16px;
      background-color: @primary-color;
      color: #fff;
      margin-left: 10px;
      transition: all .3s;
      &:hover {
        color: #fff;
        background-color: @primary-color;
      }
    }

    &-status {
      line-height: 24px;
      font-size: 12px;
      color: #8a93a8;
    }
  }

  &-loading {
    padding: 10px 0;
    text-align: center;
    color: #8a93a8;
    img {
      width: 14px;
      height: 14px;
      vertical-align: middle;
    }
    span {
      margin-left: 4px;
      vertical-align: middle;
    }
  }
}
.notify-list-small .notify-list {
  left: 43px;
  bottom: 10px;
  &::before {
    width: 25px;
    height: 80px;
    left: -25px;
  }
 
}
.notify-list-small {
  // width: 40px;
  // height: 40px;
  line-height: 40px;
  text-align: center;
  margin-right: 0px;
  margin-top: 5px;
  .dk-icon-zhanneixin {
    font-size: 18px;
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
  > span {
    left: 18px;
    top: 2px;
  }
}
.notify-list-loading {
  justify-content: center;
}
// @media screen and (-webkit-min-device-pixel-ratio: 2) {
//   .cooper-notify {
//     .notify-list {
//       &-body {
//         &-empty {
//           &-icon {
//             // background-image: url('../../common/images/<EMAIL>');
//           }
//         }
//       }
//     }
//   }
// }

.notify-list-item-bold-name {
  font-weight: 600;
  color: @blueGray-1;
}