

.aside-large-wrap{
  width: 240px;
  opacity: 1;
  // transition: all 0.1s linear;
}
.aside-small-wrap{
  width: 64px;
  opacity: 1;
  // transition: all 0.1s linear;
}
.aside {
  display: flex;
  flex-direction: column;
  padding-top: 10px;
  
  
  .aside-content{
    position: relative;
    margin-top: 20px;
    flex: 1;
  }

  .aside-content-dc {
    margin-top: 9px;
  }

  
  .item-name {
    width: 100%;
    color: @blueGray-1;
    margin-bottom: 2px;
    .ellipsis();
    padding: 0 8px;
  }


  .item-name-normal {
    padding: 12px 8px 12px 12px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    border-radius:@border-radius-sm;
    cursor: pointer;
    &:hover {
      background-color: @blueGray-16;
    }
   
  }

  .item-container {
    display: flex;
    align-items: center;
    width: auto;
  }

  .item-small-container{
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-around;
    flex-direction: column;
    >i{
      padding-top: 4px;
    }
    >span {
      width: 200%;
      overflow: hidden;
      text-align: center;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 0 4px;
    }
  }
  .aside-icon{
    color: @blueGray-15;
  }

  .item-text {
    padding-left: 12px;
    .ellipsis();
  }
  

  .item-name-active {
    color: @primary-color ;
    background-color: @primary-1;
    font-weight: @font-weight-medium;

    .aside-icon{
      color:  @primary-color;
    }
    &:hover {
      background-color:  @primary-1;
    }
  }

}
.left-action {
  background-color: #F7F9FA ;
  width: 240px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  // padding: 0 19px;
  z-index: 2;
  margin-left: 22px;
}
.left-action-tenant {
  justify-content: flex-start;
  // padding: 0 22px;
}
.left-action-small {
  width: 64px;
  height: 145px;
  background-color: #F7F9FA ;
  display: flex;
  flex-direction: column-reverse;
  padding: 19px 0;
  justify-content: flex-start;
  margin-left: 0;
  // z-index: 999;
}

.icon-arrow-right{
  margin-left: 2px;
  color: @blueGray-8;
  font-size: 12px;
}
.icon-aside-large{
  font-size: 18px;
  line-height: 20px;
}
.split-line{
  width: 100%;
  height: 1px;
  background: @blueGray-16;
  margin: 8px 0;
}
.more-title{
  font-size: 12px;
  margin-bottom: 8px;
  padding-left: 12px;
  color: @blueGray-6;
}


