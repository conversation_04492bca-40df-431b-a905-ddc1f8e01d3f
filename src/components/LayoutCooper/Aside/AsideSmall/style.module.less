.no-width {
  width: 0;
  opacity: 0;
  // position: absolute;
  // top: 0;
  // left: -100px;
}

.aside-small-box {
  width: 64px;
  flex: 1;
}

.aside-small {
  height: 100%;
  padding: 10px 2px 120px 2px;
}

.space-content-popover {
  padding-left: 4px !important;

  :global {
    li {
      height: auto !important;
      padding: 0 !important;

      &:hover {
        background: none !important;
      }
    }

    .ant-popover-inner-content {
      padding: 8px !important;
    }
  }

}

.item-name {
  margin-bottom: 6px !important;
}

.item-name-normal {
  height: auto !important;
  padding: 4px 0 !important;
}

.small-name {
  font-size: 22px !important;
  transform: scale(0.5);
}

.title-space {
  display: inline-flex;
  align-items: center;
  padding-left: 18px;
  margin-bottom: 8px;
  // width: 92px;
  border-radius: 4px;
  padding: 0 12px;
  cursor: pointer;

  .title-text {
    .font-size(13px);
    color: @blueGray-6;
  }

  &:hover {
    background: @blueGray-16;
  }
}

.popover-space {
  width: 228px;
}

:global{
  @media screen and (max-height: 726px) {
    .windows .aside-small-box-media li {
      margin-bottom: 0 !important;
    }
  }
}