
.space-content-wrap{
  width: 100%;
  .font-size(13px);
 
  .space-item{
    width: 100%;
    color: @blueGray-1;
    margin-bottom: 2px;
   

    &:hover{
      .icon-pin{
        display: flex !important;
      }
    }

    .wrap{
      width: 100%;
    }

    .space-item-normal{
     
      padding: 7px 12px;
      width: 100%;
      display: inline-block;
      border-radius:@border-radius-sm;
     
    
      &:hover {
        background-color: @blueGray-12;
        .icon-pin{
          display: block;
        }
        .icon-dian {
          color: @blueGray-8 !important;
        }
      }

      .content-wrap{
        display: flex;
        align-items: center;
        justify-content: space-between;
        .ellipsis();

        .left-wrap{
          display: flex;
          align-items: center;
          .ellipsis();
  
          .icon-dian{
            font-size: 20px;
            line-height: 14px;
            color: @blueGray-17;
          }
          .item-text{
            padding-left: 4px;
            .ellipsis();
          }
        }
       
        .icon-pin{
          display: none;
          font-size: 16px;
          line-height: 16px;
          padding: 2px;
          color: @blueGray-15;
          border-radius: @border-radius-sm;
          &:hover {
            background-color:@blueGray-17;
          }
        }
      }
    }
    .space-item-active {
      color: @primary-color;
      background-color: @primary-1;
      font-weight: @font-weight-medium;
      .icon-dian {
        color: @primary-color !important;
      }
      &:hover {
        color: @primary-color;
        background-color: @primary-1;
        font-weight: @font-weight-medium;
        .icon-dian {
          color: @primary-color !important;
        }
      }
    }
    
  }
}
@media screen and (max-height: 726px) {
  .space-content-wrap{
    max-height: 167px !important;
  }
}

/** scrollbar for windows,mac and others**/
:global{
  .windows .space-content-wrap-os-flag{
    overflow-y: hidden;
    &:hover{
      overflow-y: auto;
    }
  }
  .mac, .linux, .generic{
    .space-content-wrap-os-flag{
      overflow-y: auto;
    }
  }
}
