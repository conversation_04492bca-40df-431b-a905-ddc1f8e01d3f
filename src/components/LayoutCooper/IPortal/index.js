
import { getUserNameFromCookie } from '@/utils';
import { getLocale } from 'di18n-react';
import { useEffect, useRef } from 'react';

// 默认配置 - cooper的
const DEFAULT_CONFIG = {
  appId: '11',
  appSecretKey: '25C3CF82715DE8FCD0D411CC6F1E32F7',
  secretKey: '0E557393BE781A93206891F02687468F',
};

function IPortal(props) {
  const {
    // 基础配置 - 支持传入自定义配置
    mountSelector,
    appId = DEFAULT_CONFIG.appId,
    appSecretKey = DEFAULT_CONFIG.appSecretKey,
    secretKey = DEFAULT_CONFIG.secretKey,
    id = 'iPortal-entry-cooper',
    children,

    // 事件回调 - 支持自定义回调函数
    onOpen,
    onClose,
    onSelect,

    // 其他配置
    ldap,
    customClass,
    instanceKey = 'popupCooperSDK', // 全局实例的key，用于区分不同实例
    ...otherProps
  } = props;

  const sdkInstanceRef = useRef(null);
  const locale = getLocale();

  useEffect(() => {
    if (!window.PopupSDK) {
      console.error('PopupSDK 未加载');
      return;
    }

    // 如果该实例已存在，直接返回
    if (window[instanceKey]) {
      sdkInstanceRef.current = window[instanceKey];
      return;
    }

    const config = {
      mountSelector: mountSelector || `#${id}`,
      appId,
      appSecretKey,
      secretKey,
      ldap: ldap || getUserNameFromCookie(),
      onOpen: onOpen || (() => {}),
      onClose: onClose || (() => {}),
      onSelect: onSelect || ((item) => {}),
    };

    // 如果有自定义样式类，添加到配置中
    if (customClass) {
      config.customClass = customClass;
    }

    const popupCooperSDK = new window.PopupSDK(config);

    // 保存实例引用
    sdkInstanceRef.current = popupCooperSDK;

    // 将实例挂载到window对象，使用不同的key区分不同实例
    window[instanceKey] = popupCooperSDK;


    // 清理函数
    return () => {
      if (window[instanceKey]) {
        // 如果SDK提供销毁方法，在这里调用
        window[instanceKey] = null;
      }
      sdkInstanceRef.current = null;
    };
  }, [mountSelector, appId, appSecretKey, secretKey, id, ldap, customClass, instanceKey, onOpen, onClose, onSelect]);

  useEffect(() => {
    /**
     * 支持动态更新ldap、lang配置：
     * 1. 在前端框架中，可在watch监听器或useEffect钩子中调用updateConfig方法
     * 2. 对于异步获取的ldap、lang数据，可在用户点击图标前更新配置
     */
    if (sdkInstanceRef.current && sdkInstanceRef.current.updateConfig) {
      sdkInstanceRef.current.updateConfig({
        lang: locale,
      });
    }
  }, [locale]);

  return (
    children || <div
      id={id}
      {...otherProps} />
  );
}

export default IPortal;
