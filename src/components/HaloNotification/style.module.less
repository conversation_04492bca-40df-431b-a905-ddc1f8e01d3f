

.link {
  color: @primary-color;
  cursor: pointer;
}

.publish-text {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.close-icon {
  margin-left: 8px;
  font-size: 24px;
  line-height: 28px;
  cursor: pointer;
}

.notification-modal {
  padding-bottom: 0;

  :global {
    .ant-modal-content {
      border-radius: 6px;

      .ant-modal-body {
        padding: 32px 28px 24px;

        .ant-modal-confirm-title {
          font-size: 24px;
        }

        .ant-modal-confirm-content {
          margin-top: 10px;
          font-size: 16px;
          color: #444B4F;
          line-height: 24px;
        }

        .ant-modal-confirm-btns {
          margin-top: 36px;

          .ant-btn {
            height: 44px;
            background: #F6F6F6;
            border-radius: 4px;
            border: none;
            font-size: 16px;
            color: #444B4F;

            &::after {
              display: none;
            }

            &.ant-btn-primary {
              margin-left: 16px;
              color: #FFFFFF;
              background: @primary-color;
            }
          }
        }
      }
    }
  }
}
