.global-search-wrap {
  height: 32px;
  line-height: 32px;
  width: 100%;
  position: relative;
  .search-before {
    display: flex;
    align-items: center;
    background: none;
    height: 24px;
    line-height: 24px;
    color: @blueGray-8;
    border: none;
    padding-left: 3px;
    font-size: 14px;
  };
  .search-tag {
    margin-left: 8px;
    padding: 0 4px 0 8px;
    border-radius: 3px;
    background: @blueGray-16;
    display: flex;
    align-items: center;
    color: @black-4;
    
    >span{
      >i{
        color: #333;
        font-size: 14px;
        cursor: pointer;
        
      }
    }
    &:hover {
      background: @primary-1;
      >span{
        >i{
          color: @blueGray-5;
        }
      }
    }
  }
  .search-icon-btn {
    display: block;
    align-items: center;
    line-height: 14px;
    font-size: 12px;
    border: 1px solid @blueGray-9;
    padding: 2px 6px;
    border-radius: 4px;
    margin-right: 4px;
    color: @black-6;
    cursor: pointer;
    &:hover {
      background: @blueGray-17;
      border-color: transparent;
    }
    .search-text{
      margin-right: 5px;
    }
  }

  :global{
    .ant-input-affix-wrapper{
      height: 32px;
    }
    .ant-input-group-addon {
      border-radius: 4px;
    }
  }

}

.click-tip {
  padding-right: 12px;
}

.global-search-popover {
  width: 540px !important;
  max-width: 540px !important;
  z-index: 1069 !important;
  padding-top: 3px!important;
 
 
  .search-popover-content {
    margin: 0px 6px 10px 6px;
    li{
      height: auto !important;
      margin-bottom: 0px!important;
      border: none!important;
      padding: 0!important;
      &:hover{
        background-color: transparent !important;
      }
    }
  }
  .sticky-content{
    position: sticky;
    top: 0;
    margin: 6px 10px 0px 10px;
  }
  .search-more-wrap {
    width: 100%;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 8px;
  }
  .search-more {
    cursor: pointer;
    color: rgba(34, 42, 53, 0.5);
    border-radius: @border-radius-lg;
    padding: 8px 12px;

    .more-icon-wrap{
      background: @primary-color;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 22px;
      height: 22px;
      margin-right: 9px;
      i{
        color: @white;
        font-size: 14px;
      }
    }

    .more-b {
      font-weight: @font-weight-medium;
      color: @blueGray-2;
      .font-size(14px);
    }
   
    &:hover {
      background: @blueGray-13;
    }
  }
}

/** scrollbar for windows,mac and others**/
:global{
  .mac, .linux, .generic{
    .search-popover-content-os-flag {
      overflow: auto;
      overflow: overlay;
    }
  }
  .windows .search-popover-content-os-flag {
    overflow: hidden;
    &:hover{
      overflow: auto;
    }
  }
}