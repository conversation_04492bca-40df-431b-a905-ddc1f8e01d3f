.page-style-trigger {
  cursor: pointer !important;

  &::before {
    position: absolute;
    content: '';
    left: -15px;
    top: 0;
    width: 26px;
    height: 59px;
  }

  .page-style-title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .icon-expand {
      margin-right: 0;
      margin-top: -2px;
    }

    .icon-page-style {
      vertical-align: text-top;
    }
  }

  .page-style-title-menu {
    display: flex;
    align-items: center;
  }

  &:hover .page-style-popover {
    opacity: 1;
    transform: translateX(-100%);
    visibility: visible;
  }
}

.summer-tip {
  font-size: 12px;
  color: #94979B;
  line-height: 18px;
  margin: 2px 0 0 26px;
  text-shadow: 0 6px 36px rgba(34, 45, 60, 0.2);
}

.page-style-popover {
  position: absolute;
  left: -14px;
  top: -11px;
  transform: translateX(-90%);
  padding: 18px 16px 20px;
  background: #FFFFFF;
  box-shadow: 0 6px 36px 0 rgba(34, 45, 60, 0.2);
  border-radius: 6px;
  border: 1px solid #F1F1F1;
  opacity: 0;
  visibility: hidden;
  transition: all 300ms;

  .title {
    font-size: 14px;
    font-weight: 500;
    color: #2B3037;
    line-height: 20px;
    text-shadow: 0 6px 36px rgba(34, 45, 60, 0.2);
  }

  .content {
    display: flex;
    margin-top: 10px;

    .tab {
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 118px;
      border-radius: 6px;
      border: 1px solid #F1F1F1;
      cursor: pointer;
      padding: 8px;
      word-break: break-all;

      &:first-child {
        margin-right: 12px;
      }

      &.active {
        border: 1px solid rgba(47, 125, 246, 0.8);

        .small-font,
        .font-size-16 {
          color: rgba(47, 125, 246, 0.8);
        }

        .narrow-screen-icon {
          background: url("../../assets/icon/narrowScreenActive.png") no-repeat center/contain;
        }

        .wide-screen-icon {
          background: url("../../assets/icon/wideScreenActive.png") no-repeat center/contain;
        }
      }

      &.font-size {
        height: 62px;
      }

      &.page-width {
        height: 106px;
      }

      .narrow-screen-icon {
        width: 40px;
        height: 28px;
        border-radius: 2px;
        background: url("../../assets/icon/narrowScreen.png") no-repeat center/contain;
      }

      .wide-screen-icon {
        width: 40px;
        height: 28px;
        border-radius: 2px;
        background: url("../../assets/icon/wideScreen.png") no-repeat center/contain;
      }

      .text {
        font-size: 13px;
        color: #2B3037;
        line-height: 18px;
        margin-top: 2px;
      }

      .font-size-14 {
        font-size: 14px;
        line-height: 1;
      }

      .font-size-16 {
        font-size: 16px;
        line-height: 1;
      }

      .summary {
        font-size: 12px;
        color: #94979B;
        line-height: 18px;
        margin-top: 2px;
      }
    }
  }

  hr {
    height: 1px;
    box-shadow: 0 6px 36px 0 rgba(34, 45, 60, 0.2);
    border: none;
    background: #F4F5F5;
    margin: 15px auto;
  }

  .tips {
    display: flex;
    border-top: 1px solid #F4F5F5;
    font-size: 12px;
    line-height: 20px;
    color: #909499;
    padding-top: 12px;
    margin-top: 16px;

    .icon {
      margin-right: 2px !important;
    }
  }
}