.hint-header {
  display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    background-color: #eaf2ff;
    padding: 4px 16px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 101;
  .hint-icon {
    color: @primary-color;
    margin-right: 6px;
  }
  .hint-body {
    display: flex;
    flex: 1;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .hint-title {
      font-size: 12px;
      font-weight: 400;
      line-height: 17px;
    }
    .hint-close-icon-wrap{
      height: 100%;
      padding: 0 2px;
      .hint-close-icon {
        width: 10px;
        height: 10px;
      }
    }
    
  }
}
.hidden-hint{
  display: none;
}