.global-auth-tip {
  padding: 10px;
  border-radius: 6px;
  word-break: break-word;

  p {
    font-size: 14px;
    line-height: 22px;
    .auth-type {
      margin: 0px 3px;
      font-weight: bold;
    }
  }

  a {
    display: block;
    color: #047FFE;
    font-size: 12px;
    margin-top: 6px;
  }
  a:hover {
    text-decoration: underline;
    color: #047FFE;
  }

  .line {
    width: 100%;
    height: 1px;
    background: #F2F3F3;
    margin: 12px 0;
  }

  .name_link {
    display: inline-block;
    font-size: 14px;
    margin: 0px 3px;
  }
}

.global-auth-tip-pop {
  width: 260px !important;
}

.ant-popover.global-auth-tip-pop {
  .ant-popover-arrow {
    display: none !important;
    // .ant-popover-arrow-content {
    //   background-color: #FFFFFF !important;
    //   --antd-arrow-background-color: '';
    // }
  }
}