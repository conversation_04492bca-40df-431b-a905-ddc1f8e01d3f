// @import '../../components/theme.scss';

.link-invite {
  padding: 0 32px 32px;
    div {
      display: flex;
    }
  .ant-input {
    height: 36px;
    margin-right: 10px;
    // font-size: $fontSize;
    // color: $textColor;
    border-color: #e4e9f3;
    padding-left: 10px;

    &:hover,
    &:focus {
      border: 1px solid #e4e9f3;
      box-shadow: none;
    }
  }

  .ant-btn-primary {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #FFFFFF;
    height: 36px;
    padding: 0 16px;
    border: 0;
    color: #fff;
    min-width: 88px;
    height: 36px;
    background: #000000;
    border-radius: 4px;

    &:hover {
      background-color: #000000;
    }
  }

  > div:first-child {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  > div:nth-child(2) {
    margin: 10px 0 40px 0;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
  }

  > div:nth-child(3) {
    color: #666666;
    text-align: center;
    margin-bottom: 16px;
    height: 18px;
    position: relative;
    display: flex;

    &::before,
    &::after {
      content: '';
      height: 1px;
      background-color: rgba(#bec5d2, 0.5);
      flex: 1;
      transform: translateY(9px);
    }

    &::before {
      left: 0;
      margin-right: 10px;
    }

    &::after {
      right: 0;
      margin-left: 10px;
    }
  }

  > img {
    display: block;
    height: 100px;
    border: 1px solid rgba(#e4e9f3, 0.3);
    border-radius: 2px;
    margin: auto;
  }
}
