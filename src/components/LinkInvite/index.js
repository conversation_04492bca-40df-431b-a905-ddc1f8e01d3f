import { intl } from 'di18n-react';
import React from 'react';
import { Button, Input, message } from 'antd';
import CopyToClipboard from 'react-copy-to-clipboard';
import './index.less';

class LinkInvite extends React.Component {
  render() {
    const { link, qrCode } = this.props;
    return (
      <div className='link-invite'>
        <div>
          <Input
            value={link}
            readOnly />
          <CopyToClipboard
            text={link}
            onCopy={() => message.success(intl.t('复制成功'))}
          >
            <Button type='primary'>{intl.t('复制链接')}</Button>
          </CopyToClipboard>
        </div>
        <div>
          {intl.t('用户可以通过链接加入到此团队中，发送邀请链接前请确认邀请对象')}
        </div>
        <div>{intl.t('或扫描二维码邀请成员')}</div>
        <img src={qrCode} />
      </div>
    );
  }
}

export default LinkInvite;
