import { getLocale, intl } from 'di18n-react';
import { useEffect, useRef, useState } from 'react';
import classBind from 'classnames/bind';
import styles from './style.module.less';
import FAQDisplay from './FAQDisplay';
import 'splitting/dist/splitting.css';
import 'splitting/dist/splitting-cells.css';
import Splitting from 'splitting';
import { getApolloConfig } from '@/utils/ab';
import { openNewWindow } from '@/utils';

const cx = classBind.bind(styles);

/**
 *  Banner功能
 * @returns
 */
function Banner() {
  const [bannerData, setBannerData] = useState({});
  const [bannerVisible, setBannerVisible] = useState(true);
  const [total, setTotal] = useState(0);
  const [backgroundColor, setBackgroundColor] = useState('#FFF');
  const [prevIndex, setPrevIndex] = useState(0);
  const [targetIndex, setTargetIndex] = useState(0);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direct, setDirect] = useState('up');
  const [clickable, setClickable] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const currentIndexRef = useRef(currentIndex);
  const clickableRef = useRef(clickable);

  const isZH = getLocale() === 'zh-CN';

  const defaultData = {
    slides: [{
      title: '协作文档优化上线了！',
      title_en: 'Docs optimization is launched !',
      titleColor: '#01249E',
      desc: '文档表格支持插入图片；版本保存功能让您的工作成果有迹可循，随时回溯历史版本；评论功支持回复与点赞，促进团队成员间的互动交流；附件支持在线预览功能，大大提升了工作效率。您的创作与互动将迎来更便捷的体验！',
      desc_en: 'Docs support inserting images in tables inside, saving versions, replying/liking comments and online preview of attachments. Creation and interaction become more convenient!',
      descColor: 'rgba(1, 46, 158, 0.8)',
      bgColor: '#F0F4FF',
      img: 'https://img-ys011.didistatic.com/static/cooper_cn/do1_upkqEqBIMYduX1skquey',
      img_en: 'https://img-ys011.didistatic.com/static/cooper_cn/do1_JIImVQ2QZJ2UN1p5LTaJ',
      url: 'https://cooper.didichuxing.com/knowledge/share/book/V2fuj0ncesoZ/2202764413581',
      url_en: 'https://cooper.didichuxing.com/knowledge/share/book/V2fuj0ncesoZ/2202764413581',
    }, {
      title: '协作表格强势升级！',
      title_en: 'More powerful sheets ！',
      titleColor: '#3914B5',
      desc: '表格能力升级迭代，上新更多实用能力：支持跨表引用数据、多级下拉菜单、下拉列表支持多选、锁定单元格、协作过程支持独立视图等。让协作与数据处理更高效！',
      desc_en: 'It supports referencing data between different sheets, multi-level drop-down menus, drop-down lists with multiple selections, locking cells, etc. Collaboration and data processing become more efficient!',
      descColor: 'rgba(57, 20, 181, 0.8)',
      bgColor: '#F3F3FF',
      img: 'https://img-ys011.didistatic.com/static/cooper_cn/do1_KUEGPMdFYBpBlbBrKdk1',
      img_en: 'https://img-ys011.didistatic.com/static/cooper_cn/do1_fak53M8W5tHYWlKYYVyV',
      url: 'https://cooper.didichuxing.com/knowledge/share/book/V2fuj0ncesoZ/2202759911071',
      url_en: 'https://cooper.didichuxing.com/knowledge/share/book/V2fuj0ncesoZ/2202759911071',
    }],
    faqs: [
      { id: 1, content: '如何设置团队成员为管理员？', url: 'https://cooper.didichuxing.com/knowledge/share/book/V2fuj0ncesoZ/2201315968523' },
      { id: 2, content: '内容丢失如何找回？', url: 'https://cooper.didichuxing.com/knowledge/share/book/V2fuj0ncesoZ/2201315968523' },
      { id: 3, content: '第三条数据', url: 'https://cooper.didichuxing.com/knowledge/share/book/V2fuj0ncesoZ/2201315968523' },
      { id: 4, content: '第四条数据', url: 'https://cooper.didichuxing.com/knowledge/share/book/V2fuj0ncesoZ/2201315968523' },
      { id: 5, content: '第五条数据', url: 'https://cooper.didichuxing.com/knowledge/share/book/V2fuj0ncesoZ/2201315968523' },
    ],
    interval: 10000,
  };

  useEffect(() => {
    // 获取banner配置信息
    getBannerConfig().then((res) => {
      let renderData = JSON.stringify(res) !== '{}' ? res : defaultData;
      setBannerData(renderData);
      setBackgroundColor(renderData?.slides?.[0]?.bgColor);
      setTotal(renderData?.slides?.length);

      // run the SplittingJS magic here, using 'lines' as the splitting technique
      Splitting({
        by: 'chars',
        whitespace: false,
      });
    });
  }, []);

  useEffect(() => {
    let timerId;
    // 设置定时器，每秒更新一次计数
    const interval = bannerData?.interval;
    if (!isHovered && total > 1 && interval > 0) {
      timerId = setInterval(() => {
        slideToNext(true);
      }, interval)
    } else {
      clearInterval(timerId)
    }

    // 清除定时器
    return () => clearInterval(timerId);
  }, [isHovered, total]);

  useEffect(() => {
    currentIndexRef.current = currentIndex;
  }, [currentIndex]);

  const getSlidesLocaleData = (index, key) => {
    const slideConfig = bannerData?.slides[index];
    const key_en = `${key}_en`;
    return isZH ? slideConfig?.[key] : slideConfig?.[key_en];
  }

  const getSlidesLocaleDataTitle = (index) => {
    const slideConfig = bannerData?.slides[index];
    return slideConfig?.title;
  }

  /**
   * 展示下一个
   *
   * @param} isNext
   * @returns
   */
  const slideToNext = (isNext) => {
    if (!clickableRef.current) return;
    clickableRef.current = false;
    setClickable(false);

    // eslint-disable-next-line no-unused-expressions
    isNext ? setDirect('up') : setDirect('down');
    const nextIndex = isNext ? (currentIndexRef.current + 1) % total : (currentIndexRef.current - 1 + total) % total;

    // 设置正确的索引，以调整slide元素到正确的动画前的问题
    setPrevIndex(currentIndexRef.current);
    setTargetIndex(nextIndex);
    setBackgroundColor(bannerData?.slides[nextIndex]?.bgColor);

    // 开始动画
    const activeAnimationTId = setTimeout(() => {
      currentIndexRef.current = nextIndex;
      setCurrentIndex(nextIndex);
      const setClickabkeTId = setTimeout(() => {
        clickableRef.current = true;
        setClickable(true);
        clearTimeout(setClickabkeTId);
      }, 600);
      clearTimeout(activeAnimationTId);
    }, 10);
  }

  /**
   * 展示下一个点击事件处理
   */
  const slideToNextHandler = () => {
    setIsHovered(true);
    slideToNext(true);
    window.__OmegaEvent('ep_home_banner_slide_next_ck');
  };

  /**
   * 展示上一个点击事件处理
   */
  const slideToPrevHandler = () => {
    setIsHovered(true);
    slideToNext(false);
    window.__OmegaEvent('ep_home_banner_slide_before_ck');
  };

  // 处理鼠标悬停事件
  const bannerMouseEnterHandler = () => {
    setIsHovered(true);
  };

  // 处理鼠标移出事件
  const bannerMouseLeaveHandler = () => {
    setIsHovered(false);
  };

  const slideClickHandler = (index) => {
    const url = getSlidesLocaleData(index, 'url');
    const title = getSlidesLocaleDataTitle(index);

    openNewWindow(url);
    window.__OmegaEvent(
      'ep_home_banner_slide_ck',
      '轮播图点击',
      {
        title,
      },
    );
  }

  const closeBannerHandler = () => {
    setBannerVisible(false);

    window.__OmegaEvent(
      'ep_home_banner_close_ck',
      '关闭banner位',
    );
  }

  // 获取客服配置信息
  const getBannerConfig = () => {
    return new Promise((resolve, reject) => {
      getApolloConfig('banner').then((res) => {
        resolve(res);
      }).catch((err) => {
        reject(err);
      });
    });
  }

  return (
    <>
      { bannerVisible ? (
        <div
          className={cx('banner')}
          id='banner'>
          <div
            className={cx('banner_left', 'banner-item')}
            onMouseEnter={bannerMouseEnterHandler}
            onMouseLeave={bannerMouseLeaveHandler}
            style={{
              backgroundColor, // 自定义颜色
              transition: 'background-color 0.5s ease-in-out', // 自定义过渡
            }}
          >
            {bannerData?.slides?.map((slide, index) => {
              return (
                <div
                  key={index}
                  className={`slide slide-${index} ${(index === prevIndex) ? `slide-prev-${direct}` : ''} ${index === targetIndex ? `slide-target-${direct}` : ''}`}
                  data-active={`${index === currentIndex}`}
                  onClick={slideClickHandler.bind(null, index)}
                >
                  <div className='slide-content'>
                    <div
                      className='slide-title'
                      style={{ color: slide?.titleColor }}
                    >
                      <div data-splitting='true'>{getSlidesLocaleData(index, 'title')}</div>
                    </div>
                    <div
                      className='slide-desc two-line-ellipsis'
                      style={{ color: slide?.descColor }}
                    >
                      {getSlidesLocaleData(index, 'desc')}
                    </div>
                  </div>
                  <div className='slide-img-area'>
                    <div className='slide-img-container'>
                      <img
                        className='slide-img'
                        src={getSlidesLocaleData(index, 'img')}
                        alt="logo" />
                    </div>
                  </div>
                </div>
              );
            })}
            {
              total > 1 ? (
                <div className='slide-to'>
                  <div
                    className='slide-to_item slide-to_prev'
                    onClick={slideToPrevHandler}>
                    <i className={'dk-iconfont dk-icon-shangyiye'}/>
                  </div>
                  <div className='slide-to_item slide-to_indicator'>
                    <span>{total > 0 ? (currentIndex + 1) : 0}/{total}</span>
                  </div>
                  <div
                    className='slide-to_item slide-to_next'
                    onClick={slideToNextHandler}>
                    <i className={'dk-iconfont dk-icon-xiayiye'}/>
                  </div>
                </div>
              ) : null
            }
            {isHovered ? (
              <div
                className='close'
                onClick={closeBannerHandler}>
                <i className={'dk-iconfont dk-icon-shanchu4'}/>
              </div>
            ) : null}
          </div>
          <div className={cx('banner_right', 'banner-item')}>
            <FAQDisplay data={bannerData?.faqs}/>
          </div>
        </div>) : null }
    </>
  );
}

export default Banner;
