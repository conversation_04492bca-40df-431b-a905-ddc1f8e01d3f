import { getLocale, intl } from 'di18n-react';
import { Card } from 'antd';
import React, { useEffect, useState } from 'react';
import classBind from 'classnames/bind';
import { openNewWindow } from '@/utils/index';
import styles from './style.module.less';

const cx = classBind.bind(styles);

/**
 * FAQ展示
 * @param {*} param0
 * @returns
 */
function FAQDisplay({ data }) {
  const [stack, setStack] = useState([]);

  const isZH = getLocale() === 'zh-CN';

  useEffect(() => {
    const randomArray = shuffleArray(data);
    setStack(randomArray);
  }, [data]);

  const shuffleArray = (array = []) => {
    const shuffledArray = [...array]; // 创建数组的副本，以免修改原数组
    for (let i = shuffledArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1)); // 生成0到i之间的随机整数
      [shuffledArray[i], shuffledArray[j]] = [shuffledArray[j], shuffledArray[i]]; // 交换元素
    }
    return shuffledArray;
  }

  const displayNextPair = () => {
    setStack((prevStack) => {
      if (prevStack.length < 2) {
        return prevStack; // 如果元素不足两个，直接返回
      }

      // 弹出两个元素
      const first = prevStack.pop();
      const second = prevStack.pop();

      // 将它们放到栈底
      return [second, first, ...prevStack];
    });
  };

  const gotoFAQ = (item) => {
    if (item) {
      openNewWindow(isZH ? item.url : item.url_en);

      window.__OmegaEvent(
        'ep_home_banner_faq_ck',
        '常见问题点击',
        {
          title: item.content
        },
      );
    }
  }

  const currentDisplay = stack.slice(-2).reverse(); // 获取顶部两个元素进行展示

  return (
    <div className='faq-display'>
      <Card
        title={intl.t('常见问题')}
        extra={
          <div onClick={displayNextPair}>
            {intl.t('换一换')}
            <span>
              <i className={'dk-iconfont dk-icon-shuaxin2'}/>
            </span>
          </div>
        }
        style={{ width: '100%', height: 160 }}
      >
        <div className={cx('faq-items')}>
          {currentDisplay?.length === 2 ? (
            <>
              <div
                className={cx('faq-item', 'item-1')}
                onClick={gotoFAQ.bind(null, currentDisplay[0])}
              >
                {isZH ? currentDisplay[0]?.content : currentDisplay[0]?.content_en}
              </div>
              <div
                className={cx('faq-item', 'item-2')}
                onClick={gotoFAQ.bind(null, currentDisplay[1])}
              >
                {isZH ? currentDisplay[1]?.content : currentDisplay[1]?.content_en}
              </div>
            </>
          ) : null
          }
        </div>
      </Card>
    </div>
  );
}

export default FAQDisplay;
