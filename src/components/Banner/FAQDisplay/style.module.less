:global {
  .faq-display {
    .ant-card {
      padding: 20px 24px;
      border-radius: 8px;
      background-color: #EEFBEB;

      .ant-card-head {
        border-radius: 8px 8px 0 0;
        border: 0;
        padding: 0;
        min-height: 0;
        height: 22px;
        line-height: 22px;

        .ant-card-head-wrapper {
          .ant-card-head-title {
            padding: 0;
            color: #297E19;
          }

          .ant-card-extra {
            padding: 0px 4px;
            color: #297E19;
            font-size: 12px;
            cursor: pointer;
            border-radius: 4px;

            .dk-iconfont {
              margin-left: 4px;
              font-size: 12px;
              line-height: 22.5px;
            }

            &:hover {
              background: rgba(41, 126, 25, 0.08);
            }
          }
        }
      }

      .ant-card-body {
        padding: 0;
        // margin-top: 2px;
      }
    }

    .ant-card-bordered {
      border: 0;
    }

    .faq-items {
      display: 'flex';
      flex-direction: 'column';
      gap: 16px;

      .faq-item {
        cursor: pointer;
        border-radius: 6px;
        padding: 9px 12px;
        background-color: #FFF;
        margin-top: 12px;
        line-height: 20px;
        box-shadow: 0 1px 6px 0 rgba(1, 111, 15, .03);

        &:hover {
          background: #ffffff;
          box-shadow: 0 3px 12px 0 rgba(0, 111, 15, 0.1);
        }

        .ellipsis()

      }

      .item-2 {
        margin-top: 10px;
      }
    }
  }
}