.banner {
  width: 100%;
  height: 160px;
  padding: 0 32px;
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: flex-start;
  
  .banner-item {
    border-radius: 8px;
  }
  .banner_right {
    flex:1 0 200px;
    background-color: #EEFBEB;
    min-width: 200px;
  }
}

.banner_left {
  position: relative;
  width: 100%;
  display: flex;
  flex: 4 4 300px;
  background-color: #F0F4FF;
  transition: background-color .5s ease-in-out;

  img {
    height: 160px;
    margin: 0;
  }

  :global {
    .two-line-ellipsis {
      margin-top: 4px !important;
      line-height: 20px;
      .ellipsis2()
    }
    .slide {
      cursor: pointer;
      position: absolute;
      display: flex;
      width: 100%;
      height: 160px;
      z-index: 2;
      overflow: hidden;

      .slide-title {
        font-size: 20px;
        font-weight: 500;
        overflow: hidden;
        height: 28px;

        .splitting .word {
          opacity: 0;
          transition: opacity 0.5s cubic-bezier(0.9,0,.2,1);
          display: inline-block;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          max-width: 200px;

          @media screen {
            @media (min-width:800px) {
                max-width: 200px;
            }
            @media (min-width:900px) {
                max-width: 220px;
            }
            @media (min-width:1000px) {
                max-width: 300px;
            }
            @media (min-width:1200px) {
                max-width: none;
            }
          }
        }
        .char {
          display: inline-block;
          color: inherit;
          overflow: hidden;
          visibility: visible;
          opacity: 0;

          transition: transform 0.5s cubic-bezier(0.9,0,.2,1);
          transition-delay: calc( 0.01s * ( var(--char-index)) );
          transform: translateY(-100%);
        }
      }

      &.slide-prev-up {
        .word{
          opacity: 0;
          transition: opacity 0.5s cubic-bezier(0.9,0,.2,1);
        }
        .char {
          opacity: 1;
          transition: transform 0.5s cubic-bezier(0.9,0,.2,1);
          transition-delay: calc( 0.01s * ( var(--char-index)) );
          transform: translateY(-100%);
        }
        .slide-desc {
          opacity: 0;
          transition: transform .3s ease-in-out, opacity .5s ease-in-out;
          transform: translateY(-30%);
        }
        .slide-img {
          opacity: 0;
          transition: transform .5s ease-in-out, opacity .5s ease-in-out;
          transform: translateY(-0%) scale(1);
        }
      }
      &.slide-target-up {
        .word {
          opacity: 0;
          transition: none;
        }
        .char {
          opacity: 0;
          transition: none;
          transform: translateY(100%);
        }
        .slide-desc {
          opacity: 0;
          transition: none;
          transform: translateY(30%);
        }
        .slide-img {
          opacity: 0;
          transition: none;
          transform: translateY(0%) scale(1);
        }
      }
      &.slide-prev-down {
        .word {
          opacity: 0;
          transition: opacity 0.5s cubic-bezier(0.9,0,.2,1);
        }
        .char {
          opacity: 1;
          transition: transform 0.5s cubic-bezier(0.9,0,.2,1);
          transition-delay: calc( 0.01s * ( var(--char-index)) );
          transform: translateY(100%);
        }
        .slide-desc {
          opacity: 0;
          transition: transform .3s ease-in-out, opacity .5s ease-in-out;
          transform: translateY(30%);
        }
        .slide-img {
          opacity: 0;
          transform-origin: 50% 50%;
          transition: transform .5s ease-in-out, opacity .5s ease-in-out;
          transform: translateY(0%) scale(1);
        }
      }
      &.slide-target-down {
        .word {
          opacity: 0;
          transition: none;
        }
        .char {
          opacity: 0;
          transition: none;
          transform: translateY(-100%);
        }
        .slide-desc {
          opacity: 0;
          transition: none;
          transform: translateY(-30%);
        }
        .slide-img {
          opacity: 0;
          transition: none;
          transform: translateY(-0%) scale(1);
        }
      }

      .slide-desc-area {
        overflow: hidden;
      }
      .slide-desc {
        opacity: 0;
        margin: 10px 0;
        transform: translateY(0);
        transition: transform .3s ease-in-out, opacity .5s ease-in-out;
      }

      .slide-content {
        flex-grow: 1;
        margin: 26px 20px 18px 30px;
        min-width: 200px;
      }
      .slide-img-area {
        display: flex;
        align-items: center;
        cursor: pointer;
      }
      .slide-img-container {
        flex: 1;
        overflow: hidden;
      }
      .slide-img {
        opacity: 0;
        transform-origin: 50% 50%;
        transition: transform .5s ease-in-out, opacity .5s ease-in-out;
        transform: translateY(0%) scale(1);
      }

      .slide-clickto {
        display: none;
        position: absolute;
        bottom: 10px;
      }

      &[data-active='true'] {
        visibility: visible;
        z-index: 3;
        
        .slide-title {
          .word {
            opacity: 1;
            transition: opacity 0.5s cubic-bezier(0.9,0,.2,1);
          }
          .char { 
            opacity: 1;
            transition: transform 0.5s cubic-bezier(0.9,0,.2,1);
            transition-delay: calc( 0.01s * ( var(--char-index)) );
            transform: translateY(0%);
          }
        }

        .slide-desc {
          opacity: 1;
          transition: transform .3s ease-in-out, opacity .5s ease-in-out;
          transform: translateY(0);
          transition-delay: 0.5s;
        }
        .slide-img {
          opacity: 1;
          transform-origin: 50% 50%;
          transition: transform .5s ease-in-out, opacity .5s ease-in-out;
          transform: translateY(0) scale(1);
        }

        .slide-clickto {
          display: block;
        }
      }

    }

    .slide-to {
      display: flex;
      position: absolute;
      bottom: 18px;
      left: 30px;
      z-index: 3;

      .slide-to_item {
        display: inline-block;
        text-align: center;
        line-height: 22.5px;
        height: 22.5px;
      }
      .slide-to_prev, .slide-to_next {
        cursor: pointer;
      }
      // .slide-to_next{}
      .slide-to_indicator {
        margin: 0 10px;
        color: rgba(0, 0, 0, .3);
        font-size: 12px;
      }
      .dk-iconfont {
        font-size: 22px;
        line-height: 22.5px;
        color: rgba(0, 0, 0, .3);
        &:hover{
          color: rgba(0, 0, 0, .7)
        }
      }
    }
    .close {
      position: absolute;
      top: 8px;
      right: 8px;
      cursor: pointer;
      z-index:100;
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 100%;
      width: 17px;
      height: 17px;
      display: flex;
      align-items: center;
      justify-content: center;

      .dk-iconfont {
        font-size: 14px;
        color: #ffffff;
      }
    }
    .close:hover {
      background-color: rgba(0, 0, 0, 0.4)
    }
  }
}

:global {
  .slide-btn {
    position: relative;
    z-index: 100;
  }
}
