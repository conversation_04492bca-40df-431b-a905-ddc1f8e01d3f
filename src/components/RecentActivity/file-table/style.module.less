.cooper-list-content {
  .widthAdaptation();
  margin: -8px auto 0;

  .folder-tree {
    height: 100%;
    width: 100%;
    position: relative;
  }

  .tb-header {
    font-size: 14px;
    font-weight: 500;
    color: #656A72;
    position: sticky;
    top: 36px;
    background-color: #fff;
    padding-left: 29px;
    padding-right: 32px;
    z-index: 4;

    .tb-header-div {
      height: 36px !important;
      line-height: 36px !important;
      border-bottom: 1px solid @blueGray-11;
      position: relative;

      .file-name {
        position: relative;
        overflow: visible;
        display: flex;
        align-items: center;

        .file-resizer {
          position: absolute;
          right: 0;
          top: 0;
          transform: translateX(50%);
        }
      }
    }

  }

  .dc-tb-header {
    padding-left: 21px;
  }

  .tb-header>.tb-header-div,
  .tb-body>li {
    height: 46px;
    line-height: 46px;
    width: 100%;
    white-space: nowrap;
    display: flex;
    align-items: center;
    color: @blueGray-4;

    >span {
      display: inline-block;
    }

    >.file-name {
      max-width: initial;
      min-width: 250px;
      height: 100%;
      display: flex;
      align-items: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      flex: 3;
      cursor: pointer;
      padding-right: 12px;
    }


    .file-resizer {
      width: 20px;
      margin-right: 8px;
      margin-left: 52px;
      position: relative;
      text-align: center;

      .resizer-line {
        width: 1px;
        height: 50px;
        background: #076BFF;
        position: absolute;
        top: 0;
        left: 50%;
      }
    }

    .file-content-box {
      margin-right: 12px;
      width: 100%;
      height: 100%;
      cursor: pointer;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .file-owner-content-box {
      padding-left: 4px;
    }

    >.file-address {
      height: 100%;
      width: 300px;
      min-width: 200px;
      display: flex;
      align-items: center;
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      .file-content-box {
        padding: 0 2px;
        height: 22px;
        border-radius: 4px;
        line-height: 22px;
        cursor: pointer;
        width: auto;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }
      }
    }


    >.file-owner,
    >.file-time {
      height: 100%;
      width: 130px;
      min-width: 130px;
      display: flex;
      align-items: center;
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      padding-right: 12px;

      .file-content-box {
        cursor: default;
      }
    }

    .file-owner {
      width: 150px;
      min-width: 150px;
    }

    >.file-operate {
      width: 80px;
      height: 100%;
      display: flex;
      justify-content: right;
      align-items: center;
    }
  }

  @media screen and (max-width: 1050px) {

    .tb-header>.tb-header-div,
    .tb-body>li {

      >.file-name {
        min-width: 230px;
      }

      >.file-owner {
        min-width: 150px;
        overflow: hidden;
      }

      >.file-address {
        min-width: 130px;
      }
    }
  }

  .tb-body {
    padding-left: 27px;
    padding-right: 28px;
    padding-top: 8px;

    >li {
      padding-left: 4px;
      padding-right: 4px;

      .file-name {
        color: @blueGray-1;
      }
    }
  }

  .dc-tb-body {
    padding-left: 19px;
  }

  .tb-body-row:hover {
    border-radius: 4px;
    background: @blueGray-12;
  }
}


.file-time-sort {
  cursor: pointer;
}

.cooper-recent-table-loading {
  position: relative;
  margin-top: 16px;
  width: 100%;
  text-align: center;
  font-size: 14px;
  color: @blueGray-4;
  height: 100%;
}

.loading-tip {
  margin-top: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: @blueGray-4;

  .text {
    margin-left: 4px;
  }
}