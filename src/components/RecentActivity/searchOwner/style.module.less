.custom-filter-dropdown {
  width: 194px;
  li {
    min-width: 128px;
    justify-content: space-between;

    .icon-selected {
      display: none;
      color: #076BFF;
    }
  }
  .is-flow-me {
    margin-bottom: 2px !important;
  }
  :global {
    .ant-checkbox {
      transform: scale(0.875);
    }
    .ant-checkbox-wrapper {
      width: 100%;
      .ant-checkbox + span {
        padding-left: 6px;
        padding-right: 12px;
        width: 100%;
      }
    }
    .position-select-wrap-global {
      margin-top: 8px;
    }
  }
  .scroll-content {
    max-height: 192px;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .filter-resent {
    color: rgba(4, 127, 254, 0.4) !important;
    cursor: not-allowed !important;
  }

  .filter-action {
    color: #047ffe !important;
    cursor: pointer !important;
  }

  .filter-item {
    position: relative;
    display: flex;
    padding: 6px 12px !important;
    .shanchu-btn {
      position: absolute;
      right: 10px;
      top: calc(50% - 10px);
      width: 20px;
      height: 20px;
      color: #909499;
      border-radius: 4px;
      align-items: center;
      justify-content: center;
      display: none;
      .dk-icon-shanchu4 {
        font-size: 18px;
      }
      &:hover {
        background-color: #E0E2E3;
      }
    }
    .filter-item-text {
      display: flex;
      align-items: center;
      max-width: calc(100% - 8px);
      min-width: calc(100% - 8px);
      .filter-item-text-name {
        max-width: calc(100% - 34px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    &:hover {
      .shanchu-btn {
        display: flex;
      }
    }
  }
  .filter-tip {
    justify-content: flex-start;
    position: relative;
    color: @blueGray-6;
    font-size: 12px !important;
    cursor: default !important;
    margin-bottom: 0px !important;
    &:hover {
      background-color: #fff !important;
    }

    .icon-tip {
      margin-left: 2px;
      font-size: 12px;
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .filter-tip-hover-content {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.98);
    font-family: PingFangSC-Regular;
  }

  .filter-divider {
    margin: 0 0 2px;
  }



  .choose-li {
    .icon-selected {
      display: inline-block;
    }
  }

}

.file-handle {
  cursor: pointer;
  height: 26px;
  padding-left: 4px;
  line-height: 24px;
  background-color: #fff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  
  .icon-shaixuan2 {
    padding: 2px 2px 0;
    font-size: 14px;
  }

  &:hover {
    background-color: @blueGray-12;
  }
  .filter-owner-text {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 120px;
  }
}

.file-checked {
  display: flex;
  align-items: center;
  background-color: rgba(4, 127, 254, 0.1);
  color: #047FFE;

  .icon-shaixuan2 {
    padding: 2px 2px 0;
    font-size: 14px;
  }

  &:hover {
    background: rgba(4, 127, 254, 0.2);
    color: #047FFE;
  }
}
