import { intl } from 'di18n-react';
import { getUserNameFromCookie } from '@/utils/cooperutils';

function formatFileName(name, maxLength) {
  name = name || '';
  if (name.length <= maxLength) return name;

  // 超出用三个点
  return `${name.slice(0, maxLength)}...`;
}

export function formatAction(f, fileNameLength) {
  const userSelf = getUserNameFromCookie();
  let act = {};
  const dest = (f.parent_path || '').split('/').reverse()[0];
  const d = f.resource_type === 1 ? intl.t('文件夹') : intl.t('文件');

  // XXX: 首页动态用的是 operation，团队动态用的是 audit_type，后面需要统一处理
  const type = f.operation || f.audit_type;

  switch (type) {
    // 上传文件
    case 'upload_file': {
      const long = `${intl.t('上传文件至')} ${dest}`;
      act = {
        short: long,
        long,
      };

      break;
    }
    // 上传文件夹

    case 'upload_fold': {
      const long = `${intl.t('上传文件夹至')} ${dest}`;
      act = {
        short: long,
        long,
      };

      break;
    }
    // 新建文件夹

    case 'create_fold': {
      const long = intl.t('新建了文件夹');
      act = {
        short: long,
        long,
      };

      break;
    }
    // 下载文件

    case 'download_file': {
      const long = intl.t('下载文件');
      act = {
        short: long,
        long,
      };

      break;
    }
    // 导出协作文档

    case 'export_cooperation': {
      const long = intl.t('导出协作文档');
      act = {
        short: long,
        long,
      };

      break;
    }
    // 重命名

    case 'new_name': {
      const short = intl.t('将 {one} 重命名为 {two}', {
        one: formatFileName(f.old_name, fileNameLength),
        two: formatFileName(f.resource_name, fileNameLength),
      });
      const long = intl.t('将 {one} 重命名为 {two}', {
        one: f.old_name,
        two: f.resource_name,
      });
      act = {
        short,
        long,
      };

      break;
    }
    // 移动

    case 'move_files': {
      let long = intl.t('移动了 {one}', {
        one: d,
      });

      // share_type: 'Link' | 'Direct' | ''
      if (!f.share_type) {
        long = intl.t('移动了 {one} 至 {two}', {
          one: d,
          two: dest,
        });
      }

      act = {
        short: long,
        long,
      };

      break;
    }
    // 分享

    case 'create_share': {
      const long = intl.t('将{one}分享给{two}', {
        one: d,
        two:
          f.share_with === userSelf
            ? intl.t('您')
            : f.share_with_cn || f.share_with,
      });

      act = {
        short: long,
        long,
      };

      break;
    }
    // 恢复

    case 'restore': {
      const long = intl.t('恢复了文件');
      act = {
        short: long,
        long,
      };

      break;
    }
    // 创建协作文档

    case 'create_cooperation': {
      const long = intl.t('新建在线协作文档');
      act = {
        short: long,
        long,
      };

      break;
    }
    // 打开协作文档

    case 'open_cooperation': {
      const long = intl.t('打开了协作文档');
      act = {
        short: long,
        long,
      };

      break;
    }
    // 修改协作文档

    case 'modify_cooperation': {
      const long = intl.t('修改了协作文档');
      act = {
        short: long,
        long,
      };

      break;
    }
    // 修改协作文档

    case 'transfer_cooperation': {
      const long = intl.t('转让所有者给 {one}', {
        one: f.target_cn,
      });
      act = {
        short: long,
        long,
      };

      break;
    }
    // 邀请协作者

    case 'invite_cooperation': {
      const long = intl.t('邀请了{one}一起协作在线文档', {
        one:
          f.share_with !== userSelf
            ? f.share_with_cn || f.share_with
            : intl.t('您'),
      });

      act = {
        short: long,
        long,
      };

      break;
    }
    // 预览

    case 'preview': {
      const long = intl.t('打开预览');
      act = {
        short: long,
        long,
      };

      break;
    }
    // 预览

    case 'comment_cooperation': {
      const long = intl.t('评论在线协作文档');
      act = {
        short: long,
        long,
      };

      break;
    }

    default:
      act = {
        short: f.detail,
        long: f.detail,
      };

      break;
  }

  return act;
}
