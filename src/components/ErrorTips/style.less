.error-tips-wrap {
  width: 100%;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  // 弥补图片上边的空白导致的不上下居中
  margin-top: -60px;
  font-size: 14px;
  color: @blueGray-4;

  .title {
    margin-top: 25px;
    height: 20px;
    line-height: 20px;
  }

  .sub-div {
    height: 60px;
  }

  .desc {
    margin-top: 12px;
    height: 20px;
    line-height: 20px;
  }

  .icon {
    width: 210px;
  }
}

.error-tip-inPhone {
  width: 80%;
}