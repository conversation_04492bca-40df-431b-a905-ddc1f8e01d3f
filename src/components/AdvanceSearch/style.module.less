.search-modal-wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
  .search-page-wrap {
    min-width: 600px;
    max-width: 1072px !important;
    top: 0 !important;
    height: 82%;
    margin-top: 60px;
    max-height: 720px;
    min-height: 150px;
    padding-bottom: 0px;
  }
}
  
    
.search-page-wrap {
  // overflow: visible;
  .search-header-left{
    :global{
      .ant-input-lg {
        font-size: 18px;
      }
      .ant-input-clear-icon {
        svg {
          display: none;
        }
        &::after {
          content: '\6e05\9664';
          color: rgba(34, 42, 53, 0.5);
          font-size: 14px;
          transition: all 0.3s;
          z-index: 10;
        }
      }
      .ant-input-clear-icon-en {
        svg {
          display: none;
        }
        &::after {
          content: 'Clear' !important;
          color: rgba(34, 42, 53, 0.5);
          font-size: 14px;
          transition: all 0.3s;
          z-index: 10;
        }
      }
      .ant-input-suffix:hover {
        .ant-input-clear-icon {
          &::after {
            color: #222A35;
          }
        }
      }
    }
  }

  .search-header-left-en {
    :global{
      .ant-input-clear-icon {
        svg {
          display: none;
        }
        &::after {
          content: 'Clear';
          color: rgba(34, 42, 53, 0.5);
          font-size: 14px;
          transition: all 0.3s;
          z-index: 10;
        }
      }
    }
  }
  :global {
    .ant-modal-content {
      height: 100%;
      border-radius: 6px;
    }
    .ant-modal-header {
      padding: 0;
      border-radius: 6px 6px 0 0;
    }
    .ant-modal-close-x {
      display: none;
    }
    .ant-modal-body {
      padding: 0;
      height: calc(100% - 61px);
    }
  }
  .ant-input-clear-icon {
    svg {
      display: none;
    }
    &::after {
      font-family: "dk-iconfont" !important;
      content: '\e6fd';
      font-size: 24px;
      z-index: 10;
      font-weight: 300;
      line-height: 24px;
      width: 24px;
      height: 24px;
      border-radius: 4px;
      transition: all 0.3s;
      transform: scale(1.1);
    }
    &:hover {
      &::after {
        background: rgba(47, 52, 60, 0.08);
      }
    }
  }
  .search-modal-header {
    display: flex;
    height: 60px;
    align-items: center;
    box-sizing: border-box;
    padding: 12px 24px 10px 24px;
    line-height: 20px;
    .search-header-left {
      display: flex;
      align-items: center;
      flex: 1;
      font-size: 18px;
      :global {
        input.ant-input {
          line-height: 1.66667 !important;
        }
        .ant-input-affix-wrapper-lg {
          padding: 3.4px 11px;
        }
      }
    }
    .search-header-right {
      cursor: pointer;
      width: 48px;
      height: 24px;
      line-height: 24px;
      vertical-align: middle;
      text-align: right;
      color: #505050;
      border-left: 1px solid #eee;
      padding-left: 24px;
    }
  }
  .search-page-content{
    display: flex;
    height: calc(100% - 48px);
    background-color: @blueGray-13;
    padding-bottom: 6px;
  }
}


.search-page-side {
  width: 280px;
  height: 100%;
  border-right: 1px solid @border-color-split;
  min-width: 280px;
  p {
    width: fit-content;
    font-size: 14px;
    font-weight: 500;
    color: @primary-color;
    line-height: 20px;
    padding: 7px 16px;
    border-radius: 17px;
    border: 1px solid @primary-color;
    margin: 30px auto;
    cursor: pointer;
    transition: all 0.2s;
    &:hover {
      background-color: @primary-color;
      color: #fff;
    }
  }
}
.search-page-tab{
  height: 48px;
  padding: 10px 24px;
  border-bottom: 1px solid @blueGray-10;
 
  :global{
    .ant-tabs .ant-tabs-nav::before{
      border-bottom: 1px solid @blueGray-11;
    }
    .ant-tabs-tab-disabled{
      pointer-events: none;
    }
  }
}


@media (max-width: 750px) {
  .search-page-wrap {
    :global {
      .ant-modal-body {
        .search-page-filter{
          display: none;
        }
      }
    }
  }
}
