.search-page-filter {
  width: 300px;
  box-sizing: border-box;
  padding: 0;
  height: 100%;
  overflow: hidden;
  border-left: 1px solid #eee;
  &:hover{
    overflow: auto;
  }

  :global{
    .all-radio-style{
      .ant-radio-wrapper{
        margin-bottom: 8px;
      }
    }
  }

  :global{
    .no-input-style{
      margin-bottom: -14px;
    }
  }
  

  :global{
    .ant-radio-group{
      width: 100%;
    }
    .ant-radio-wrapper {
      display: flex;
      align-items: center;
      border-radius: 6px;
      color: @blueGray-1;
      width: 100%;
      >span:last-child{
        width: 100%;
      }

      &:last-child{
        margin-bottom: 0;
        
      }
    }
    .ant-radio-group {
      width: 100%;
    }
    .ant-radio{
      top: 0;
    }
  }
 
  .filter-item:first-child{
    padding-top: 16px;
  }
  .filter-item {
    padding: 24px 16px 0 16px;
    .tag-title-content{
      margin-bottom: 4px;
    }
    .filter-item-title{
      font-weight: @font-weight-medium;
    }
    &>p {
      font-weight: 400;
      .font-size(14px);
      color: @blueGray-1;
      font-weight: @font-weight-medium;
      margin-bottom: 8px;
    }
    .title-has-reset{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-right: 2px;

      .reset{
        color: @primary-color;
        .font-size(12px);
        cursor: pointer;
      }
    }
    &:last-child {
      border-bottom: none
    }

    .radio-item{
      width: 100%;
      display: flex;
      align-items: center;
      .font-size(14px);
      margin-bottom: 6px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .radio-item-with-input{
      :global{
        .ant-radio-wrapper{
          width: auto;
        }
      }
    }
  }
 
  .title-has-reset{
    &>p {
      font-weight: 400;
      .font-size(14px);
      color: @blueGray-1;
      font-weight: @font-weight-medium;
      margin-bottom: 8px;
    }
  }

  .knowledge-name {
    width: 160px;
    height: 22px;
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;
    display: flex;
    align-items: center;
    overflow: hidden;
    span {
      .ellipsis();
      max-width: 400px;
    }
    .shaixuanIcon {
      font-size: 12px;
      margin:0 3px;
      color: #212223;
    }
  }
  .moreDk {
    transition: all 0.2s;
    display: block;
    &.isOpen {
      transform: rotate(180deg);
    }
  }

}
.dk-switch-search-popver {
  :global {
    .ant-tooltip-content {
      transform: translate(50px, 0);
    }
  }
}

.hidden{
  display: none;
}
.noShow {
  visibility: hidden;
}

.attach{
  margin-top: 8px;
  color: @blueGray-8;
  .text{
    color: @blueGray-4;
  }
  :global{
    .checkbox-icon{
      width: 13px;
      height: 13px;
      position: relative;
      bottom: 1px;
    }

    .ant-checkbox{
      transform: scale(0.875);
    }
  }
  
}
