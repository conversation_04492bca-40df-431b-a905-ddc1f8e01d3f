.multi-choose-wrap{
  :global{
    .ant-checkbox-group{
      width: 100%;
      margin-bottom: 8px;
      .ant-checkbox-wrapper{
        flex: 1;
        overflow: hidden;
      }
      .ant-checkbox + span{
        display: inline-flex;
        overflow: hidden;
      }
    }
  }
  .options-content{
    
    .options-item{
      margin-top: 4px;
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      height: 26px;
      cursor: pointer;
      .ellipsis();

      .item-content-wrap{
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: baseline;
        .ellipsis();

        .image-content{
          width: 14px;
          height: 14px;
          margin-right: 4px;
          
        }
        .image{
          width: 18px;
          height: 18px;
          margin-right: 4px;
          position: relative;
          top: 4px;
          // position: relative;
          // top: 2px;
        }
        
        
        .text{
          flex: 1;
          font-size: 14px;
          line-height: 24px;
          color: @blueGray-1;
          .ellipsis();
        }
        
      }
      .close-options-icon{
        margin-left: 10px;
        border-radius: 4px;
        width: 24px;
        height: 24px;
        line-height: 24px;
        display: none;
        text-align: center;
        align-items: center;
        justify-content: center;

        .remove-icon{
          font-size: 10px;
          line-height: 10px;
          color:@blueGray-7;
        }
       
        &:hover {
          background-color:@blueGray-10;
          .remove-icon{
              color:@blueGray-3;
          }
        }
      }

      &:hover {
        .close-options-icon{
          display: flex;
        }
      }
      
    }

   
  }
}
