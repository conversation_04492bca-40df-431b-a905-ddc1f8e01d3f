
.item-wrap {
  margin-bottom: 2px;
  p:last-child {
    span:nth-child(2) {
      min-width: 96px;
      white-space: nowrap;
    }
    span:first-child {
      max-width: 80%;
    }
  }
}
.item-wrap:last-child{
  margin-bottom: 0px;
}
:global{
  #search-result-list{
    height: 100%;
    overflow-y: hidden;
  }
  .mac #search-result-list {
    &:hover{
      overflow-y: auto;
    }
  }
}

.search-page-result {
  padding: 2px 15px 0px 12px;
  position: relative;
  flex: 1;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  :global{
    .history-title{
      padding-top: 20px !important;
      .font-size(12px) !important;
    }
  }

  .search-page-total {
    padding-top: 20px;
    .font-size(12px);
    color: rgba(34, 42, 53, 0.5);
    position: relative;
    padding-left: 14px;
  }

  :global{ 
    .search-page-empty {
        .icon{
          width: 120px;
        }
        .title{
          margin-top: 16px;
        }
     
    }
  }
  
}




.search-empty {
  font-size: 13px;
  color: #aaa;
  text-align: center;
  margin-top: 12px;
}


@media (max-width: 1016px) {
  .search-item {
    &>*:nth-child(2) {
      display: none;
    }
  }
}

@media (max-width: 840px) {
  .search-item {
    &>*:last-child {
      display: none;
    }
  }
}

@media (max-width: 680px) {
  .search-item {
    &>*:nth-child(3) {
      display: none;
    }
  }
}

/** scrollbar for windows,mac and others**/
:global{
  .windows .search-page-result-os-flag {
    overflow: hidden;
    &:hover{
      overflow-y: auto;
    }
    .search-page-content-os-flag {
      overflow: hidden;
      &:hover{
        overflow-y: auto;
        width: -moz-calc( 100% + 17px ) !important;
        width: -o-calc( 100% + 17px ) !important;
        width: -webkit-calc( 100% + 17px ) !important;
        width: calc(100% + 17px) !important;
      }
    }
  }
  .mac, .linux, .generic{
    .search-page-result-os-flag {
      overflow-y: auto;
      overflow-y: overlay;
    }
    .search-page-content-os-flag{
      overflow-y: auto;
      overflow-y: overlay;
    }
  }
}


