.footer {
  padding: 24px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  &.onlyOne {
    justify-content: flex-end;
  }
  .memberBtn {
    // .btn {
    // display: inline-block;
    // border-radius: 4px;
    // height: auto;
    // font-size: 14px;
    // line-height: 36px;
    // border: none;
    // &::after {
    //   display: none;
    // }
    // }
    .cancel {
      // padding: 0 29px;
      margin-right: 12px;
      // line-height: 36px;
      // color: #444B4F;
      // background: #F6F6F6;
    } 
    // .confirm {
      // padding: 0 24px;
      // color: #ffffff;
      // background: @primary-color;
      // &[disabled] {
      //   opacity: 0.5;
      // }
    // }
  }
}
