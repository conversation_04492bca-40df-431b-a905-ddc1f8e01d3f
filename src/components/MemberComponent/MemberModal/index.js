import { intl } from 'di18n-react';
import { Modal } from 'antd';
import classBind from 'classnames/bind';
import { useState } from 'react';
import styles from './style.module.less';

const cx = classBind.bind(styles);

const MemberModal = ({
  customClass,
  title = intl.t('标题'),
  width = 640,
  cancel,
  children,
}) => {
  return (
    <Modal
      zIndex={1040}
      title={title}
      visible={true}
      closeIcon={<i className={cx('dk-iconfont', 'dk-icon-guanbi', 'close')} />}
      onCancel={cancel}
      footer={null}
      width={width}
      wrapClassName={cx(
        'knowledge_component_addMember',
        'memberModal',
        customClass,
      )}
    >
      {children}
    </Modal>
  );
};
export default MemberModal;
