import { intl } from 'di18n-react';
/*
 * @Author: <EMAIL> // 可DC联系
 * @Date: 2023-06-27 10:53:52
 * @LastEditTime: 2023-07-05 14:36:55
 * @Description: 简单描述
 * @FilePath: /knowledgeforge/src/components/MemberComponent/MemberEmail/index.js
 *
 */
import { useMemo } from 'react';
import { beautySub, highlight } from '@/utils';
import classBind from 'classnames/bind';
import { Tooltip } from 'antd';

const cx = classBind.bind();

const MemberEmail = ({
  groupUsers,
  keyword,
  memberEmail,
  customClass,
  maxlength,
}) => {
  const getSearchResultinGroup = useMemo(() => {
    if (!groupUsers) {
      return '';
    }
    return intl.t('包含:{slot0}', {
      slot0: groupUsers
        .map(({ cnName, email }) => {
          return `${cnName}(${email})`;
        })
        .join(','),
    });
  }, [groupUsers]);

  return (
    <Tooltip
      title={getSearchResultinGroup}
      placement="top"
      overlayClassName={cx({ hide: getSearchResultinGroup < maxlength })}
    >
      <span
        className={customClass}
      >
        {
          groupUsers ? highlight(beautySub(getSearchResultinGroup, maxlength)) : highlight(memberEmail)
        }
      </span>
    </Tooltip>
  );
};

export default MemberEmail;
