import { intl } from 'di18n-react';
import React, { useState } from 'react';
import classBind from 'classnames/bind';
import styles from './style.module.less';

const cx = classBind.bind(styles);

const MemberCircleList = ({ customClass, list, children, remove }) => {
  const [lastIndex, setLastIndex] = useState(10);

  const spread = () => {
    setLastIndex(list.length);
  };

  return (
    <div className={cx('member-list', customClass)}>
      {list.slice(0, lastIndex).map((item, index) => {
        return (
          <div
            className={cx('member')}
            key={index}>
            {item.icon ? (
              item.icon
            ) : (
              <img
                className={cx('avatar')}
                src={item.avatar} />
            )}
            <div className={cx('name')}>
              {item.name || item.cooperate_with_cn}
            </div>
            {children
              && React.cloneElement(children, { onClick: () => remove(item.id) })}
          </div>
        );
      })}

      {list.length > 10 && lastIndex < list.length && (
        <div
          className={cx('spread')}
          onClick={spread}>
          {intl.t('展开')}

          <i
            className={cx(
              'dk-iconfont',
              'dk-icon-taojian-xiaojiantou',
              'spread-icon',
            )}
          />
        </div>
      )}
    </div>
  );
};

export default MemberCircleList;
