.memberItem {
  display: flex;
  align-items: center;
  border-radius: 6px;
  padding: 6px 12px;
  margin-bottom: 2px;
  cursor: pointer;
  &:hover {
    background: #EFF0F2;
  }
  .avatar {
    border-radius: 50%;
    width: 36px;
    height: 36px;
    margin-right: 12px;
  }
  .info {
    flex:1;
    .existTag {
      margin-left: 4px;
      display: inline-block;
      width: 50px;
      height: 19px;
      background: #F5F5F6;
      border-radius: 2px;
      font-size: 12px;
      color: #666666;
      line-height: 19px;
      text-align: center;
      transform-origin: center center;
      transform: scale(0.833333333333334);
    }
  }
  .name-wrap {
    font-size: 14px;
    font-weight: 400;
    color: #111111;
    line-height: 20px;
    
    &.online {
      margin: 0;
    }
    .name {
      vertical-align: bottom;
      display: inline-block;
      .ellipsis();
      max-width: 390px;
    }
    .number {
      line-height: 20px;
      vertical-align: bottom;
    }
  }
  .from {
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    line-height: 16px;
  }
  .department {
    .ellipsis();
  }
  .nameLen {
    max-width: 80px !important;
  }
  &.name-flex {
    .info {
      width: 210px;
    }
    .name-wrap {
      display: flex;
      align-items: center;
    }
    .name {
      display: block;
      flex-shrink: 0;
    }
    .existTag {
      display: block;
      flex-shrink: 0;
      color: #0066FF;
      background: rgba(0,102,255,0.08);
      border-radius: 4px;
    }
  }
}