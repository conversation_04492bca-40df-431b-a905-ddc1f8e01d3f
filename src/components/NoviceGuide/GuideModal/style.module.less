.block-editor-guide-dialog {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0,0,0,0.4);
  z-index: 1000;

  .block-editor-guid-content {
    position: absolute;
  }

  .block-close {
    position: absolute;
    top: 10px;
    right: 16px;
    line-height: 1;
    cursor: pointer;

    &:hover {
      .close-icon {
        color: #047FFE;
      }
    }

    .close-icon {
      color: #505050;
      font-size: 12px;
    }
  }

  .guide-container {
    width: 540px;
    // height: 460px;
    padding: 24px;
    background: url("./background.png") no-repeat center;
    background-size: cover;
    display: flex;
    border-radius: 8px;
    flex-direction: column;

    .guide-title {
      text-align: center;
      height: 30px;
      font-size: 22px;
      font-weight: 500;
      color: #111111;
      line-height: 30px;
    }

    .guide-demo {
      width: 492px;
      height: 264px;
      overflow: hidden;
      border-radius: 4px;
      margin: 12px 0;

      .guide-img {
        width: 100%;
        object-fit: cover;
      }
    }

    .guide-summary {
      font-size: 14px;
      color: #333333;
      line-height: 22px;
    }

    .guide-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 16px;

      .guide-percent {
        font-size: 14px;
        color: #999999;
        line-height: 20px;
      }

      .guide-operation {
        .guide-previous {
          color: #505050;
          padding: 7px 16px;
          background: #F4F4F4;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }

        .guide-next {
          color: #FFFFFF;
          padding: 7px 16px;
          border: none;
          border-radius: 4px;
          margin-left: 10px;
          cursor: pointer;
        }
      }
    }
  }
}

.novice-guide {
  position: relative;

  .iconfont {
    color: #252525;
    font-size: 18px;
  }

  &:hover {
    .iconfont {
      color: #047FFE;
    }
  }
}

.novice-guide-tooltip {
  position: absolute;
  bottom: 100%;
  right: 0;
  transform: translateY(-10px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 222px;
  height: 36px;
  font-size: 14px;
  color: #FFFFFF;
  padding: 8px 14px;
  background: #047FFE;
  white-space: nowrap;
  border-radius: 4px;

  .iconfont {
    color: #FFFFFF;
    font-size: 18px;
    cursor: pointer;
  }

  &::after {
    content: '';
    position: absolute;
    top: 100%;
    right: 14px;
    width: 12px;
    height: 6px;
    background: #047FFE;
    clip-path: polygon(0 0, 50% 100%, 100% 0);
  }
}
