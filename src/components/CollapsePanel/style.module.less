.collapse-panel-container {
  .collapse-panel {
    position: relative;

    :global {
      .ant-collapse-item {
        .ant-collapse-header {
          position: sticky;
          top: 0;
          width: 200px;
          padding: 11px 8px;
          margin: 0 12px;
          font-size: 13px;
          color: rgba(34, 42, 53, 0.5);
          line-height: 18px;
          background-color: #FFFFFF;
          border-radius: 4px;
          .ellipsis();

          &:hover {
            background-color: #F4F4F4;
          }
        }

        .ant-collapse-content-box {
          padding: 0;

          .menu {
            padding: 0 12px;

            .option {
              width: 200px;
              padding: 6px 8px;
              border-radius: 4px;
              font-size: 14px;
              color: #222A35;
              line-height: 20px;
              cursor: pointer;
              .ellipsis();

              &.active {
                font-weight: 500;
                color: @primary-color;
                background: rgba(11, 131, 255, 0.08);
              }

              &:hover {
                background-color: #F4F4F4;
              }
            }
          }
        }
      }
    }
  }
}
