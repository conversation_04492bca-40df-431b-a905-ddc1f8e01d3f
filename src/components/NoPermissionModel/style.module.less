.no-perm-modal-content {
  overflow: hidden;
  .no-perm-modal-img {
    width: 100%;
  }
  .no-perm-modal-text {
    font-size: 14px;
    line-height: 22px;
    color: #4E555D;
    margin-bottom: 16px;
  }
  .no-perm-modal-link {
    font-size: 12px;
    a {
      color: #047FFE !important;
      &:hover {
        text-decoration: underline;
      }
    }
    &:last-child {
      margin-top: 6px;
    }
  }
}

.no-perm-modal {
  :global {
    .ant-modal-content {
      border-radius: 8px !important;
    }
    .ant-modal-confirm-body > .anticon + .ant-modal-confirm-title + .ant-modal-confirm-content {
      margin-left: 0;
    }
    .ant-modal-body {
      padding: 24px !important;
    }
    .ant-modal-confirm-content {
      margin-top: 0 !important;
    }
  }
}