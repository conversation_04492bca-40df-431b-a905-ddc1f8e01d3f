.ns-modal {
  text-align:  center;

  .ant-modal-content {
    border-radius: 8px;
  }
  
  p {
    font-size: 14px;
    line-height: 20px;
  }

  .icon-close {
    position: absolute;
    top: 27px;
    right: 27px;
  }

  .btn-link {
    display: block;
    margin: 8px auto 0;
    border: none;
    font-size: 14px;
    line-height: 20px;
    background-color: #fff;
    color: #06f;
    cursor: pointer;
  }

  img {
    height: 113px;
    margin-top: -4px;
  }

  .title {
    font-size: 18px;
    line-height: 25px;
    font-weight: 500;
    color: #2f343c;
    margin: 12px 0 8px;
    text-align: center;
  }

  .ant-btn {
    margin: 32px auto 8px;
    height: 32px;
    font-size: 14px;
    font-weight: 400;
    background-color: #06f;
    border-color: #06f;
  }

  .foot-tip {
    margin: 0 auto 8px !important;
    text-align: center !important;
    color: #828695;
    font-size: 12px;
    
    > a {
      color: #06f;
    }
  }

  .step-1 {
    margin-top: 52px;

    p {
      width: 285px;
      margin: 4px auto;
      text-align: left;
      color: #828695;
    }

    &.is-en p {
      width: 410px;
    }

    .title {
      color: #2f343c;
      text-align: center;
      margin-bottom: 16px;
    }

    .icon-back {
      display: block;
      height: 18px;
      width: 18px;
      background-image: url(../images/icon_jiantou_zuo.svg);
      background-size: 100% 100%;
      position: absolute;
      top: 24px;
      left: 24px;
      cursor: pointer;
    }

    .ant-btn {
      margin-top: 45px;
    }
  }
}
