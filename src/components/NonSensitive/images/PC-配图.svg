<?xml version="1.0" encoding="UTF-8"?>
<svg width="296px" height="229px" viewBox="0 0 296 229" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>PC-配图</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#5369FD" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="48.5593247%" y1="100%" x2="48.5593247%" y2="-46.8918411%" id="linearGradient-2">
            <stop stop-color="#92F9FF" offset="0%"></stop>
            <stop stop-color="#3F45FF" stop-opacity="0.143056163" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="48.5593247%" y1="100%" x2="48.5593247%" y2="-46.8918411%" id="linearGradient-3">
            <stop stop-color="#564DFF" offset="0%"></stop>
            <stop stop-color="#3F45FF" stop-opacity="0.143056163" offset="100%"></stop>
        </linearGradient>
        <filter x="-19.8%" y="-122.9%" width="139.8%" height="345.8%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="5" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.540677748   0 0 0 0 0.593375874   0 0 0 0 0.765313632  0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="3.03109912%" y1="51.093044%" x2="100%" y2="48.906956%" id="linearGradient-5">
            <stop stop-color="#4C91EB" offset="0%"></stop>
            <stop stop-color="#8C96E8" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="1.72350084%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#6CC1FF" offset="0%"></stop>
            <stop stop-color="#295BDF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="116.818561%" x2="50%" y2="83.561286%" id="linearGradient-7">
            <stop stop-color="#85B2FC" offset="0%"></stop>
            <stop stop-color="#8FBAFF" offset="100%"></stop>
        </linearGradient>
        <path d="M74.3975904,0 C115.486245,0 148.795181,4.02943725 148.795181,9 C148.795181,13.9705627 115.486245,18 74.3975904,18 C33.3089358,18 0,13.9705627 0,9 C0,4.02943725 33.3089358,0 74.3975904,0 Z M75.3669178,3.10783664 L74.3975904,3.10714286 C42.024711,3.10714286 15.781307,5.42566826 15.781307,8.28571429 C15.781307,11.1457603 42.024711,13.4642857 74.3975904,13.4642857 C106.77047,13.4642857 133.013874,11.1457603 133.013874,8.28571429 C133.013874,5.45426872 107.292713,3.15356752 75.3669178,3.10783664 Z" id="path-8"></path>
        <linearGradient x1="3.03109912%" y1="50.9143921%" x2="100%" y2="49.0856079%" id="linearGradient-10">
            <stop stop-color="#6292FF" offset="0%"></stop>
            <stop stop-color="#999DFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="92.5312199%" x2="50%" y2="-32.8341634%" id="linearGradient-11">
            <stop stop-color="#9EE4FC" offset="0%"></stop>
            <stop stop-color="#A1BAFF" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-12" cx="40.6706827" cy="4.5" rx="40.6706827" ry="4.5"></ellipse>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-14">
            <stop stop-color="#FFFFFF" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#536EFD" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="53.7815329%" y1="100%" x2="53.7815329%" y2="-12.7862923%" id="linearGradient-15">
            <stop stop-color="#78DBFB" offset="0%"></stop>
            <stop stop-color="#ABC2FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="97.1273644%" id="linearGradient-16">
            <stop stop-color="#6A7DF7" offset="0%"></stop>
            <stop stop-color="#39DEFD" stop-opacity="0.3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.8627534%" id="linearGradient-17">
            <stop stop-color="#436EFF" offset="0%"></stop>
            <stop stop-color="#8BBFFE" offset="100%"></stop>
        </linearGradient>
        <path d="M48.0600684,5.83333333 C56.6545103,5.83333333 73.9471711,14.373472 89.7009841,27.8213172 C90.1573896,31.1997767 90.3930723,34.6484093 90.3930723,38.1528487 C90.3930723,68.3498324 72.8938949,94.4032197 47.6155402,106.458775 C22.3350207,94.4032197 4.83584337,68.3498324 4.83584337,38.1528487 C4.83584337,34.9468571 5.03309258,31.7875717 5.41595878,28.6867855 C21.434047,14.7403106 39.2829816,5.83333333 48.0600684,5.83333333 Z M71.9247806,41.5884401 C70.8576485,40.3449516 68.9908041,40.1861986 67.7290256,41.231641 L67.7290256,41.231641 L44.0878095,60.8194913 L33.7029636,50.8135432 C32.5186153,49.6724066 30.6367801,49.6959415 29.4813406,50.8663403 L29.4813406,50.8663403 L29.0096649,51.3441227 C27.9109499,52.4570626 27.8500669,54.2268501 28.8697017,55.412664 L28.8697017,55.412664 L37.385847,65.3167623 L37.3546555,65.3434583 L43.6187651,72.6427877 L72.065487,46.1798785 C73.2458129,45.0818661 73.3488888,43.2478994 72.2990329,42.0245422 L72.2990329,42.0245422 Z" id="path-18"></path>
        <filter x="-10.4%" y="-14.4%" width="120.7%" height="132.5%" filterUnits="objectBoundingBox" id="filter-20">
            <feGaussianBlur stdDeviation="2" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="34.977702%" y1="22.6777939%" x2="73.7848564%" y2="73.2096473%" id="linearGradient-21">
            <stop stop-color="#6EBFFF" stop-opacity="0.1" offset="0%"></stop>
            <stop stop-color="#96BEF9" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="47.9947584%" y1="18.568852%" x2="50%" y2="69.7107799%" id="linearGradient-22">
            <stop stop-color="#3856FF" stop-opacity="0.6" offset="0%"></stop>
            <stop stop-color="#447CE6" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="52.1689859%" y1="30.792105%" x2="54.8814397%" y2="86.0278016%" id="linearGradient-23">
            <stop stop-color="#53FDFA" stop-opacity="0.0971099213" offset="0%"></stop>
            <stop stop-color="#53FDFA" offset="54.4013699%"></stop>
            <stop stop-color="#447CE6" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="16.2553666%" y1="15.8849824%" x2="48.298713%" y2="48.0050273%" id="linearGradient-24">
            <stop stop-color="#5369FD" stop-opacity="0.6" offset="0%"></stop>
            <stop stop-color="#447CE6" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="33.764388%" y1="14.086424%" x2="44.4905664%" y2="27.1269622%" id="linearGradient-25">
            <stop stop-color="#DBDFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-26">
            <stop stop-color="#FFFFFF" stop-opacity="0.2" offset="0%"></stop>
            <stop stop-color="#6FD8FA" offset="100%"></stop>
        </linearGradient>
        <filter id="filter-27">
            <feColorMatrix in="SourceGraphic" type="matrix" values="0 0 0 0 0.598933 0 0 0 0 0.961151 0 0 0 0 1.000000 0 0 0 1.000000 0"></feColorMatrix>
        </filter>
        <linearGradient x1="26.100914%" y1="15.0303911%" x2="69.0823843%" y2="84.7196779%" id="linearGradient-28">
            <stop stop-color="#FFCA5E" offset="0%"></stop>
            <stop stop-color="#FF8048" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-29">
            <stop stop-color="#85F4FF" offset="0%"></stop>
            <stop stop-color="#9CAEFE" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-30">
            <stop stop-color="#7E96FF" offset="0%"></stop>
            <stop stop-color="#BDC9FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-31">
            <stop stop-color="#6FD8FA" offset="0%"></stop>
            <stop stop-color="#BDC9FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-32">
            <stop stop-color="#6784FF" offset="0%"></stop>
            <stop stop-color="#BDC9FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-33">
            <stop stop-color="#85F4FF" offset="0%"></stop>
            <stop stop-color="#9CAEFE" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-34">
            <stop stop-color="#6784FF" offset="0%"></stop>
            <stop stop-color="#BDC9FF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.8627534%" id="linearGradient-35">
            <stop stop-color="#436EFF" offset="0%"></stop>
            <stop stop-color="#8BBFFE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.8627534%" id="linearGradient-36">
            <stop stop-color="#6787F0" offset="0%"></stop>
            <stop stop-color="#A4E1FB" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="V3.1.7公网低敏" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-738.000000, -80.000000)">
            <g id="PC-配图" transform="translate(738.000000, 80.000000)">
                <rect id="矩形" x="0" y="0" width="296" height="229" rx="1"></rect>
                <g id="编组" transform="translate(18.000000, 28.000000)">
                    <g id="配图备份-2">
                        <ellipse id="椭圆形" fill="#F8F9FB" cx="130" cy="160" rx="130" ry="20"></ellipse>
                        <path d="M73.9254112,144.391046 C63.714292,146.184169 57.6064257,148.460331 57.6064257,150.937324 C57.6064257,156.721753 90.9153615,161.410959 132.004016,161.410959 C173.092671,161.410959 206.401606,156.721753 206.401606,150.937324 C206.401606,148.444241 200.214133,146.154607 189.883117,144.356164" id="路径" stroke="url(#linearGradient-1)" stroke-width="0.5"></path>
                        <path d="M68.7223279,151.21994 C57.4220227,153.371688 50.6626506,156.103082 50.6626506,159.075474 C50.6626506,166.016789 87.5245395,171.643836 132.995984,171.643836 C178.467428,171.643836 215.329317,166.016789 215.329317,159.075474 C215.329317,156.083775 208.481846,153.336213 197.048856,151.178082" id="路径" stroke="url(#linearGradient-2)" stroke-width="0.4"></path>
                        <path d="M70.2710907,147.80433 C59.243082,149.716995 52.6465863,152.144901 52.6465863,154.787027 C52.6465863,160.957085 88.620237,165.958904 132.995984,165.958904 C177.371731,165.958904 213.345382,160.957085 213.345382,154.787027 C213.345382,152.127739 206.66291,149.685462 195.505413,147.767123" id="路径" stroke="url(#linearGradient-3)" stroke-width="0.4"></path>
                        <g id="大底" filter="url(#filter-4)" transform="translate(57.606426, 135.000000)">
                            <path d="M74.3975904,2 C106.211806,2 133.361979,4.95252217 143.997645,9.1054249 L148.795181,9.10559006 L148.795181,13.068323 C148.544794,19.1120673 115.331879,24 74.3975904,24 C33.3089358,24 0,19.0751322 0,13 L0.003,13.068 L0,13.068323 L0,9.10559006 L4.79753617,9.1054249 C15.4332019,4.95252217 42.5833749,2 74.3975904,2 Z" id="形状结合" fill="url(#linearGradient-5)"></path>
                            <ellipse id="椭圆形" fill="url(#linearGradient-6)" cx="74.8902896" cy="8" rx="60.1093114" ry="6"></ellipse>
                            <mask id="mask-9" fill="white">
                                <use xlink:href="#path-8"></use>
                            </mask>
                            <use id="形状结合" fill="url(#linearGradient-7)" xlink:href="#path-8"></use>
                        </g>
                        <g id="小底" transform="translate(91.333333, 133.000000)">
                            <path d="M41.3432468,1.00073684 C58.4181204,1.03817503 72.9350878,2.49859296 78.6922681,4.54329447 L81.3413655,4.54347826 L81.3413655,6.54347826 C81.1672724,9.56103984 63.0251631,12 40.6706827,12 C18.2088849,12 0,9.53756612 0,6.5 L0.002,6.543 L0,6.54347826 L0,4.54347826 L2.64878305,4.54340611 C8.48118859,2.47185156 23.3049053,1 40.6706827,1 Z" id="形状结合" fill="url(#linearGradient-10)"></path>
                            <mask id="mask-13" fill="white">
                                <use xlink:href="#path-12"></use>
                            </mask>
                            <use id="椭圆形" fill="url(#linearGradient-11)" xlink:href="#path-12"></use>
                        </g>
                        <path d="M93.2849461,130.018407 C86.4775334,130.964658 82.4056225,132.165813 82.4056225,133.47295 C82.4056225,136.525455 104.61158,139 132.004016,139 C159.396452,139 181.60241,136.525455 181.60241,133.47295 C181.60241,132.157323 177.477427,130.949058 170.590083,130" id="路径" stroke="url(#linearGradient-14)" stroke-width="0.5"></path>
                        <path d="M46.7376563,57 L215.372194,57 L172.674699,138 C166.908886,140.666667 153.021335,142 131.012048,142 C109.002761,142 95.7765228,140.666667 91.3333333,138 L46.7376563,57 Z" id="光束" fill="url(#linearGradient-15)" opacity="0.151390439"></path>
                        <g id="盾牌" transform="translate(83.397590, 29.000000)">
                            <path d="M48.1104418,0 C57.6763983,0 76.9237882,9.5903307 94.4584213,24.6918883 C94.966581,28.4858297 95.2289157,32.3586404 95.2289157,36.2941233 C95.2289157,70.2046083 75.751663,99.4619456 47.6158633,113.000119 C19.4772526,99.4619456 0,70.2046083 0,36.2941233 C0,32.6940228 0.219527508,29.1463677 0.645637074,25.6643996 C18.4739277,10.002644 38.3410164,0 48.1104418,0 Z M71.9247806,41.5884401 C70.8576485,40.3449516 68.9908041,40.1861986 67.7290256,41.231641 L67.7290256,41.231641 L44.0878095,60.8194913 L33.7029636,50.8135432 C32.5186153,49.6724066 30.6367801,49.6959415 29.4813406,50.8663403 L29.4813406,50.8663403 L29.0096649,51.3441227 C27.9109499,52.4570626 27.8500669,54.2268501 28.8697017,55.412664 L28.8697017,55.412664 L37.385847,65.3167623 L37.3546555,65.3434583 L43.6187651,72.6427877 L72.065487,46.1798785 C73.2458129,45.0818661 73.3488888,43.2478994 72.2990329,42.0245422 L72.2990329,42.0245422 Z" id="浅色环" fill="url(#linearGradient-16)" opacity="0.18236"></path>
                            <mask id="mask-19" fill="white">
                                <use xlink:href="#path-18"></use>
                            </mask>
                            <use id="形状结合" fill="url(#linearGradient-17)" xlink:href="#path-18"></use>
                            <path d="M71.9247806,41.5884401 L72.2990329,42.0245422 C73.3488888,43.2478994 73.2458129,45.0818661 72.065487,46.1798785 L43.6187651,72.6427877 L43.6187651,72.6427877 L37.3546555,65.3434583 L37.385847,65.3167623 L28.8697017,55.412664 C27.8500669,54.2268501 27.9109499,52.4570626 29.0096649,51.3441227 L29.4813406,50.8663403 C30.6367801,49.6959415 32.5186153,49.6724066 33.7029636,50.8135432 L44.0878095,60.8194913 L44.0878095,60.8194913 L67.7290256,41.231641 C68.9908041,40.1861986 70.8576485,40.3449516 71.9247806,41.5884401 Z" id="路径" fill="#FEFEFE" filter="url(#filter-20)" mask="url(#mask-19)"></path>
                            <rect id="矩形" fill-opacity="0.12" fill="#000000" mask="url(#mask-19)" x="-10.3743307" y="3.91666667" width="57.9887885" height="109.25"></rect>
                            <g id="彩色纹理" opacity="0.699999988" mask="url(#mask-19)">
                                <g transform="translate(-26.535141, -14.291667)" id="路径-2">
                                    <polygon fill-opacity="0.05" fill="#ADC4FF" points="15.210174 40.25 89.3597724 57.2582273 122.632028 23 81.7546854 0"></polygon>
                                    <polygon fill-opacity="0.18" fill="#D3B5FF" transform="translate(114.874404, 82.895833) scale(-1, 1) translate(-114.874404, -82.895833) " points="33.9178178 111.166667 124.088815 92.9583333 195.830991 69.9624872 107.11678 54.625"></polygon>
                                    <polygon fill-opacity="0.2" fill="#50BBFF" transform="translate(98.687634, 36.481384) rotate(15.000000) translate(-98.687634, -36.481384) " points="69.3750025 27.4023052 115.016441 19.5991878 128.000266 53.3635796"></polygon>
                                    <polygon fill-opacity="0.1" fill="#0037FF" points="0.178244227 87.2083333 77.001506 45.0979679 53.6281567 108.291667"></polygon>
                                    <polygon fill-opacity="0.2" fill="#B100FF" opacity="0.560732887" points="100.610661 20.125 33.7481577 18.2083333 16.1608099 43.7582035"></polygon>
                                    <polygon fill-opacity="0.2" fill="#00FFF8" opacity="0.400000006" points="23.0254016 46.4025059 16.8054563 64.6875 83.6559572 120.270833 41.1050992 23"></polygon>
                                    <path d="M61.0085255,115.291667 L76.5067728,123.39881 L92.0050201,109.791667 C84.807486,112.465581 79.3505678,114.132248 75.6342655,114.791667 C71.9179632,115.451085 67.0427166,115.617752 61.0085255,115.291667 Z" fill-opacity="0.2" fill="#00FFF8" opacity="0.400000006"></path>
                                </g>
                            </g>
                        </g>
                        <ellipse id="装饰原点" fill="#70D6F9" opacity="0.319999993" cx="220.289157" cy="60" rx="3.96787149" ry="4"></ellipse>
                        <g id="罩子" transform="translate(17.482741, 0.974418)">
                            <path d="M192.57665,160.025582 C204.621035,143.480817 211.734127,123.063825 211.734127,100.97291 C211.734127,45.7735253 167.322212,1.02558229 112.53734,1.02558229 C57.7524668,1.02558229 13.3405524,45.7735253 13.3405524,100.97291 C13.3405524,122.964778 20.3900023,143.2977 32.3363195,159.802808" id="路径" stroke="url(#linearGradient-22)" fill="url(#linearGradient-21)" opacity="0.5" transform="translate(112.537340, 80.525582) rotate(-360.000000) translate(-112.537340, -80.525582) "></path>
                            <path d="M192.57665,160.025582 C204.621035,143.480817 211.734127,123.063825 211.734127,100.97291 C211.734127,45.7735253 167.322212,1.02558229 112.53734,1.02558229" id="路径" stroke="url(#linearGradient-23)" opacity="0.300000012"></path>
                            <path d="M82.7688021,4.76627644 C44.2689255,12.1354175 13.6130684,41.9119378 4.67500387,80.1122831" id="路径" stroke="url(#linearGradient-24)" opacity="0.5" transform="translate(43.721903, 42.439280) rotate(-6.000000) translate(-43.721903, -42.439280) "></path>
                            <path d="M21.3178338,68.720529 C40.0071672,16.925462 96.7829348,-9.78035152 148.130567,9.07119036 C148.83562,9.33005195 149.535984,9.59615704 150.231606,9.86939198 C100.930951,-3.99279797 48.5894518,22.5789961 30.7907801,71.9057096 C12.3586367,122.990312 37.8590917,179.401141 87.8248666,199.02647 C85.3591469,198.332649 82.8991985,197.537558 80.4515839,196.638951 C29.1042208,177.786669 2.62924561,120.515867 21.3178338,68.720529 Z" id="形状结合" fill="url(#linearGradient-25)" opacity="0.5"></path>
                        </g>
                        <g id="环绕线条" transform="translate(16.935743, 54.000000)" stroke="url(#linearGradient-26)" stroke-width="2">
                            <path d="M23.9798636,30.2302398 C8.30992437,32.4506139 -1.06613238,35.2478477 -1.0722097,38.2712572 C-1.086392,45.3317104 50.0035129,50.9519506 113.04029,50.8244149 C176.077066,50.6968793 227.18997,44.869863 227.204158,37.8094098 C227.210269,34.7663612 217.723278,31.9908468 201.878206,29.8277408" id="路径" transform="translate(113.065974, 40.327142) rotate(-15.000000) translate(-113.065974, -40.327142) "></path>
                        </g>
                        <ellipse id="装饰原点" fill="#0266FF" opacity="0.200000003" cx="32.8072289" cy="111" rx="1.98393574" ry="2"></ellipse>
                        <g filter="url(#filter-27)" id="线条">
                            <g transform="translate(68.518072, 1.000000)">
                                <path d="M12.8955823,125 C13.404299,125 13.8235754,125.534517 13.8808765,126.22314 L13.8875502,126.384615 C13.5914708,137.461538 13.2608148,143 12.8955823,143 C12.5564379,143 12.2257819,137.461538 11.9036145,126.384615 C11.9036145,125.619913 12.3477336,125 12.8955823,125 Z" id="直线备份" fill="url(#linearGradient-29)" fill-rule="nonzero" opacity="0.400000006"></path>
                                <path d="M111.100402,110 C111.609118,110 112.028395,110.534517 112.085696,111.22314 L112.092369,111.384615 C111.79629,122.461538 111.465634,128 111.100402,128 C110.761257,128 110.430601,122.461538 110.108434,111.384615 C110.108434,110.619913 110.552553,110 111.100402,110 Z" id="直线" fill="url(#linearGradient-30)" fill-rule="nonzero" opacity="0.600000024"></path>
                                <path d="M0.991967871,0 C1.50068455,0 1.91996092,0.831471179 1.97726205,1.90266219 L1.98393574,2.15384615 C1.68785631,19.3846154 1.35720036,28 0.991967871,28 C0.652823421,28 0.322167464,19.3846154 0,2.15384615 C0,0.96430977 0.444119144,0 0.991967871,0 Z" id="直线备份-3" fill="url(#linearGradient-31)" fill-rule="nonzero" opacity="0.600000024"></path>
                                <path d="M108.124498,35 C108.633215,35 109.052491,35.5642126 109.109792,36.2910922 L109.116466,36.4615385 C108.820386,48.1538462 108.48973,54 108.124498,54 C107.785354,54 107.454698,48.1538462 107.13253,36.4615385 C107.13253,35.6543531 107.576649,35 108.124498,35 Z" id="直线备份-5" fill="url(#linearGradient-32)" fill-rule="nonzero" opacity="0.300000012"></path>
                                <path d="M11.9036145,85 C12.4123311,85 12.8316075,85.8314712 12.8889086,86.9026622 L12.8955823,87.1538462 C12.5995029,104.384615 12.2688469,113 11.9036145,113 C11.56447,113 11.2338141,104.384615 10.9116466,87.1538462 C10.9116466,85.9643098 11.3557657,85 11.9036145,85 Z" id="直线备份-4" fill="url(#linearGradient-33)" fill-rule="nonzero" opacity="0.100000001"></path>
                                <path d="M159.706827,96 C160.215544,96 160.63482,96.3563448 160.692121,96.8154267 L160.698795,96.9230769 C160.402716,104.307692 160.07206,108 159.706827,108 C159.367683,108 159.037027,104.307692 158.714859,96.9230769 C158.714859,96.4132756 159.158979,96 159.706827,96 Z" id="直线备份-2" fill="url(#linearGradient-34)" fill-rule="nonzero" opacity="0.300000012"></path>
                            </g>
                        </g>
                        <g id="锁" transform="translate(53.142570, 109.500000)">
                            <ellipse id="椭圆形" fill="url(#linearGradient-35)" cx="9.91967871" cy="10" rx="9.42369478" ry="9.5"></ellipse>
                            <path d="M13.2464714,9.19251006 L12.5125812,9.19251006 L12.5125812,7.52805293 C12.5125812,6.13341993 11.3499252,5 9.91960972,5 C8.48940922,5 7.32548829,6.13341993 7.32548829,7.52805293 L7.32548829,9.19251006 L6.59217303,9.19251006 C6.2384062,9.19251006 5.95180723,9.47201743 5.95180723,9.81693976 L5.95180723,14.3749895 C5.95180723,14.7203921 6.2384062,15 6.59217303,15 L13.2471499,15 C13.6009167,15 13.8875502,14.7203921 13.8875502,14.3750007 L13.8875502,9.81692859 C13.8875502,9.47200626 13.6009282,9.19248772 13.2464714,9.19248772 L13.2464714,9.19251006 Z M10.2791722,12.2149111 L10.2791722,13.2098617 C10.2791722,13.2544248 10.2413853,13.2922531 10.1955028,13.2922531 L9.64445263,13.2922531 C9.59788015,13.2922531 9.56020819,13.2544248 9.56020819,13.2098617 L9.56020819,12.2149111 C9.30132272,12.0855778 9.12218543,11.8274137 9.12218543,11.5246867 C9.12218543,11.0955525 9.47975855,10.7470339 9.91962122,10.7470339 C10.3596104,10.7470339 10.7165228,11.0955525 10.7165228,11.5246755 C10.717747,11.8274137 10.5380462,12.0855778 10.2791607,12.2148999 L10.2791722,12.2149111 Z M11.5170802,9.19251006 L8.32219678,9.19251006 L8.32219678,7.56897497 C8.32219678,6.71021521 9.03988439,6.0113686 9.92031118,6.0113686 C10.8013244,6.0113686 11.5170802,6.71021521 11.5170802,7.56897497 L11.5170802,9.19251006 L11.5170802,9.19251006 Z" id="形状" fill="#FEFEFE" fill-rule="nonzero"></path>
                        </g>
                        <g id="云盘" transform="translate(187.554217, 69.000000)">
                            <ellipse id="椭圆形" fill="url(#linearGradient-36)" cx="13.8875502" cy="14" rx="13.8875502" ry="14"></ellipse>
                            <path d="M13.8875502,7.30434783 C16.0028224,7.30434783 17.7923936,8.69468265 18.3870965,10.6087757 C20.3465994,11.3624242 21.7370351,13.2584497 21.7370351,15.4782608 C21.7370351,18.3597465 19.3941457,20.6956522 16.5040452,20.6956522 C15.5509041,20.6956522 14.6572804,20.4415878 13.8875852,19.9976783 C13.1190108,20.4412491 12.2248313,20.6956522 11.2710552,20.6956522 C8.38095471,20.6956522 6.0380653,18.3597465 6.0380653,15.4782608 C6.0380653,13.2582784 7.42871571,11.3621315 9.38885652,10.6085325 C9.98232994,8.69497546 11.7720553,7.30434783 13.8875502,7.30434783 Z M16.5040452,11.4782609 C14.2883014,11.4782609 12.4920862,13.2691219 12.4920862,15.4782608 L12.4966022,15.3517973 C12.810386,15.5721177 13.0153852,15.9361032 13.0153852,16.3478261 C13.0153852,17.0201727 12.468711,17.5652174 11.7943542,17.5652174 C11.1199974,17.5652174 10.5733233,17.0201727 10.5733233,16.3478261 C10.5733233,16.0581929 10.6747702,15.7921832 10.8441697,15.5831916 C9.84055638,14.7356412 9.19789019,13.4760479 9.17831929,12.0663094 C8.02742925,12.7675075 7.25909629,14.0334247 7.25909629,15.4782608 C7.25909629,17.6873998 9.0553115,19.4782608 11.2710552,19.4782608 C12.9346988,19.4782608 14.3618359,18.4686747 14.96975,17.0304819 C14.836867,16.8350237 14.7597152,16.6004079 14.7597152,16.3478261 C14.7597152,15.6754794 15.3063894,15.1304348 15.9807462,15.1304348 C16.655103,15.1304348 17.2017771,15.6754794 17.2017771,16.3478261 C17.2017771,17.0201727 16.655103,17.5652174 15.9807462,17.5652174 L16.0702567,17.5614664 C15.8055429,18.1668271 15.4289816,18.7123986 14.9666016,19.1722298 C15.4388515,19.3697942 15.9587436,19.4782608 16.5040452,19.4782608 C18.7197889,19.4782608 20.5160042,17.6873998 20.5160042,15.4782608 C20.5160042,13.2691219 18.7197889,11.4782609 16.5040452,11.4782609 Z M13.8875502,8.52173913 C11.9608165,8.52173913 10.3988903,10.0790096 10.3988903,12 C10.3988903,12.9391987 10.772248,13.7914551 11.3790581,14.4172807 C11.8712972,12.0442417 13.9787841,10.2608696 16.5040452,10.2608696 C16.6431482,10.2608696 16.7809835,10.2662809 16.9173509,10.2769039 C16.3175105,9.22820001 15.1852784,8.52173913 13.8875502,8.52173913 Z" id="形状结合" fill="#FFFFFF" fill-rule="nonzero"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>