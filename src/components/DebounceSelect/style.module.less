.switch-search-input {
  background-color: #F4f4f4 !important;
  height: 36px !important;
  line-height: 36px !important;
  border-radius: 4px !important;
  color: #222A35 !important;
  padding-top: 4px !important;
  font-size: 14px !important;
  padding-left: 12px !important;
  border: none !important;
  &:focus {
    border: none;
    outline: none;
    box-shadow: none;
  }
}
.switch-search-icon {
  position: absolute;
  z-index: 20;
  top: 22px;
  left: 20px;
  color: #bbb;
}

.search-popover-content {
  color: @blueGray-1;
  padding-top: 4px;
  ul {
    margin: 0 -8px;
    padding: 10px 8px;
    max-height: 420px;
    overflow: auto;
    li {
      padding: 8px 12px;
      line-height: 20px;
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .ellipsis();
      border-radius: 4px;

      &:hover {
        background: #F7F8F9;
      }
      &.active {
        background-color: rgba(11,131,255,.08);
        color: #0B83FF;
      }
    }
  }
  .check-msg{
    color: #FE0B19;
    margin: 12px;
    font-size: 12px;
  }
}

.check-icon{
  font-size: 16px;
  margin-right: 4px;
  color: @primary-color;
  margin-left: 4px;
}

.active{
  color: @primary-color;
  font-weight: @font-weight-medium;
  background: rgba(255, 255, 255, 0.9);
}