.file-content-wrap {
  display: flex;
  align-items: center;
  border-radius: 8px;
  padding: 8px;
  width: 100%;
  cursor: pointer;
  .ellipsis();

  &:hover {
    background-color: @item-hover-bg;
  }

  .file-icon {
    width: 28px;
    height: 28px;
  }

  .file-text-main {
    margin-left: 12px;
    width: calc(100% - 64px);

    .title {
      color: @text-color;
      font-weight: @font-weight-medium;
      .font-size(14px);
      display: flex;

      .title-text {
        .ellipsis();

      }

      .tag-list {
        margin-right: -12px;
        margin-left: -4px;

        :global {
          .cooper-tag {
            margin-right: 0 !important;
          }
        }
      }
    }

    .description {
      margin-top: 2px;
      color: @blueGray-4;
      .font-size(12px);
    }

    .addition {
      margin-top: 2px;
      color: @blueGray-6;
      display: flex;
      white-space: nowrap;
      .font-size(12px);

      .metis-name {
        // width: 35%;
        flex: 7;
        .ellipsis();
        color: @blueGray-6;
        display: flex;
        align-items: center;

        .metis-span {
          border-radius: @border-radius-sm;
          padding: 0 6px;
          display: inline-block;
          color: @blueGray-6;

          .dk-book {
            font-size: 12px;
            margin-right: 2px;
            color: @blueGray-6;
          }

          &:hover {
            background: @item-hover-bg;
          }
        }

      }

      .owner {
        // width: 25%;
        flex: 5;
        margin-left: 16px;
        .ellipsis();
      }

      .publish {
        // width: 40%;
        flex: 8;
        margin-left: 16px;
        .ellipsis();
      }

      div:first-child {
        margin-left: 0;
      }

      .metis-name {
        margin-left: -6px !important;
      }
    }
  }
}

.file-content-wrap-large {
  .file-text-main {
    width: calc(100% - 80px);
  }
}

.no-padding-left {
  padding-left: 0px;
  padding-right: 0px;

  &:hover {
    background: none;
  }
}

.icon-star {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  color: #f6c746;
}