.feedback {
  .content {
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    text-align: center;
    .icon{
      width: 20px;
      height: 20px;
      text-align: center;
    }

    &:hover{
      background:@blueGray-11;
      cursor: pointer;
      // .icon{
      //   content: url('/src/assets/icon/feedback-hover.png')
      // }
    }
  }

  :global{
    .dk-icon-lianxikefu {
      font-size: 18px;
    }
  }
}