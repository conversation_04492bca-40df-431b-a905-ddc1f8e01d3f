.newfeature-popup{
  :global{
    .customer-service-popover{
      z-index: 1000;
      .ant-popover-inner{
        border-radius: 8px !important;
        box-shadow: 0px 4px 20px 0px rgba(60, 68, 88, 0.16) !important;
      }
      .ant-popover-arrow{
        display: block !important;
      }
      .popup-main{
        width: 254px;
        max-height: 228px;
      }
      .ant-popover-inner-content{
        padding: 16px !important;
      }
      .customer-service-popup-newfeature{
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .customer-service-popup-newfeature-title{
          font-size: 14px;
          font-weight: normal;
          /* 带蓝中性色/BlueGray1 */
          color: #222A35;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          line-height: 22px;
          max-height: 44px;
        }
        .customer-service-popup-newfeature-img{
          border-radius: 6px;
          height: 124px;
          object-fit: cover;
          margin-top: 8px;
        }
        .customer-service-popup-newfeature-op{
          text-align: right;
          margin-top: 20px;
          width: 100%;
          .ant-btn:first-child{
            margin-right: 10px;
          }
        }
      }
    }
  }
}