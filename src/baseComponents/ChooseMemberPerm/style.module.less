.choose-member-perm{
  width: 100%;
  height: 36px;
  border: 1px solid @blueGray-9;
  border-radius: 4px;
  background: #FFFFFF;
  cursor: pointer;
  &:hover {
    border: 1px solid @primary-color;
  }

  .choosed-perm{
    color: @blueGray-1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 6px 12px;
    .choosed-perm-text{
      font-size: 14px;
    }
    .choosed-perm-icon{
      display: inline-block;
      transform: rotate(90deg);
      color: #333;
    }
    .icon-up{
      transform: rotate(270deg);
    }
  }
  
  .placeholder{
    color: @blueGray-7;
  }
}
.active{
  border: 1px solid @primary-color;
}

.member-perm-popover{
  :global{
    .ant-popover-inner{
      border-radius: 8px;
      width: 300px;
    }
    .ant-popover-inner-content{
      padding: 8px;
    }
  }
}
.member-perm-popover-small{
  :global{
    .ant-popover-inner{
      width: 160px;
    }
  }
}

.line-multi{
  height: 1px;
  background: @blueGray-11;
  margin: 8px 0 10px 0;
}