
import React from 'react';
import { intl } from 'di18n-react';
import classNames from 'classnames/bind';
import { Tooltip } from 'antd';
import styles from './style.module.less';


const cx = classNames.bind(styles);

const PermOptionItem = ({ optionItem, onOptionClickCallback, canApply }) => {
  // 多次点击：多选点击可以取消，单选点击不可以取消
  const onOptionClick = (optionItemCurr) => {
    let newOptions = { ...optionItemCurr };
    if (optionItemCurr.multiple) {
      newOptions.checked = !optionItemCurr.checked;
    } else {
      newOptions.checked = true;
    }
    onOptionClickCallback(newOptions);
  };


  return (
    <Tooltip
      title={canApply ? '' : intl.t('仅支持申请更多权限，不可降级申请')}
      overlayClassName={cx('dk-ant-tooltip__reset')}
    >
      <div
        key={optionItem.value}
        onClick={() => canApply && onOptionClick(optionItem)}
        className={cx('perm-option-item', {
          'disabled-perm-option-item': !canApply,
        })} >
        <div>
          <div className={cx('perm-option-label')}>{optionItem.label}</div>
          {
            optionItem.desc
            && <div className={cx('perm-option-desc')}>{optionItem.desc}</div>
          }
        </div>
        {optionItem.checked && <i
          className={cx('select-arrow-icon', {
            'select-arrow-disable__ico': !canApply,
          })}
        />}
      </div>

    </Tooltip>
  );
};

export default PermOptionItem;
