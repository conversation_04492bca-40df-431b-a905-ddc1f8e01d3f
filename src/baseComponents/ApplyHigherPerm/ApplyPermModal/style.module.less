.label-item {
  margin-bottom: 20px;
  .label-text{
    font-size: 14px;
    font-weight: 500;
    color: @blueGray-1;
    margin-bottom: 8px;

    .curr-perm{
      color: @primary-color;
      margin: 0 2px;
    }
    .require{
      color: #FF563B;
      margin-left: 2px;
    }
    
  }

  .apply-input{
    border-radius: 4px;
    height: 96px;
    padding: 6px 12px;
    font-size: 14px;
    border:1px solid @blueGray-9;
    color: @blueGray-1;

    &:hover {
      border: 1px solid @primary-color;
    }
    &:focus{
      border: 1px solid @primary-color;
    }
  }
 
}
.link{
  color: @primary-color;
  cursor: pointer;
    &:hover{
      color: @primary-color;
    }
}
.icon-close{
  cursor: pointer;
  margin-left: 20px;
  font-size: 20px;
  color: rgba(34, 42, 53, 0.5);
  position: relative;
  top: 2px;
}

.footer-wrap{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .explanation{
    font-size: 12px;
    color: @blueGray-6;

    &:hover {
      color: @primary-color;
    }
  }
  .btn-content{
    button:nth-child(1){
      margin-left: 12px !important;
    }
  }
}