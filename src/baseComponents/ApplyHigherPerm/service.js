import { get, post } from '@/utils/request/index';
import Cooper<PERSON><PERSON> from '@/utils/request/api/CooperApi';
import Dk<PERSON>pi from '@/utils/request/api/DkApi';

export function getCooperatorByName({ resourceId }, isDk) {
  if (!resourceId) return;
  return get(CooperApi.GET_COOPERATOR.replace(':resourceId', resourceId), {
    headers: {
      'X-App-Id': isDk ? 4: 2,
    },
  }).then((data) => {
    if (data) {
      return {
        id: data.id,
        username: data.cooperate_with,
        usernameCn: data.cooperate_with_cn,
        avatar: data.avatar,
        permission: data.permission,
        cooperatorType: data.cooperator_type,
        // A: 在职，I: 离职，其他：未知
        dimissed: data.hrStatus === 'I',
        totalCount: data.cooperator_total_count,
        resourceRole: data.resourceRole,
        spaceRole: data.spaceRole,
      };
    }
  });
}

export async function applyHigherPermFn(params, isDk) {
  const data = await post(CooperApi.APPLY_MEMBER_PERM.replace(':resourceId', params.resourceId), params, {
    headers: {
      'X-App-Id': isDk ? 4: 2,
    },
  });
  return data;
}


export async function applyOwnerPermCheck(params, isDk) {
  const res = await get(CooperApi.OWNER_CHECK_APPLY_PERM.replace(':id', params.resourceId), {
    headers: {
      'X-App-Id': isDk ? 4: 2,
    },
  });
  return res;
}


export async function applyOwnerPermFn(params, isDk) {
  const res = await post(CooperApi.APPLY_OWNER_APPLY_PERM.replace(':id', params.resourceId), params, {
    headers: {
      'X-App-Id': isDk ? 4: 2,
    },
  });
  return res;
}

// dk
export function getPagePermissionDk({ pageId }, isDk) {
  return get(DkApi.GET_PAGE_PERMISSION.replace(':pageId', pageId), {
    'X-App-Id': isDk ? 4: 2,
  });
}


