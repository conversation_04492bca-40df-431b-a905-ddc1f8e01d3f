import EditorTitle from './EditorTitle';
import TitleAttach from './TitleAttach';
import styles from './style.module.less';

function EditorTitleContent({ canEdit, docInfo, changeDocTitle, view }) {
  return (
    <div className={`${styles['editor-title-content']} editor-title-content`}>
      <EditorTitle
        view={view}
        editable={canEdit}
        docId={docInfo?.guid}
        title={docInfo?.pageName}
        changeDocTitle={changeDocTitle}
      />
      {
        !canEdit && (
          <TitleAttach
            lastPublishTime={docInfo.lastPublishTime}
            pageId={docInfo.pageId}
          />
        )
      }
    </div>
  );
}

export default EditorTitleContent;
