import React, { useRef, useEffect, useState, useMemo } from 'react'
import classNames from 'classnames/bind';
import { Input } from 'antd';
import { getDidocTitle, updateDidocTitle } from '@/service/knowledge/didoc';
import { debounce } from 'lodash-es';
import styles from '../style.module.less';

const cx = classNames.bind(styles);
const { TextArea } = Input;
const MAX_LEN = 100

const EditorTitle = ({
  title = '',
  editable = true,
  onChange,
  onEnter,
  maxLength,
  className,
  placeholder,
  docId,
  changeDocTitle,
  view,
}) => {
  const inputRef = useRef()
  const [titleValue, setTitleValue] = useState(title);

  useEffect(() => {
    setTitleValue(title);
    if (changeDocTitle) {
      changeDocTitle(title);
    }
  }, [title]);

  useEffect(() => {
    if (editable) {
      getDidocTitle({guid: docId}).then(res => {
        const { data } = res;
        if (data.title !== null) {
          setTitleValue(data.title);
          changeDocTitle(data.title);
        }
      })
    }
  }, [editable])

  // useEffect(() => {
  //   // 移除 @ts-ignore
  //   const dom = inputRef.current?.resizableTextArea?.textArea;
  //   let resizeObserver;
  //   if (dom) {
  //     // 宽窄屏幕切换时，文本框高度自适应
  //     resizeObserver = new ResizeObserver((entries) => {
  //       // 移除 @ts-ignore
  //       inputRef.current.resizableTextArea?.resizeTextarea();
  //     });
  //     resizeObserver.observe(dom);
  //   }

  //   return () => {
  //     resizeObserver?.disconnect();
  //   };
  // }, []);

  const debouncedRenameDoc = useMemo(() => {
    return debounce(updateDidocTitle, 500); // 500ms 防抖
  }, [updateDidocTitle]);

  const onTitleChange = (e) => {
    // 粘贴场景回车字符过滤
    let newTitle = e.target.value.replace(/\n+/g, ' ');
    setTitleValue(newTitle);
    onChange && onChange(newTitle);
    changeDocTitle(newTitle);
    debouncedRenameDoc({ title: newTitle.trim(), docId });
  }

  const onFocus = () => {
    // 进入知识库编辑态，页面标题聚焦时，取消编辑器选区来禁用顶部菜单
    view?.commands.selectDocStart()
  }

  const onKeyPress = (e) => {
    let keyCode = null;
    if (e.which) {
      keyCode = e.which;
    } else if (e.keyCode) {
      keyCode = e.keyCode;
    }
    if (keyCode === 13) {
      inputRef.current.blur();
      if (onEnter) onEnter();
      return false;
    }
    return true;
  }

  const onPressEnter = () => {
    view?.chain().insertContentAt(0, '<p></p>', {updateSelection: true}).focus().run()
  }

  if (!editable) {
    // 只读模式
    return (
      <p className={cx('editor-title', className)}>
        {titleValue}
      </p>
    );
  }

  // 编辑模式
  return (
    <TextArea
      ref={inputRef}
      className={cx('didoc2-editor-input', className)}
      onFocus={onFocus}
      onKeyPress={onKeyPress}
      onChange={onTitleChange}
      onPressEnter={onPressEnter}
      value={titleValue}
      rows={1}
      // style={style}
      autoSize
      bordered={false}
      placeholder={placeholder}
      maxLength={maxLength || MAX_LEN}
      // {...props}
    />
  )
}

export default EditorTitle;


