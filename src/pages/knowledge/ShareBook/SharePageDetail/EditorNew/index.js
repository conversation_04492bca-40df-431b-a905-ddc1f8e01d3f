import { getLocale } from 'di18n-react';
import USER_VIEW from '@/constants/userView';
import { inPhone, parseUrlSearch } from '@/utils';
import { getLocalData, setLocalData } from '@/utils/localStorage';
import { objMixin } from '@/utils/upload';
import classnames from 'classnames/bind';
import { useEffect, useMemo, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { connect } from 'react-redux';
// import UpdateInfo from '../UpdateInfo';
import { DidocEditor } from '@didi/didoc2-editor';
import EditorTitleContent from '@/pages/knowledge/EditorTitleContent';

import '@didi/didoc2-editor/dist/style.css';
import styles from './style.module.less';

const cls = classnames.bind(styles);

const isInPhone = inPhone();
window.msContainer = true;

function Editor(props) {
  const {
    changeView,
    docInfo,
    changeInitLoading,
    profile,
    writerVisible,
    toggleWriterVisible,
    changeEditReady,
    hasDownloadPerm,
    hasCommentPerm,
    shareId,
    pageId,
    isSnapshoot,
    // editorKey,
    // 外层传值覆盖
    // editorProps,
  } = props;

  const { guid } = docInfo;
  const [view, setView] = useState();
  const boxRef = useRef(null);

  // useEffect(async () => {
  //   const element = document.querySelector('#knowedge-body-content');

  //   if (window.ResizeObserver === undefined) {
  //     const module = await import('resize-observer-polyfill');
  //     window.ResizeObserver = module.default;
  //   }

  //   const resizeObserver = new window.ResizeObserver((entries) => {
  //     for (const entry of entries) {
  //       if (boxRef.current) {
  //         boxRef.current.style.width = `${entry.contentRect.width}px`;
  //       }
  //     }
  //   });

  //   if (element) {
  //     resizeObserver.observe(element);
  //   }

  //   return () => {
  //     if (element) {
  //       resizeObserver.unobserve(element);
  //     }
  //   };
  // }, []);

  useEffect(() => {
    changeInitLoading(false);
  }, [docInfo])

  // useEffect(() => {
  //   const updateWidth = () => {
  //     if (boxRef.current) {
  //       const newWidth = document.querySelector('#knowedge-body-content')?.clientWidth;
  //       boxRef.current.style.width = `${newWidth}px`;
  //     }
  //   };

  //   window.addEventListener('resize', updateWidth);
  //   updateWidth();

  //   return () => {
  //     window.removeEventListener('resize', updateWidth);
  //   };
  // }, []);

  // 设置大纲初始展示状态：如果用户设置过用设置过的状态，如果没有设置过默认展示，如果大纲长度为空【优先级最高】，则不展示。
  // defaultOpen：表示用户行为。大纲为空相关判断由编辑器内部控制
  const getDictionaryConfig = () => {
    let dictionaryConfig = {
      isShow: !isInPhone,
      onChange: (value) => {
        let currView = JSON.stringify(getLocalData('always:USER_VIEW')) !== '{}'
          ? getLocalData('always:USER_VIEW')
          : USER_VIEW;
        currView.Read_Page.Dictionary_State = value.open + 0 ?? true;
        setLocalData('always:USER_VIEW', currView);
      },
    };

    const view = JSON.stringify(getLocalData('always:USER_VIEW')) !== '{}'
      ? getLocalData('always:USER_VIEW')
      : USER_VIEW;
    const { Dictionary_State } = view?.Read_Page;
    if (Dictionary_State !== null) {
      dictionaryConfig = objMixin(dictionaryConfig, {
        defaultOpen: !!Dictionary_State,
      });
    }
    return dictionaryConfig;
  };

  const initAi = () => {
    const showAi = window.editorIframeABSwitch;

    if (showAi && !isInPhone) {
      try {
        window.CooperClientService?.create({
          resourceId: docInfo.pageId,
          version: docInfo.latestVersion,
        })
      } catch (error) {}
    }
  }

  const userInfo = {
    username: profile.username,
    username_zh: profile.username_zh,
    avatar: profile.avatar,
  };

  const editorShow = useMemo(() => {
    return guid && userInfo.username;
  }, [guid, profile]);

  const scrollToPoint = () => {
    let msgId = parseUrlSearch('msgId');
    if (msgId) {
      let operateDiv = document.getElementById(msgId);
      operateDiv?.scrollIntoView(true);
    }
  };

  const addDom = (id) => {
    let afterSlot = document.getElementById('didoc2-editor-didoc-editor-slot-after');
    let bottomContent = document.getElementById(id);
    if (bottomContent) {
      bottomContent.style = 'display:block';
      afterSlot?.appendChild(bottomContent);
    }
  };

  useEffect(() => {
    return () => {
      changeEditReady(false);
      view?.destroy && view.destroy();
    };
  }, []);

  useEffect(() => {
    try {
      window.CooperClientService?.destroy();
    } catch (error) {}
  }, [])

  const onEditorInit = (editor) => {
    changeInitLoading(false);
    changeView(editor);
    changeEditReady(true);
    initAi();
    setView(editor);

    // setTimeout(() => {
      if (document.querySelector('#didoc2-editor-didoc-editor-slot-before')) {
        ReactDOM.render(
          <EditorTitleContent
            canEdit={false}
            docInfo={docInfo}
          />,
          document.querySelector('#didoc2-editor-didoc-editor-slot-before'),
        );
      }
    // }, 0);

    if (document.querySelector('.page-detail-loading')) {
      document.querySelector('.page-detail-loading').style.zIndex = -1;
    }

    setTimeout(() => {
      let afterSlot = document.querySelector('.didoc2-editor-didoc-editor-slot-after');
      if (afterSlot) {
        addDom('dk-editor-bottom-wrap');
        addDom('dk-editor-bottom-wrap-gotoDkhome');
      }
    }, 0);

    /**
     * 发布也会重新加载预览页，会再次打点但时间不变
     */
    scrollToPoint();
  }

  const onChange = () => {
    // const { view } = editor;
    // console.log('change', editor);
  };

  return (
    <div
      ref={boxRef}
      id='knowledge_editor_box'
      className={cls('editor')}
    >
      <div className={cls('editor-container', { 'writer-hidden': !writerVisible })}>
        {
          editorShow && (
            <DidocEditor
              mode={'view'}
              id={guid}
              language={getLocale() === 'zh-CN' ? 'zh' : 'en'}
              isMobile={isInPhone}
              pageId={pageId}
              shareId={shareId}
              user={userInfo}
              draggable={!isInPhone}
              outlineConfig={getDictionaryConfig()}
              hasDownload={hasDownloadPerm}
              hasComment={hasCommentPerm && !isSnapshoot}
              onLoad={onEditorInit}
              onChange={onChange}
            />
          )
        }
      </div>
    </div>
  );
}

function mapStateToProps({ SharePage, CooperIndex }) {
  const { docInfo, editReady, editorKey } = SharePage;
  const { profile } = CooperIndex;
  return {
    docInfo,
    profile,
    editReady,
    editorKey,
  };
}

function mapDispatchToProps({ SharePage }) {
  const { changeInitLoading, changeEditReady, changeView } = SharePage;
  return {
    changeInitLoading,
    changeEditReady,
    changeView,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Editor);
