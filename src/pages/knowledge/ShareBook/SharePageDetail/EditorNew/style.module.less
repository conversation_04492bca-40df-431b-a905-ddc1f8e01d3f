.editor {
  // position: fixed;
  position: relative;
  z-index: 999;
  height: 100%;
  overflow: auto;
  // padding-bottom: 120px;

  .editor-container {
    height: 100%;

    &.writer-hidden {
      :global {
        .didoc-editor-app {
          .editor {
            #collab-editor-container {
              visibility: hidden;
              z-index: -1;
            }

            .editor-underline {
              border-bottom-style: none!important;
              pointer-events: none;
            }
          }
        }
      }
    }

    :global {
      .didoc-editor-app {
        .editor-menu-wrapper {
          display: none;
        }
        .editor {
          .editor-title {
            .editorTitle();
            -webkit-text-fill-color: #333333;
            padding-top: 24px !important;
            padding-bottom: 8px !important;
            // padding: 24px 60px 8px 60px !important;
          }

          .loading {
            display: none;
          }
        }

        .tab-intergation-float-container {
          top: 192px;
          user-select: none;
        }
        // .tab-intergation-float-drawer {
        //   user-select: none;
        // }
      }
    }
  }

  .closure-tip-box {
    position: absolute;
    top: 58px;
    left: 0;
    bottom: 0;
    width: 100px;
    padding: 0 16px;
    text-align: right;
    z-index: 1;

    &:hover {
      .closure-tip {
        display: inline-block;
      }
    }

    .closure-tip {
      display: none;
      padding: 4px;
      border-radius: 2px;
      cursor: pointer;
      color: #727272;

      &:hover {
        background-color: #F1F2F3;
        color: #222A35;
      }
    }
  }
}
