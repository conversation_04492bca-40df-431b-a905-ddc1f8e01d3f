import { getLocale, intl } from 'di18n-react';
import { useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { debounce } from 'lodash-es';
import ReactDOM from 'react-dom';
import { connect } from 'react-redux';
import classnames from 'classnames/bind';
import { Modal } from 'antd';
import produce from 'immer';
import {
  EditorName,
  EXIT_FULLSCREEN,
  FULLSCREEN,
  FULL_SCREEN,
  getIframeConfig,
  HISTORY_REVERT_SUCCESS,
  MenuConfig,
} from '@/constants/editor';
import { checkEmptyNew, formatDateTime, inPhone } from '@/utils';
import { DidocEditor } from '@didi/didoc2-editor';
import EditorTitleContent from '@/pages/knowledge/EditorTitleContent';

import '@didi/didoc2-editor/dist/style.css';
import styles from './style.module.less';

const cls = classnames.bind(styles);

const isInPhone = inPhone();

window.msContainer = true;
function Editor(props) {
  const containerRef = useRef();
  const viewRef = useRef();
  const {
    changeSaveStatus,
    changeView,
    changeCollaborators,
    docInfo,
    changeInitLoading,
    profile,
    changeTemplateVisible,
    writerVisible,
    changeEditReady,
    changeBlock,
    isBlock,
    alertTip,
    setAlertTip,
    changeDocTitle,
    changeTitle,
  } = props;
  const { guid, pageId } = docInfo;
  const userInfo = profile;
  const [view, setView] = useState();
  const alertTipRef = useRef(null);
  const boxRef = useRef(null);

  useEffect(() => {
    const updateWidth = () => {
      if (boxRef.current) {
        const newWidth = document.querySelector('#knowedge-body-content')?.clientWidth;
        boxRef.current.style.width = `${newWidth}px`;
      }
    };

    window.addEventListener('resize', updateWidth);
    updateWidth();

    return () => {
      window.removeEventListener('resize', updateWidth);
    };
  }, []);

  useEffect(() => {
    if (document.querySelector('#didoc2-editor-didoc-editor-slot-before')) {
      ReactDOM.render(
        <EditorTitleContent
          canEdit={ !isInPhone }
          docInfo={docInfo}
          changeDocTitle={changeDocTitle}
          view={view}
        />,
        document.querySelector('#didoc2-editor-didoc-editor-slot-before'),
      );
    }
  }, [docInfo.pageName])

  useEffect(async () => {
    const element = document.querySelector('#knowedge-body-content');

    if (window.ResizeObserver === undefined) {
      const module = await import('resize-observer-polyfill');
      window.ResizeObserver = module.default;
    }

    const resizeObserver = new window.ResizeObserver((entries) => {
      for (const entry of entries) {
        if (boxRef.current) {
          boxRef.current.style.width = `${entry.contentRect.width}px`;
        }
      }
    });

    if (element) {
      resizeObserver.observe(element);
    }

    return () => {
      if (element) {
        resizeObserver.unobserve(element);
      }
    };
  }, []);

  // 新编辑器协作者获取逻辑变更
  const updateCollaboratorsByView = (view) => {
    if (view && view?.storage?.collaborationCursor?.users) {
      changeCollaborators(view?.storage?.collaborationCursor?.users);
    }
  };

  const reloadWindow = () => {
    window.location.reload();
  };

  const detectTemplate = useCallback(debounce(() => {
    const ele = containerRef.current?.querySelector('.didoc2-editor-didoc-editor-content');
    if (!ele) return;

    const isEmpty = checkEmptyNew(ele);
    changeTemplateVisible(isEmpty);
  }, 300), []);

  const handleRevertHistory = (data = {}) => {
    const { users = {}, versionTime } = data;
    if (users.name === profile.username) {
      return;
    }
    const content = (
      <div>
        {`${decodeURI(decodeURI(users.nameCn))} ${users.name}`}
        {intl.t('已将⻚面恢复至')}
        <span className={cls('time')}>{formatDateTime(versionTime)}</span>
        {intl.t(
          '的历史记录，⻚面刷新后将展示最新内容。你仍可以在历史记录中找回修改内容。',
        )}
      </div>
    );

    Modal.info({
      className: cls('revert-history-modal'),
      width: 480,
      title: intl.t('当前页面已被还原'),
      icon: null,
      centered: true,
      content,
      okText: intl.t('刷新页面'),
      onOk() {
        window.location.reload();
      },
    });
  };

  useEffect(() => {
    const dom = view?.dom || view?.view?.dom;
    if (!dom) return;
    writerVisible
      ? dom.classList.add('collab-show')
      : dom.classList.remove('collab-show');
  }, [writerVisible]);

  const handleBeforeunload = (event) => {
    let res = isBlock ? 'unload' : undefined;
    if (res) {
      event.preventDefault();
      event.returnValue = res;
      return res;
    }
  };

  useEffect(() => {
    changeSaveStatus('');
    window.addEventListener('beforeunload', handleBeforeunload);
  }, []);

  const getMenuConfig = () => {
    const newMenu = produce(MenuConfig, (draft) => {
      const iframeConfig = getIframeConfig();
      const { children } = draft.menuBarContent.selectGroup;
      draft.menuBarContent.selectGroup.children = {
        ...children,
        ...iframeConfig,
      };
    });
    return newMenu;
  };

  const initAI = (editorView) => {
    const showAi = window.editorIframeABSwitch;

    if (showAi && !isInPhone) {
      try {
        window.CooperClientService?.create({
          resourceId: pageId,
          getVersion: editorView.getVersion,
        })
      } catch (error) {}
    }
  }

  const editorShow = useMemo(() => {
    return guid && userInfo.username;
  }, [guid, userInfo]);

  useEffect(() => {
    if (docInfo.pageName) {
      window.document.title = docInfo.pageName;
    }
  }, [docInfo.pageName]);

  // 编辑器埋点
  const handleOmega = (event) => {
    const insertCommentEle = document.querySelector(
      '#float-menu-inner-box .button-menu-icon:last-child',
    );
    const outlineEle = document.querySelector(
      '.tab-intergation-float-container .didoc-button-two-chinese-chars:first-child',
    );
    const commentListEle = document.querySelector(
      '.tab-intergation-float-container .didoc-button-two-chinese-chars:last-child',
    );
    const markdownEle = document.querySelector('.markdown-tip');
    const map = {
      ep_dkpc_pagedetail_edit_comment_ck: insertCommentEle,
      ep_dkpc_pagedetail_commentlist_ck: commentListEle,
      ep_dkpc_pagedetail_markdown_ck: markdownEle,
      ep_dkpc_pagedetail_outlinelist_ck: outlineEle,
    };

    Object.entries(map).forEach(([key, dom]) => {
      if (dom && dom.contains(event.target)) {
        window.__OmegaEvent(key);
      }
    });
  };

  useEffect(() => {
    if (document.body) { // fix:报白屏错误removeEventListener不存在
      document.body.addEventListener('click', handleOmega, { capture: true });
      return () => {
        document.body?.removeEventListener('click', handleOmega, {
          capture: true,
        });
      };
    }
  }, []);

  useEffect(() => {
    return () => {
      // 页面切换时卸载编辑器
      const TenonEditor = window[EditorName];
      if (TenonEditor && typeof TenonEditor.unmount === 'function') { // 监控到白屏报错做修复
        TenonEditor.unmount();
      }
      viewRef.current?.unmount && viewRef.current.unmount();
    };
  }, []);

  useEffect(() => {
    return () => {
      try {
        window.CooperClientService?.destroy();
      } catch (error) {}
    }
  }, [])

  useEffect(() => {
    const ele = alertTipRef.current;
    if (view && alertTip && ele) {
      ele.classList.add('animate');
      ele.onanimationend = () => {
        ele.classList.remove('animate');
        setAlertTip(null);
      };
    }
  }, [view, alertTip]);

  const onEditorInit = (editor) => {
    if (document.querySelector('#didoc2-editor-didoc-editor-slot-before')) {
      ReactDOM.render(
        <EditorTitleContent
          canEdit={ !isInPhone }
          docInfo={docInfo}
          changeDocTitle={changeDocTitle}
          view={editor}
        />,
        document.querySelector('#didoc2-editor-didoc-editor-slot-before'),
      );
    }

    changeInitLoading(false);
    changeView(editor);
    updateCollaboratorsByView(editor);
    changeEditReady(true);
    setView(editor);
    viewRef.current = editor;
    detectTemplate();
    initAI(editor);
  }

  const onChange = (value) => {
    const { editor, action } = value;
    console.log(editor, action, 'change');
    if (!editor) return;
    const { state, view } = editor;
    if (action && action.type === 'PluginCursor') {
      updateCollaboratorsByView(view);
    }

    if (action && action.type === 'historyRevert') {
      handleRevertHistory(action.value);
    }
    if (action && action.type === 'fileChange') {
      changeBlock(action.value?.length > 0);
    }

    detectTemplate();
    changeSaveStatus('sending');
  }

  // 监听编辑器状态变化
  const onStateless = (status, data) => {
    changeSaveStatus(status);
    if (status === HISTORY_REVERT_SUCCESS && data?.title) {
      changeTitle({
        name: data.title,
        pageId,
      });
    }

    if (status === FULLSCREEN || status === EXIT_FULLSCREEN) {
      document.querySelector('#knowledge_editor_box')?.classList.toggle('full-screen');
    }
  }

  return (
    <div
      ref={boxRef}
      id='knowledge_editor_box'
      className={cls('editor')}
    >
      <div
        ref={containerRef}
        className={cls('editor-container', { 'writer-hidden': !writerVisible })}
      >
        {
          view && alertTip && ReactDOM.createPortal(
            <div
              ref={alertTipRef}
              id='editor-alert-tip'
              className={cls('editor-alert-tip')}
            >
              <span className={cls('editor-alert-tip-icon')} />
              <span className={cls('editor-alert-tip-text')}>{alertTip?.text}</span>
              {alertTip?.refresh && (
                <span
                  className={cls('editor-alert-link')}
                  onClick={reloadWindow}
                >
                  {intl.t('点击刷新')}
                </span>
              )}
            </div>,
            containerRef.current.querySelector('#didoc-editor-slot-menu'),
          )
        }
        {
          editorShow && (
            <DidocEditor
              mode={isInPhone ? 'view' : 'edit'}
              id={guid}
              language={getLocale() === 'zh-CN' ? 'zh' : 'en'}
              isMobile={isInPhone}
              pageId={pageId}
              user={userInfo}
              hasComment={!isInPhone}
              outlineConfig={{
                isShow: !isInPhone,
                onChange: () => {},
              }}
              onLoad={onEditorInit}
              onChange={onChange}
              onStateless={onStateless}
            />
          )
        }
      </div>
    </div>
  );
}

function mapStateToProps({ pageDetail, CooperIndex }) {
  const { docInfo, writerVisible, editorKey, isBlock, alertTip } = pageDetail;
  const { profile } = CooperIndex;
  return {
    docInfo,
    profile,
    writerVisible,
    editorKey,
    isBlock,
    alertTip,
  };
}

function mapDispatchToProps({ pageDetail, template }) {
  const {
    changeView,
    changeSaveStatus,
    changeCollaborators,
    changeInitLoading,
    changeEditReady,
    toggleWriterVisible,
    changeBlock,
    setAlertTip,
    changeDocTitle,
    changeTitle,
  } = pageDetail;

  const { changeTemplateVisible } = template;
  return {
    changeView,
    changeSaveStatus,
    changeCollaborators,
    changeInitLoading,
    changeTemplateVisible,
    changeEditReady,
    toggleWriterVisible,
    changeBlock,
    setAlertTip,
    changeDocTitle,
    changeTitle,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Editor);
