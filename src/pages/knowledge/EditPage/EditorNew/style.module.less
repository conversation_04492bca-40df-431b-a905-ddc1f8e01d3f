.editor {
  position: relative;
  height: 100%;
  overflow: hidden;
  z-index: 999;

  .editor-container {
    height: 100%;

    &.writer-hidden {
      :global {
        .didoc-editor-app {
          .editor {
            #collab-editor-container {
              visibility: hidden;
              z-index: -1;
            }

            .editor-underline {
              border-bottom-style: none!important;
              pointer-events: none;
            }
          }
        }
      }
    }

    :global {
      .didoc-editor-app {
        .editor-menu-wrapper {
          position: sticky;
          top: 0;
          z-index: 200;

          .editor-menu {
            justify-content: center;
            box-shadow: none;
          }
        }
        .editor {
          .editor-title {
            .editorTitle();
            // -webkit-text-fill-color: #333333;
            padding-top: 30px !important;
            padding-bottom: 7px !important;
          }

          .loading {
            display: none;
          }
          .didoc-editor-container {
            padding-bottom: 120px;
          }
        }
        // 重置编辑器Float按钮位置
        .tab-intergation-float-container {
          top: 112px !important;
          user-select: none;
        }
        // .tab-intergation-float-drawer {
        //   user-select: none;
        // }
      }
    }
  }

  .closure-tip-box {
    position: absolute;
    top: 100px;
    left: 0;
    bottom: 0;
    width: 100px;
    padding: 0 16px;
    text-align: right;
    z-index: 1;

    &:hover {
      .closure-tip {
        display: inline-block;
      }
    }

    .closure-tip {
      display: none;
      padding: 4px;
      border-radius: 2px;
      cursor: pointer;
      color: #727272;

      &:hover {
        background-color: #F1F2F3;
        color: #222A35;
      }
    }
  }
}

.time {
  font-weight: bold;
}

.revert-history-modal {
  padding-bottom: 0;

  :global {
    .ant-modal-content {
      border-radius: 6px;

      .ant-modal-body {
        padding: 32px 28px 24px;

        .ant-modal-confirm-title {
          font-size: 24px;
        }

        .ant-modal-confirm-content {
          margin-top: 10px;
          font-size: 16px;
          color: #444B4F;
          line-height: 24px;
        }

        .ant-modal-confirm-btns {
          margin-top: 36px;

          .ant-btn {
            width: 96px;
            height: 44px;
            background: #F6F6F6;
            border-radius: 4px;
            border: none;
            font-size: 16px;
            color: #444B4F;

            &::after {
              display: none;
            }

            &.ant-btn-primary {
              width: 112px;
              margin-left: 16px;
              color: #FFFFFF;
              background: @primary-color;
            }
          }
        }
      }
    }
  }
}

:global {
  .markdown-modal {
    top: 160px!important;
  }
}

.editor-alert-tip {
  position: absolute;
  top: 46px;
  display: flex;
  align-items: center;
  width: 100%;
  z-index: 1;
  opacity: 0;
  pointer-events: none;

  &:global(.animate) {
    animation: FlashAlert 2.5s linear 0s 2 alternate;

    &:hover {
      animation-play-state: paused;
    }
  }

  .editor-alert-tip-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url("../../../../assets/icon/notification_warn.png") no-repeat center/contain;
    margin-right: 4px;
  }

  .editor-alert-tip-text {
    line-height: 24px;
    color: #7A7F86;
    font-size: 16px;
  }

  .editor-alert-link {
    color: @primary-color;
    cursor: pointer;
    margin-left: 4px;
  }
}

@keyframes FlashAlert {
  20% {
    opacity: 1;
    pointer-events: all;
  }
  100% {
    opacity: 1;
    pointer-events: all;
  }
}
