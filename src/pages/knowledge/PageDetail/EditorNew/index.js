import { getLocale } from 'di18n-react';
import USER_VIEW from '@/constants/userView';
import usePermission from '@/hooks/usePermission';
import { inPhone, parseUrlSearch } from '@/utils';
import { getLocalData, setLocalData } from '@/utils/localStorage';
import { objMixin } from '@/utils/upload';
import classnames from 'classnames/bind';
import { useEffect, useMemo, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { connect } from 'react-redux';
import { DidocEditor } from '@didi/didoc2-editor';
import EditorTitleContent from '@/pages/knowledge/EditorTitleContent';
import { DOCUMENT_PUBLISH_SUCCESS } from '@/constants/editor';

import '@didi/didoc2-editor/dist/style.css';
import styles from './style.module.less';

const cls = classnames.bind(styles);

const isInPhone = inPhone();
window.msContainer = true;

function Editor(props) {
  const {
    changeView,
    docInfo,
    changeInitLoading,
    profile,
    writerVisible,
    changeEditReady,
    changeTitle,
  } = props;

  const [view, setView] = useState();
  const boxRef = useRef(null);

  useEffect(() => {
    if (document.querySelector('#didoc2-editor-didoc-editor-slot-before')) {
      ReactDOM.render(
        <EditorTitleContent
          canEdit={false}
          docInfo={docInfo}
        />,
        document.querySelector('#didoc2-editor-didoc-editor-slot-before'),
      );
    }
  }, [docInfo.pageName]);

  useEffect(() => {
    const updateWidth = () => {
      if (boxRef.current) {
        const newWidth = document.querySelector('#knowedge-body-content')?.clientWidth;
        boxRef.current.style.width = `${newWidth}px`;
      }
    };

    window.addEventListener('resize', updateWidth);
    updateWidth();

    return () => {
      window.removeEventListener('resize', updateWidth);
    };
  }, []);

  useEffect(async () => {
    const element = document.querySelector('#knowedge-body-content');

    if (window.ResizeObserver === undefined) {
      const module = await import('resize-observer-polyfill');
      window.ResizeObserver = module.default;
    }

    const resizeObserver = new window.ResizeObserver((entries) => {
      for (const entry of entries) {
        if (boxRef.current) {
          boxRef.current.style.width = `${entry.contentRect.width}px`;
        }
      }
    });

    if (element) {
      resizeObserver.observe(element);
    }

    return () => {
      if (element) {
        resizeObserver.unobserve(element);
      }
    };
  }, []);

  const {
    guid,
    permission,
    pageId,
  } = docInfo;

  const { checkOperationPermission } = usePermission();

  // 设置大纲初始展示状态：如果用户设置过用设置过的状态，如果没有设置过默认展示，如果大纲长度为空【优先级最高】，则不展示。
  // defaultOpen：表示用户行为。大纲为空相关判断由编辑器内部控制
  const getDictionaryConfig = () => {
    let dictionaryConfig = {
      isShow: !isInPhone,
      onChange: (value) => {
        let currView = JSON.stringify(getLocalData('always:USER_VIEW')) !== '{}'
          ? getLocalData('always:USER_VIEW')
          : USER_VIEW;
        
        currView.Read_Page.Dictionary_State = value ?? true;
        setLocalData('always:USER_VIEW', currView);
      },
    };

    const view = JSON.stringify(getLocalData('always:USER_VIEW')) !== '{}'
      ? getLocalData('always:USER_VIEW')
      : USER_VIEW;
    const { Dictionary_State } = view?.Read_Page;
    if (Dictionary_State !== null) {
      dictionaryConfig = objMixin(dictionaryConfig, {
        defaultOpen: !!Dictionary_State,
      });
    }
    return dictionaryConfig;
  };

  const initAi = () => {
    const showAi = window.editorIframeABSwitch;

    if (showAi && !isInPhone) {
      try {
        window.CooperClientService?.create({
          resourceId: docInfo.pageId,
          version: docInfo.latestVersion,
        })
      } catch (error) {}
    }
  }

  const userInfo = {
    username: profile.username,
    username_zh: profile.username_zh,
    avatar: profile.avatar,
  };

  const editorShow = useMemo(() => {
    return guid && userInfo.username;
  }, [guid, profile]);

  const scrollToPoint = () => {
    let msgId = parseUrlSearch('msgId');
    if (msgId) {
      let operateDiv = document.getElementById(msgId);
      operateDiv?.scrollIntoView(true);
    }
  };

  const scrollToMention = () => {
    let mentionId = parseUrlSearch('mentionId');
    if (mentionId) {
      let operateDiv = document.querySelector(
        `span[data-mention-id='${mentionId}']`,
      );
      operateDiv?.scrollIntoView(true);
    }
  };

  useEffect(() => {
    const dom = view?.view?.dom;
    if (!dom) return;
    writerVisible
      ? dom.classList.add('collab-show')
      : dom.classList.remove('collab-show');
  }, [writerVisible]);

  useEffect(() => {
    return () => {
      // 页面切换时卸载编辑器
      // const TenonEditor = window[EditorName];
      // if (TenonEditor && typeof TenonEditor.unmount === 'function') { // 监控到白屏报错做修复
      //   TenonEditor.unmount();
      // }
      changeEditReady(false);
      view?.destroy && view.destroy();
    };
  }, []);

  useEffect(() => {
    try {
      window.CooperClientService?.destroy();
    } catch (error) {}
  }, [])

  const onStateless = (status, data) => {
    if (status === DOCUMENT_PUBLISH_SUCCESS && data?.title) {
      changeTitle({
        name: data.title,
        pageId,
      })
    }
  }

  const onEditorInit = (editor) => {
    if (document.querySelector('#didoc2-editor-didoc-editor-slot-before')) {
      ReactDOM.render(
        <EditorTitleContent
          canEdit={false}
          docInfo={docInfo}
          title={docInfo.pageName}
        />,
        document.querySelector('#didoc2-editor-didoc-editor-slot-before'),
      );
    }
    changeEditReady(true);

    if (document.querySelector('.page-detail-loading')) {
      document.querySelector('.page-detail-loading').style.zIndex = -1;
    }

    changeInitLoading(false);
    changeView(editor);
    initAi();
    setView(editor);

    setTimeout(() => {
      let afterSlot = document.querySelector('#didoc2-editor-didoc-editor-slot-after');
      if (afterSlot) {
        let bottomContent = document.getElementById('dk-editor-bottom-wrap');
        if (bottomContent) {
          bottomContent.style = 'display:block';
          afterSlot?.appendChild(bottomContent);
        }
      }
    }, 0);

    // 发布也会重新加载预览页，会再次打点但时间不变
    scrollToPoint();
    scrollToMention();
  }

  return (
    <div
      ref={boxRef}
      id='knowledge_editor_box'
      className={cls('editor')}
    >
      <div className={cls('editor-container', { 'writer-hidden': !writerVisible })}>
        {
          editorShow && (
            <DidocEditor
              mode={'view'}
              id={guid}
              language={getLocale() === 'zh-CN' ? 'zh' : 'en'}
              isMobile={isInPhone}
              draggable={!isInPhone}
              pageId={pageId}
              user={userInfo}
              hasComment={!isInPhone}
              outlineConfig={getDictionaryConfig()}
              hasDownload={checkOperationPermission('MANAGE_PAGE_CONTEXT', permission)}
              onLoad={onEditorInit}
              onStateless={onStateless}
            />
          )
        }
      </div>
    </div>
  );
}

function mapStateToProps({ pageDetail, CooperIndex }) {
  const { docInfo } = pageDetail;
  const { profile } = CooperIndex;

  return {
    docInfo,
    profile,
  };
}

function mapDispatchToProps({ pageDetail }) {
  const {
    changeView,
    changeInitLoading,
    changeEditReady,
    toggleWriterVisible,
    changeTitle,
  } = pageDetail;
  return {
    toggleWriterVisible,
    changeView,
    changeInitLoading,
    changeEditReady,
    changeTitle,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Editor);
