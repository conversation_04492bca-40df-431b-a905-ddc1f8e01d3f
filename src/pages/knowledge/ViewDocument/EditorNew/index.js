import { getLocale, intl } from 'di18n-react';
import { inPhone } from '@/utils';
import { Tooltip } from 'antd';
import classnames from 'classnames/bind';
import { useEffect, useMemo, useRef, useState } from 'react';
import { connect } from 'react-redux';
import { DidocEditor } from '@didi/didoc2-editor';
import { EditorName } from '@/constants/editor';

import '@didi/didoc2-editor/dist/style.css';
import styles from './style.module.less';

const cls = classnames.bind(styles);
const isInPhone = inPhone();

function Editor(props) {
  const {
    changeView,
    docInfo,
    changeInitLoading,
    profile,
    shareId,
    writerVisible,
    toggleWriterVisible,
    changeEditReady,
  } = props;

  const [view, setView] = useState();
  const boxRef = useRef(null);

  // useEffect(() => {
  //   const updateWidth = () => {
  //     if (boxRef.current) {
  //       const newWidth = document.querySelector('#knowedge-body-content')?.clientWidth;
  //       boxRef.current.style.width = `${newWidth}px`;
  //     }
  //   };

  //   window.addEventListener('resize', updateWidth);
  //   updateWidth();

  //   return () => {
  //     window.removeEventListener('resize', updateWidth);
  //   };
  // }, []);

  // useEffect(async () => {
  //   const element = document.querySelector('#knowedge-body-content');


  //   if (window.ResizeObserver === undefined) {
  //     const module = await import('resize-observer-polyfill');
  //     window.ResizeObserver = module.default;
  //   }

  //   const resizeObserver = new window.ResizeObserver((entries) => {
  //     for (const entry of entries) {
  //       if (boxRef.current) {
  //         boxRef.current.style.width = `${entry.contentRect.width}px`;
  //       }
  //     }
  //   });

  //   if (element) {
  //     resizeObserver.observe(element);
  //   }

  //   return () => {
  //     if (element) {
  //       resizeObserver.unobserve(element);
  //     }
  //   };
  // }, []);

  const {
    guid,
    pageId,
  } = docInfo;

  const userInfo = {
    username: profile.username,
    username_zh: profile.username_zh,
    avatar: profile.avatar,
  };

  const editorShow = useMemo(() => {
    return guid && userInfo.username;
  }, [guid, profile]);

  useEffect(() => {
    return () => {
      // 页面切换时卸载编辑器
      const TenonEditor = window[EditorName];
      if (TenonEditor && typeof TenonEditor.unmount === 'function') { // 监控到白屏报错做修复
        TenonEditor.unmount();
      }
      changeEditReady(false);
      view?.destroy && view.destroy();
    };
  }, []);

  useEffect(() => {
    try {
      window.CooperClientService?.destroy();
    } catch (error) {}
  }, [])

  const onEditorInit = (editor) => {
    changeInitLoading(false);
    changeView(editor);
    changeEditReady(true);
    setView(editor);
    if (document.querySelector('.page-detail-loading')) {
      document.querySelector('.page-detail-loading').style.zIndex = -1;
    }
  }

  return (
    <div
      ref={boxRef}
      id='knowledge_editor_box'
      className={cls('editor')}
    >
      {writerVisible && !isInPhone && (
        <div className={cls('closure-tip-box')}>
          <Tooltip title={intl.t('隐藏编写者')}>
            <i
              className={cls('dk-iconfont', 'dk-icon-shanchu1', 'closure-tip')}
              onClick={toggleWriterVisible}
            />
          </Tooltip>
        </div>
      )}

      <div className={cls('editor-container', { 'writer-hidden': !writerVisible })}>
        {
          editorShow && (
            <DidocEditor
              mode={'view'}
              id={guid}
              language={getLocale() === 'zh-CN' ? 'zh' : 'en'}
              isMobile={isInPhone}
              draggable={false}
              pageId={pageId}
              shareId={shareId}
              user={userInfo}
              hasComment={false}
              hasUtils={false}
              outlineConfig={{
                isShow: false,
                defaultOpen: false,
                onChange: () => {},
              }}
              hasDownload={false}
              hasMenu={false}
              onLoad={onEditorInit}
            />
          )
        }
      </div>
    </div>
  );
}

function mapStateToProps({ pageDetail, CooperIndex }) {
  const { docInfo, writerVisible, editorKey, editReady } = pageDetail;
  const { profile } = CooperIndex;

  return {
    docInfo,
    profile,
    writerVisible,
    editorKey,
    editReady,
  };
}

function mapDispatchToProps({ pageDetail }) {
  const {
    changeView,
    changeInitLoading,
    changeEditReady,
    toggleWriterVisible,
  } = pageDetail;
  return {
    toggleWriterVisible,
    changeView,
    changeInitLoading,
    changeEditReady,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Editor);
