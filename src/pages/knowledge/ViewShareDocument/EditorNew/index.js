import { getLocale } from 'di18n-react';
import { inPhone } from '@/utils';
import classnames from 'classnames/bind';
import { useEffect, useMemo, useRef, useState } from 'react';
import { connect } from 'react-redux';
import { DidocEditor } from '@didi/didoc2-editor';

import '@didi/didoc2-editor/dist/style.css';
import styles from './style.module.less';

const cls = classnames.bind(styles);

const isInPhone = inPhone();
window.msContainer = true;

function Editor(props) {
  const {
    changeView,
    docInfo,
    changeInitLoading,
    profile,
    writerVisible,
    changeEditReady,
    shareId,
    pageId,
  } = props;

  const { guid } = docInfo;
  const [view, setView] = useState();
  const boxRef = useRef(null);

  // useEffect(async () => {
  //   const element = document.querySelector('#knowedge-body-content');

  //   if (window.ResizeObserver === undefined) {
  //     const module = await import('resize-observer-polyfill');
  //     window.ResizeObserver = module.default;
  //   }

  //   const resizeObserver = new window.ResizeObserver((entries) => {
  //     for (const entry of entries) {
  //       if (boxRef.current) {
  //         boxRef.current.style.width = `${entry.contentRect.width}px`;
  //       }
  //     }
  //   });

  //   if (element) {
  //     resizeObserver.observe(element);
  //   }

  //   return () => {
  //     if (element) {
  //       resizeObserver.unobserve(element);
  //     }
  //   };
  // }, []);

  useEffect(() => {
    changeInitLoading(false);
  }, [docInfo])

  // useEffect(() => {
  //   const updateWidth = () => {
  //     if (boxRef.current) {
  //       const newWidth = document.querySelector('#knowedge-body-content')?.clientWidth;
  //       boxRef.current.style.width = `${newWidth}px`;
  //     }
  //   };

  //   window.addEventListener('resize', updateWidth);
  //   updateWidth();

  //   return () => {
  //     window.removeEventListener('resize', updateWidth);
  //   };
  // }, []);

  const userInfo = {
    username: profile.username,
    username_zh: profile.username_zh,
    avatar: profile.avatar,
  };

  const editorShow = useMemo(() => {
    return guid && userInfo.username;
  }, [guid, profile]);

  useEffect(() => {
    return () => {
      changeEditReady(false);
      view?.destroy && view.destroy();
    };
  }, []);

  useEffect(() => {
    try {
      window.CooperClientService?.destroy();
    } catch (error) {}
  }, [])

  const onEditorInit = (editor) => {
    changeInitLoading(false);
    changeView(editor);
    changeEditReady(true);

    if (document.querySelector('.page-detail-loading')) {
      document.querySelector('.page-detail-loading').style.zIndex = -1;
    }
    // initAi();
    setView(editor);
  }

  return (
    <div
      id='knowledge_editor_box'
      className={cls('editor')}
      ref={boxRef}
    >
      <div className={cls('editor-container', { 'writer-hidden': !writerVisible })}>
        {
          editorShow && (
            <DidocEditor
              mode={'view'}
              id={guid}
              language={getLocale() === 'zh-CN' ? 'zh' : 'en'}
              isMobile={isInPhone}
              draggable={false}
              pageId={pageId}
              shareId={shareId}
              user={userInfo}
              hasComment={false}
              hasUtils={false}
              hasMenu={false}
              outlineConfig={{
                isShow: false,
                defaultOpen: false,
                onChange: () => {},
              }}
              hasDownload={false}
              onLoad={onEditorInit}
            />
          )
        }
      </div>
    </div>
  );
}

function mapStateToProps({ SharePage, CooperIndex }) {
  const { docInfo, editReady, editorKey } = SharePage;
  const { profile } = CooperIndex;
  return {
    docInfo,
    profile,
    editReady,
    editorKey,
  };
}

function mapDispatchToProps({ SharePage }) {
  const { changeInitLoading, changeEditReady, changeView } = SharePage;
  return {
    changeInitLoading,
    changeEditReady,
    changeView,
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Editor);
