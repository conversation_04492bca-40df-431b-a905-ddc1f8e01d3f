import SortIcon from '@/components/CooperFilesList/SortIcon/index';
import SpinRender from '@/components/SpinRender';
import FileEllipsis from '@/components/common/FileEllipsis';
import { deepCopy, formatRecentTime, isDC } from '@/utils';
import { noopenerOpen, setImgUrl } from '@/utils/cooperutils';
import { get, put } from '@/utils/request/cooper';
import { Checkbox, Tooltip, message } from 'antd';
import cls from 'classnames';
import dayjs from 'dayjs';
import { intl } from 'di18n-react';
import { useCallback, useEffect, useState } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
import InfiniteScroll from 'react-infinite-scroller';
import { useDispatch, useSelector } from 'react-redux';
import ShareEditor from './ShareEditor';
import CancelShare from '@/components/CooperOperation/CancelShare';
import ErrorTips from '@/components/ErrorTips';
import NoMore from '@/components/NoMore';
import OperateMenu from '@/components/OperateMenu';
import goToPath from '@/components/RecentActivity/goToPath';
import FileTableSkeleton from '@/components/SkeletonPage/common/FileTableSkeleton';
import BatchButton from '@/components/common/BatchButton';
import ImageEnlarger from '@/components/common/ImageEnlarger';
import { PAGE_SIZE } from '@/constants';
import { SHARE_FILE_SORT } from '@/constants/cooper';
import { Coop, DiDoc, FlowChart, POFile, SHARE_FROM_ME, DK_PAGE } from '@/constants/cooperConstants';
import pathUtil from '@/utils/path';
import SearchFileType from '@/components/RecentActivity/searchType'
import CooperApi from '@/utils/request/api/CooperApi';
import { useNavigate } from 'react-router-dom';
import GetHtml from '@/utils/DOMPurify';
import './index.less';
import { SHARE_LIST_OPT } from '@/components/OperateMenu/constant';

function formatTime(time) {
  return dayjs(time).format(intl.t('YYYY年MM月DD日 HH时mm分ss秒'));
}

function addTime(expire) {
  if (expire === '1') return intl.t('永久有效');
  if (expire === '2') {
    return dayjs()
      .add(1, 'M')
      .format(intl.t('YYYY年MM月DD日 HH时mm分ss秒'));
  }
  if (expire === '3') {
    return dayjs()
      .add(1, 'w')
      .format(intl.t('YYYY年MM月DD日 HH时mm分ss秒'));
  }
  if (expire === '4') {
    return dayjs()
      .add(1, 'd')
      .format(intl.t('YYYY年MM月DD日 HH时mm分ss秒'));
  }
  return '';
}

const config = {
  moveOperate: false,
  copyOperate: false,
  renameOperate: false,
  deleteOperate: false,
  quickAccessOperate: false,
  duplicateOperate: false,
  starOperate: true,
  multiSelectOperate: false,
  restoreOperate: false,
  permanentlyDeleteOperate: false,
  cancelShareOperate: true,
  downloadOperate: false,
  shareOperate: true,
  useConfig: true,
};

const filterCof = [
  {
    text: () => intl.t('全部类型'),
    value: 'all',
  },
  {
    text: () => intl.t('文档'),
    value: 'coo_doc',
  },
  {
    text: () => intl.t('表格'),
    value: 'coo_sheet',
  },
  {
    text: () => intl.t('流程图'),
    value: 'flow',
  },
  {
    text: () => intl.t('思维导图'),
    value: 'mind',
  },
  {
    text: () => intl.t('幻灯片'),
    value: 'coo_ppt',
  },
  {
    text: () => intl.t('文件'),
    value: 'coo_file',
  },
  {
    text: () => intl.t('文件夹'),
    value: 'coo_dir',
  },
]

const filterInfo = {
  initFilter: ['all'],
  initTenantFilter: ['all'],
  filterAll: ['coo_doc', 'coo_sheet', 'coo_ppt', 'flow', 'mind', 'coo_file', 'coo_dir'],
  filterTenantAll: ['coo_doc', 'coo_sheet', 'coo_file', 'coo_dir'],
}


const CooperShareFromMe = () => {
  const { needShareFromMeSkeleton, IsExternalTenant } = useSelector((state) => state.GlobalData);
  const { setNeedShareFromMeSkeleton } = useDispatch().GlobalData;

  const [sortedInfo, setSortedInfo] = useState({
    sortBy: 'time',
    orderAsc: 0,
  });
  const [batchMode, setBatchMode] = useState(false);
  const [filterMsg, setFilterMsg] = useState({
    type: 'all',
  });
  const [loading, setLoading] = useState(true);
  // const [hasMore, setHasMore] = useState(true);
  // const [list, setList] = useState([]);
  const [pageNumber, setPageNumber] = useState(0);

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { shareLists, hasMore } = useSelector((state) => state.ShareFromMe);
  const { getShareFromMeList, updateShareFromeMeList } = dispatch.ShareFromMe;

  useEffect(() => {
    getShareFromFile(true);
  }, [sortedInfo.orderAsc, sortedInfo.sortBy, filterMsg]);

  // useEffect(() => {
  //   if (JSON.stringify(userViewData) !== '{}') {
  //     const { SHARE_FORM_ME_FILE = {}} = userViewData;
  //     const { sort: sortLocal } = SHARE_FORM_ME_FILE;
  //     const FoldTreeConfig = sortLocal ?? SHARE_FILE_SORT;
  //     setSortedInfo(FoldTreeConfig)
  //   }
  // }, [userViewData]);

  const getShareFromFile = async (refresh = false) => {
    const pageNum = refresh ? 0 : pageNumber;
    const { type } = filterMsg;
    // let currentData = refresh ? [] : list;

    if (sortedInfo.sortBy === undefined || sortedInfo.sortBy === undefined) return;
    await getShareFromMeList({
      ...sortedInfo,
      pageNum,
      pageSize: PAGE_SIZE,
      refresh,
      types: type,
    });
    setNeedShareFromMeSkeleton(false);
    setPageNumber(pageNum + 1);
    setLoading(false);
  }

  const clickCheckbox = (idx) => {
    const cp = deepCopy(shareLists);
    cp[idx].checked = !cp[idx].checked;
    updateShareFromeMeList(cp);
  };

  const handleOpen = (idx) => {
    const cp = deepCopy(shareLists); // 协作文件，直接跳转到编辑
    window.__OmegaEvent('ep_sharebyme_location_ck');

    if (cp[idx].file.type === Coop) {
      noopenerOpen(pathUtil.getCoopPath(cp[idx].file.id, cp[idx].file.mime_type));
      window.__OmegaEvent('ep_ishare_name_ck', '', {
        resourceid: cp[idx].file.id,
        resource_id: cp[idx].file.id,
        position: idx,
      });
      return;
    }

    // didoc 文档
    if (cp[idx].file.type === DiDoc) {
      noopenerOpen(pathUtil.getDiDocPath(cp[idx].file.id));
      window.__OmegaEvent('ep_ishare_name_ck', '', {
        resourceid: cp[idx].file.id,
        resource_id: cp[idx].file.id,
        position: idx,
      });
      return;
    }

    // drawio流程图
    if (cp[idx].file.type === FlowChart) {
      pathUtil.getFlowChart(cp[idx].file.id)
        .then((url) => {
          noopenerOpen(url);
          window.__OmegaEvent('ep_ishare_name_ck', '', {
            resourceid: cp[idx].file.id,
            resource_id: cp[idx].file.id,
            position: idx,
          });
        });
      return;
    }

    // processon
    if (cp[idx].file.type === POFile || [10, 11].includes(cp[idx].file.mime_type)) {
      noopenerOpen(pathUtil.getCoopPath(cp[idx].file.id, cp[idx].file.mime_type));
      window.__OmegaEvent('ep_ishare_name_ck', '', {
        resourceid: cp[idx].file.id,
        resource_id: cp[idx].file.id,
        position: idx,
      });
      return;
    }
    // 知识库页面
    if (cp[idx].file.type === DK_PAGE) {
      noopenerOpen(pathUtil.getDkPageUrl(cp[idx].file.spaceId, cp[idx].file.resource_id));
      return;
    }

    if (cp[idx].shares.length === 0) {
      get(CooperApi.SHARE_DETAILE.replace(':resourceId', cp[idx].file.id)).then((data = []) => {
        const sl = data.map((s) => {
          const s1 = {
            ...s,
            editable: false,
            id: s.share_id,
            type:
              s.share_type === 1 ? intl.t('定向分享') : intl.t('全员分享'),
            link: s.uri ? `${window.location.origin}${pathUtil.resolve(s.uri)}` : '',
            expireType: String(s.expiration),
            expireTime:
              s.expiration <= 1
                ? intl.t('永久有效')
                : formatTime(s.expire_time),
            hasPassword: s.has_password,
            password: s.password,
            downCount: s.downloads,
            create_time: s.create_time,
            createTime: formatRecentTime(Date.parse(s.create_time)),
            shareTo: s.share_with_cn,
          }; // 定向分享取 permission 字段

          if (s1.type === intl.t('定向分享')) s1.permis = s.permission;
          // 全员分享取 read_only 字段
          else s1.permis = s.read_only ? 1 : 33;
          return s1;
        });
        cp[idx].shares = sl;
        cp[idx].open = !cp[idx].open;
        updateShareFromeMeList(cp);
      });
    } else {
      cp[idx].open = !cp[idx].open;
      updateShareFromeMeList(cp);
    }

    window.__OmegaEvent('ep_ishare_name_ck', '', {
      platform: 'new',
    });
  };

  const clickEdit = (fidx, sidx) => {
    const cp = deepCopy(shareLists);
    cp[fidx].shares[sidx].editable = true;
    updateShareFromeMeList(cp);
  };

  const editOkay = (fidx, sidx, permis, expire, password) => {
    const cp = deepCopy(shareLists);
    const s = cp[fidx].shares[sidx];
    const d = {
      share_id: s.id,
      expiration: Number(expire),
      read_only: permis === 1,
      permission: permis,
    };
    if (password) d.password = password;
    let promise = Promise.resolve(0);
    if (s.type === intl.t('全员分享')) promise = put(CooperApi.ADAPTER_SHARE_LINK, d);
    else {
      promise = put(CooperApi.DIRECT_PERMISSIONS, {
        share_id: s.id,
        permissions: permis,
      });
    }
    promise.then(() => {
      message.success(intl.t('修改成功'));
      s.editable = false;
      s.permis = permis;
      s.expireType = expire;
      s.expireTime = addTime(expire);
      s.password = password;
      updateShareFromeMeList(cp);
    });
  };

  const editCancel = (fidx, sidx) => {
    const cp = deepCopy(shareLists);
    cp[fidx].shares[sidx].editable = false;
    updateShareFromeMeList(cp);
  };


  const clickDelete = (e, s) => {
    e.stopPropagation();
    if (s.id >= 0) {
      CancelShare({
        shareId: s.id,
        shareType: s.share_type,
        runCallbacks: () => {
          const cp = deepCopy(shareLists);

          for (let i = 0; i < cp.length; i++) {
            const idx = cp[i].shares.findIndex((item) => item.id === s.id);

            if (idx !== -1) {
              cp[i].file.shareCount--;
              const d = cp[i].shares.splice(idx, 1); // 更新下载次数

              cp[i].file.downCount -= d[0].downCount; // 没有分享记录时，不显示该文件

              if (cp[i].shares.length === 0) {
                cp.splice(i, 1);
              }

              break;
            }
          }
          updateShareFromeMeList(cp);
        },
      });
    } else {
      message.error('数据异常，不可取消')
    }
  };


  const clickBatchDelete = () => {
    CancelShare(-1);
  };


  const toggleSort = async (s, oder) => {
    setSortedInfo({ sortBy: s, orderAsc: oder })
    let document = window.document.getElementById('share-form-me-scroll');
    document.scrollTop = 0;
  };
  const updateFilteredMsg = (key, value) => {
    let temp = { ...filterMsg, [key]: key === 'type' ? value.toString() : value };
    setFilterMsg(temp);
    return temp;
  }
  const doFilters = () => {
    let document = window.document.getElementById('share-form-me-scroll');
    document.scrollTop = 0;
  }
  // 判断当前排序方式
  const getSorterStatus = useCallback(
    (itemSortBy, itemOrderAsc) => {
      return (
        sortedInfo.sortBy === itemSortBy
        && sortedInfo.orderAsc === itemOrderAsc
      );
    },
    [sortedInfo.sortBy, sortedInfo.orderAsc],
  );

  const getHasCooper = () => {
    let hasCooper = false;

    for (let i = 0; i < selectedList.length; i++) {
      const item = selectedList[i];
      if (item.type === 2) {
        hasCooper = true;
        break;
      }
    }
    return hasCooper;
  };

  const setMultiOperate = () => {
    setBatchMode(true);
  }


  const selectedList = shareLists.filter((sh) => sh.checked && sh.type !== 2 && sh.type !== 3);
  const checkedCount = selectedList.length;
  const indeterminate = checkedCount > 0 && checkedCount < shareLists.length;
  const allChecked = checkedCount
    && checkedCount
    === shareLists.filter((v) => v.type !== 2 && v.type !== 3).length;
  const batches = [
    {
      name: intl.t('取消分享'),
      callback: clickBatchDelete,
    },
  ];

  const _topContent = (s) => (
    <>
      {s.type === intl.t('全员分享') && <div className='download-count'>
        {intl.t('下载次数')}：
        {s.downCount + intl.t('次')}
      </div>}
      <div className='create-time'>
        {intl.t('分享时间')}：
        {formatRecentTime(s.create_time)}
      </div>
    </>
  );

  const _bottomContent = (s, cp, k, k1) => {
    return <div className='link-opt'>
      {
        s.type === intl.t('全员分享')
        && <Tooltip title={intl.t('复制链接')}>
          <CopyToClipboard
            text={cp}
            onCopy={() => message.success(intl.t('复制成功'))}
          >
            <img src={require('./icon/copy.svg')} />
          </CopyToClipboard>
        </Tooltip>
      }
      <Tooltip title={intl.t('取消分享')}>
        <img
          src={require('./icon/unshare.svg')}
          onClick={(e) => clickDelete(e, s)}
        />
      </Tooltip>
      <Tooltip title={intl.t('修改分享')}>
        <img
          src={require('./icon/edit.svg')}
          onClick={() => clickEdit(k, k1)}
        />
      </Tooltip>
    </div>
  }

  // 只会跳转到个人空间或者团队空间
  const handlePathClick = (e, f) => {
    e.stopPropagation()
    const params = {
      resourceTypeStr: f.resourceTypeStr,
      teamId: f.share_to_type === 'PERSONAL_SPACE' ? null : f.space_id,
      id: f.parent_id ? f.parent_id : 0,
      deleted: f.deleted,
    }
    goToPath(params, '', navigate);
  };

  const optionEnhance = (options) => {
    // 判断是否为多租户，否直接返回入参。是经过处理
    if (IsExternalTenant) {
      const exceptList = ['coo_ppt', 'wiki', 'flow', 'mind'];
      return options.filter((v) => !exceptList.includes(v.value));
    }
    return options;
  };

  return (
    <>
      {needShareFromMeSkeleton ? (
        <>
          <div dangerouslySetInnerHTML={{ __html: GetHtml(FileTableSkeleton) }} />
        </>
      )
        : <div className='cooper-share-from-me folder-tree list-view'>
          <div className={cls('body', { 'is-dc': isDC() })}>
            <div className='tb-header'>
              <div className='tb-header-div'>
                {batchMode && <div className='file-checkbox' />}
                <div className='file-name li-block-item'>
                  {/* <div
                    className='name-sort'
                    style={{ cursor: 'pointer' }}
                    onClick={() => {
                      toggleSort('display_name', sortedInfo.orderAsc === 1 ? 0 : 1);
                    }}
                  >
                    {checkedCount > 0
                      ? intl.t('已选中{one}个文件/文件夹', {
                        one: checkedCount,
                      })
                      : intl.t('名称')}
                    <SortIcon
                      iconUp={getSorterStatus('display_name', 1)}
                      iconDown={getSorterStatus('display_name', 0)}
                      // sort={(value) => toggleSort('display_name', Number(value))}
                    />
                  </div> */}
                  <SearchFileType
                    filterMsg= {filterMsg.type.split(',')}
                    valueMap = { optionEnhance(filterCof) }
                    filterInfo={ filterInfo }
                    updateFilteredMsg={updateFilteredMsg}
                    doFilters={doFilters}
                  />
                </div>
                <div className='file-address li-block-item'>{intl.t('文件位置')}</div>
                <div
                  className='file-time li-block-item'
                  style={{
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    toggleSort('time', sortedInfo.orderAsc === 1 ? 0 : 1);
                  }}
                >
                  {intl.t('分享时间')}
                  <SortIcon
                    iconUp={getSorterStatus('time', 1)}
                    iconDown={getSorterStatus('time', 0)}
                    // sort={(value) => toggleSort('time', Number(value))}
                  />
                </div>
                {/* <div className='file-count li-block-item'>{intl.t('下载次数')}</div> */}
                <div className={'file-operate li-block-item'}>
                  {intl.t('操作')}
                </div>

              </div>
            </div>
            {
              !loading && !shareLists.length ? <ErrorTips
                title={intl.t('暂无分享内容')}
                img={
                  'http://img-ys011.didistatic.com/static/cooper_cn/noSearchResult.png'
                }
              /> : <div
                className='tb-body share-form-me-scroll os-scrollbar os-scrollbar-width'
                id='share-form-me-scroll'>
                <InfiniteScroll
                  initialLoad={false}
                  pageStart={0}
                  loadMore={() => getShareFromFile(false)}
                  hasMore={!loading && hasMore}
                  useWindow={false}
                  getScrollParent={() => document.querySelector('.share-form-me-scroll')}
                >
                  {shareLists.map((sh, k) => {
                    const s = sh.file;
                    // 收藏文件没有permission字段，由于文件都属于自己，权限默认最高，赋值1放过收藏校验
                    const f = { ...s, permission: 1 };
                    return (
                      <div
                        key={k}
                        className={sh.open ? 'open' : ''}>
                        <li
                          className='overview tb-body-row'
                          key={k}
                          onClick={() => handleOpen(k)}
                        >
                          {batchMode && <div className='file-checkbox li-block-item'>
                            {
                              <Checkbox
                                checked={sh.checked}
                                disabled={sh.type === 2 || sh.type === 3 || sh.type === 4}
                                onClick={(e) => e.stopPropagation()}
                                onChange={() => clickCheckbox(k)}
                              />
                            }
                          </div>}
                          <div className='file-name li-block-item'>
                            <ImageEnlarger
                              src={setImgUrl(f)}
                              isTiny={!!f.tiny}
                              mimeType={f.mime_type || f.mimeType}
                            />
                            <FileEllipsis
                              value={f.name}
                              resourceType={f.resourceTypeStr}
                              doneCallback={() => getShareFromFile(true)}
                              record={f}
                              originFileType={SHARE_FROM_ME}
                            />
                          </div>

                          <div
                            className='file-address li-block-item'
                          // onClick={(e) => {
                          //   handlePathClick(e, f);
                          // }}
                          >
                            <Tooltip
                              title={f.file_path}
                              placement='top'
                            >
                              <span className='file-address-item'>
                                {f.file_path}
                              </span>
                            </Tooltip>
                          </div>
                          <div className='file-time li-block-item'>
                            {formatRecentTime(Date.parse(f.create_time))}
                          </div>
                          {/* <div className='file-count li-block-item'>
                            <span className='file-count-span'>{f.downCount + intl.t('次')}</span>
                          </div> */}
                          <div className='file-operate li-block-item'>
                            <OperateMenu
                              key={new Date()}
                              file={{
                                ...s,
                                shareId: -1,
                                shareType: s.share_type,
                              }}
                              setBatchMode={setMultiOperate}
                              config={{
                                ...config,
                                cancelShareOperate: s.type !== 2 && s.type !== 3 && s.type !== 6 && s.type !== 7,
                              }}
                              doneCallback={() => getShareFromFile(true)}
                              originFileType={SHARE_FROM_ME}
                              isFlowChart={s.type === FlowChart && s.mime_type === 9}
                              location={SHARE_LIST_OPT}
                            />
                          </div>

                        </li>

                        <div className='detail'>
                          {sh.shares.map((s, k1) => {
                            let cp = `${intl.t('文件名称')}：${sh.file.name}\n`;
                            cp += s.password
                              ? `${intl.t('链接')}：${s.link} ${intl.t('密码')}：${s.password
                              }`
                              : s.link;
                            return (
                              <div key={k1}>
                                {s.type === intl.t('全员分享') && (
                                  <div className='link-summary'>
                                    <div className='link-top' >
                                      <div className='link-detail'>
                                        {intl.t('全员分享')}：
                                        <a href={s.link}>{s.link}</a>
                                        {s.password && (
                                          <span className='password'>
                                            {`${intl.t('密码')}：${s.password}`}
                                          </span>
                                        )}
                                      </div>
                                      {_topContent(s)}
                                    </div>

                                    <div className='link-bottom'>
                                      {!s.editable ? (
                                        <div>
                                          <span className='bottom-text-item'>
                                            {intl.t('权限')}：
                                            <span>{intl.t('只查看')}</span>
                                            {s.permis === 33 ? (
                                              <span>{intl.t('可下载')}</span>
                                            ) : null}
                                          </span>
                                          <span className='bottom-text-item'>
                                            {intl.t('有效期')}：{s.expireTime}
                                          </span>
                                          <span className='bottom-text-item'>
                                            {intl.t('类型')}：
                                            {s.hasPassword
                                              ? intl.t('私密')
                                              : intl.t('公开')}
                                          </span>
                                        </div>
                                      ) : (
                                        <ShareEditor
                                          fidx={k}
                                          sidx={k1}
                                          permis={s.permis}
                                          expire={s.expireType}
                                          needPassword={s.hasPassword}
                                          onOkay={editOkay}
                                          onCancel={editCancel}
                                        />
                                      )}
                                      {_bottomContent(s, cp, k, k1)}
                                    </div>

                                  </div>
                                )}
                                {s.type === intl.t('定向分享') && (
                                  <div className='link-summary'>
                                    <div className='link-top'>
                                      <div>
                                        {intl.t('定向分享')}：{s.shareTo}
                                      </div>
                                      {_topContent(s)}
                                    </div>
                                    <div className='link-bottom'>
                                      {!s.editable ? (
                                        <div>
                                          <span>
                                            {intl.t('权限')}：
                                            <span className='perm-item'>{intl.t('只查看')}</span>
                                            {s.permis === 33 ? (
                                              <span className='perm-item'>{intl.t('可下载')}</span>
                                            ) : null}
                                          </span>
                                        </div>
                                      ) : (
                                        <ShareEditor
                                          fidx={k}
                                          sidx={k1}
                                          permis={s.permis}
                                          expire={s.expireType}
                                          needPassword={false}
                                          onOkay={editOkay}
                                          onCancel={editCancel}
                                        />
                                      )}
                                      {_bottomContent(s, cp, k, k1)}
                                    </div>
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    );
                  })}

                  <SpinRender loading={loading} />
                  {!hasMore && !loading && shareLists.length !== 0 && <NoMore />}
                </InfiniteScroll>
              </div>
            }

            {
              batchMode
              && <div className='batch-operate-main'>
                <BatchButton batches={batches} />
              </div>
            }
          </div>
        </div>}
    </>
  )
};
export default CooperShareFromMe;
